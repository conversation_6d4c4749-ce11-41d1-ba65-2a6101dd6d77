-- START NYM-2906
INSERT INTO `demo`.`system_setting` (`field`, `section`, `value`) VALUES ('automatic_statement_comment_attempt_1', 'billing', '');
INSERT INTO `demo`.`system_setting` (`field`, `section`, `value`) VALUES ('automatic_statement_comment_attempt_2', 'billing', '');
INSERT INTO `demo`.`system_setting` (`field`, `section`, `value`) VALUES ('automatic_statement_comment_attempt_3', 'billing', '');
-- END NYM-2906


-- START NYM-4156
ALTER TABLE `demo`.`transcription_detail_audit`
    ADD COLUMN `note_id` BIGINT NULL AFTER `prescription_id`,
    ADD COLUMN `subject` TEXT NULL AFTER `note_id`,
    ADD COLUMN `is_reviewed` TINYINT(1) NULL NOT NULL DEFAULT 0 AFTER `subject`,
    ADD COLUMN `is_archived` TINYINT(1) NOT NULL DEFAULT 0 AFTER `audio_time`;

ALTER TABLE `demo`.`transcription_detail`
    ADD COLUMN `note_id` BIGINT NULL AFTER `prescription_id`,
    ADD COLUMN `subject` TEXT NULL AFTER `note_id`,
    ADD COLUMN `is_reviewed` TINYINT(1) NULL NOT NULL DEFAULT 0 AFTER `subject`,
    ADD COLUMN `is_archived` TINYINT(1) NOT NULL DEFAULT 0 AFTER `audio_time`;
-- END NYM-4156
