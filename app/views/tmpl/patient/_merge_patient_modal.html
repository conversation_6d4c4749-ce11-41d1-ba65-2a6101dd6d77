<script type="text/ng-template" id="mergePatientModal.html">
  <form name="mergePatientForm" id="mergePatientForm" novalidate>
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">Merge Patient</h4>
      </div>
      
      <div class="modal-body">
        <div class="form-group">
          <p class="form-control-static m-0">Merge patient #{{ sourcePatientId }} into:</p>
        </div>  

        <div class="form-group">
          <label>Patient</label>
          <div class="search">
             <input type="text"
                    size="40"
                    class="form-control"
                    placeholder="Search Patient by Name, Id, SSN, or DOB (MMDDYYYY)"
                    uib-typeahead="patient as utilService.formatNameWithDOB(patient) for patient in patientService.getPatients($viewValue)"
                    typeahead-on-select="selectPatient($item)"
                    typeahead-min-length="2"
                    typeahead-wait-ms="500"
                    ng-change="deselectPatient()"
                    ng-model="mergeTargetPatient"
                    typeahead-template-url="patientWithDOBSearchTemplate.html"/>
          </div>
        </div>
 
        <div class="bg-white p-3 text-body">
          <strong class="text-danger">Warning:</strong> Clicking this button will deactivate the current patient and merge all associated documents, notes, payments, purchase orders, prescriptions, and critical messages into the selected patient.
        </div>
  
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-info" 
                ng-disabled="mergePatientForm.$invalid || !mergeTargetPatient.id"
                ng-click="confirmMergeAction()"><i class="fa fa-merge"></i>
          Merge
        </button>
      </div>
    </div>
  </form>
</script>