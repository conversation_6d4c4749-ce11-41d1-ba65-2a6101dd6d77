<div class="page page-forms-common">
  <!-- page header -->
  <div class="pageheader">
    <h2>{{page.subtitle}}</h2>
    <div id="header-alert-container" class="alert" style="display: none;"></div>
    <div class="page-bar">
      <ul class="page-breadcrumb">
        <li>
          <a branch-dropdown></a>
        </li>
        <li>
          <a ui-sref="app.patient.all">{{page.title}}</a>
        </li>
        <li>
          <a ui-sref="app.patient.notes">{{page.subtitle}}</a>
        </li>
      </ul>
      <div class="page-toolbar">
        <a href="javascript:" class="btn btn-lightred no-border" daterangepicker="rangeOptions" date-begin="startDate"
           date-end="endDate">
            <i class="fa fa-calendar"></i>&nbsp;&nbsp;
            <span>{{startDate}} - {{endDate}}</span>&nbsp;&nbsp;
            <i class="fa fa-angle-down"></i>
        </a>
      </div>
    </div>
  </div>
  <div id="filter-form" class="panel panel-primary mb-10">
    <div class="panel-body pt-10 pb-10">
      <div class="row">
        <div class="col-sm-4 form-group">
          <label for="attending-practitioner">{{'Filter by Attending '+utilService.practitionerNoun}}</label>
          <select id="attending-practitioner" chosen class="form-control input-sm chosen-select" ng-model="filter.userId"
                  ng-options="t.id as utilService.formatName(t, 'FMiLC') for t in treatingPractitioners">
            <option value="">All</option>
          </select>
        </div>
        <div class="col-sm-2 form-group">
          <label for="matches">Matches displayed</label>
          <input type="text" id="matches" class="form-control input-sm" ng-model="appointments.length" value="" disabled>
        </div>
        <div class="col-sm-2 col-sm-offset-4 text-right mt-20">
          <button class="btn btn-rounded btn-sm btn-default"
                  ng-click="search()">
            <i class="fa fa-search"></i>Search
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="tile">
    <div class="tile-body p-0">
      <div class="alt-table-responsive-tablet">
        <table id="" class="table table-condensed">
          <thead>
          <tr class="bg-primary">
              <th>Date &amp; Time</th>
              <th ng-show="aiNotesEnabled">AI Note</th>
              <th ng-click="clientSortBy('$age')">Notes Aging (Days)</th>
              <th>Patient Name</th>
              <th>{{utilService.prescriptionAbbreviation}} #</th>
              <th>Appointment Type</th>
              <th ng-click="clientSortBy('practitioner.lastName')">{{'Attending '+utilService.practitionerNoun}}</th>
              <th ng-click="clientSortBy('practitionerFour.lastName')">{{'Supervising '+utilService.practitionerNoun}}</th>
              <th>Provider 2</th>
              <th>Provider 3</th>
              <th>Branch</th>
          </tr>
          </thead>
          <tbody>
          <tr ng-repeat="appt in appointments | orderBy:clientSortColumn:reverseOrder" ng-show="appointments.length && !loading">
            <td class="w-110">
              {{ moment(appt.notesDto.startDateTime).format('L h:mm A') + ' ' + dateService.getLocalTimeZoneAbbreviation()
              }}
            </td>
            <td>
                <ai-note-button
                        ng-show="appt.notesDto.transcriptionDetailId"
                        patient-id="appt.patientId"
                        prescription-id="appt.prescriptionId"
                        enabled="aiNotesEnabled"
                        transcription-id="appt.notesDto.transcriptionDetailId">
                </ai-note-button>
            </td>
            <td class="w-40" >{{appt.$age}}</td>
            <td class="w-90 text-uppercase"><a ng-href="#/app/patient/profile/{{appt.patient.id}}?tab=notes" target="_blank">{{    utilService.formatName(appt.patient, "FL") }}</a></td>
            <td class="w-80 text-uppercase" ng-if="!appt.notesDto.prescriptionId && !appt.notesDto.prescriptionTwoId" ng-bind-html="'None'"></td>
            <td class="w-80 text-uppercase" ng-if="appt.notesDto.prescriptionId && !appt.notesDto.prescriptionTwoId" ng-bind-html="'#' + appt.notesDto.prescriptionId "></td>
            <td class="w-80 text-uppercase" ng-if="appt.notesDto.prescriptionId && appt.notesDto.prescriptionTwoId" ng-bind-html="'#' + appt.notesDto.prescriptionId + ', #' + appt.notesDto.prescriptionTwoId "></td>
            <td class="w-80 text-uppercase">{{appt.notesDto.appointmentType}}</td>
            <td class="w-110 text-uppercase" ng-bind-html="utilService.formatName(appt.practitioner, 'LFMiC')"></td>
            <td class="w-110 text-uppercase" ng-bind-html="utilService.formatName(appt.practitionerFour, 'LFMiC')"></td>
            <td class="w-90 text-uppercase" ng-bind-html="utilService.formatName(appt.practitionerTwo, 'LFMiC')"></td>
            <td class="w-90 text-uppercase" ng-bind-html="utilService.formatName(appt.practitionerThree, 'LFMiC')"></td>
            <td class="w-90 text-uppercase">{{appt.notesDto.branch}}</td>
          </tr>
          <tr ng-hide="appointments.length"><td colspan="10" class="bg-warning text-center" ng-hide="loading">No appointments found.</td></tr>
          <tr ng-show="loading">
              <td colspan="10" class="text-greensea text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
