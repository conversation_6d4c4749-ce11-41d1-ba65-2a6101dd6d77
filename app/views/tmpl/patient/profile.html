<div class="page">
    <div class="pageheader">
        <h2 ng-bind-html="utilService.formatName(patientService.patient, 'FL')"></h2>
        <!--span ng-bind-html="page.subtitle"></span--></h2>
        <div class="pageheader-button pull-right">
            <span class="btn btn-primary btn-sm btn-rounded" ng-click="toggleDisplay()">
                <span ng-if="!displayExpanded">
                   <i class="fa fa-angle-up"></i> Expand
                </span>
                <span ng-if="displayExpanded">
                    <i class="fa fa-angle-down"></i> Default
                </span>
            </span>
        </div>
        <div id="header-alert-container" class="alert" style="display: none;"></div>
        <div class="page-bar">
            <ul class="page-breadcrumb">
                <li>
                    <a branch-dropdown></a>
                </li>
                <li>
                    <a ui-sref="app.patient.all">{{page.title}}</a>
                </li>
                <li>
                    <a ui-sref="app.patient.profile">{{page.subtitle}}</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="pagecontent">
        <div class="row">
            <div class="col-lg-4" ng-show="!displayExpanded">
                <section class="tile tile-simple">
                    <div class="tile-widget p-30 text-center">
                        <div class="thumb thumb-xl">
                            <img patient-id="{{patientService.patient.id}}"
                                 id="patient-profile-photo"
                                 class="img-circle"
                                 profile-photo onerror="return false;"
                                 ng-click="uploadService.openPhotoUpload(patientService.patient.id, 'patient')"
                                 alt="">
                        </div>
                        <h4><strong>{{patientService.patient.firstName}}</strong> {{patientService.patient.lastName}}
                        </h4>
                        <h5 ng-show="patientService.patient.nickname">( Nickname: {{patientService.patient.nickname}}
                            )</h5>
                        <h5>{{"DOB: " + patientService.getPatientAgeYearsMonths()}}</h5>
                        <h5 ng-if="patientService.patient.primaryBranchId">
                            Primary Branch: {{patientService.patient.primaryBranch.name}}</h5>
                        <h5 ng-if="!patientService.patient.primaryBranchId"
                            style="color:red">Missing Patient Primary Branch Information</h5>
                        <h5 ng-if="patientService.patient.primaryPractitionerId">
                            {{"Primary " + utilService.practitionerNoun+ ": " +patientService.patient.primaryPractitioner.firstName + " " +
                            patientService.patient.primaryPractitioner.lastName}}</h5>
                        <div id="status_holder">
                            <div class="mt-10" ng-show="patientService.patient.deceased">
                                <button class="btn btn-rounded-20 btn-sm btn-warning" id="patient_deceased" disabled>
                                    ---DECEASED---
                                </button>
                            </div>
                        </div>
                        <div id="alert_icons_holder" class="mt-10">
                            <span class="mr-10">
                                <i class="fa fa-users"></i> G: <span id="counter0">{{userNotificationService.generalAlerts.length}}</span>
                            </span>
                            <span class="mr-10">
                                <i class="fa fa-money"></i> B: <span id="counter1">{{userNotificationService.billingAlerts.length}}</span>
                            </span>
                            <span class="mr-10">
                                <i class="fa fa-medkit"></i> C: <span id="counter2">{{userNotificationService.clinicalAlerts.length}}</span>
                            </span>
                        </div>
                        <div class="mt-10">
                            <button class="btn btn-sm btn-rounded btn-success"
                                    ng-click="sendMessage(patientService.patient)">
                                <i class="fa fa-envelope"></i>
                                Send Message
                            </button>
                            <button class="btn btn-primary btn-sm btn-rounded"
                                    ng-click="openTaskModal()">
                                <i class="fa fa-plus-square"></i> New Task
                            </button>
                            <audit-button entity="patient" ng-model="patientId" showlabel="true"></audit-button>
                        </div>
                        <div class="flex align-items-center mt-10">
                            <button class="btn bg-warning btn-sm btn-rounded"
                                    ng-click="quickOrder(patientService.patient.id)">
                                <i class="fa fa-bolt"></i> Quick Order
                            </button>
						    <button ng-if="hasPermission('merge_patient_view')" 
						            class="btn btn-info btn-sm btn-rounded ml-10"
						            ng-click="openMergePatientModal()">
						        <i class="fa fa-merge"></i> Merge Patient
						    </button>
                        </div>
                    </div>
                    <div class="tile-body bg-blue" style="padding:8px 15px ">
                        <ul style="list-style: none; padding:5px 5px">
                            <li>
                                <h5 style="display: inline;">Insurance Balance</h5>
                                <h5 id="insurance-balance" class="m-0 pull-right" style="display: inline-block;">
                                    {{claimService.insuranceBalance | currency: "$": 2}}</h5>
                            </li>
                            <li>
                                <h5 style="display: inline;">Patient Balance</h5>
                                <h5 id="total-balance" class="m-0 pull-right" style="display: inline-block;">
                                    {{claimService.patientBalance | currency: "$": 2}}</h5>
                            </li>
                            <li>
                                <h5 style="display: inline;">Total Remaining Balance</h5>
                                <h5 id="total-amount" class="m-0 pull-right" style="display: inline-block;">
                                    {{claimService.totalRemainingBalance | currency: "$": 2}}</h5>
                            </li>
                            <li>
                                <h5 style="display: inline;">Total Paid</h5>
                                <h5 id="total-paid" class="m-0 pull-right" style="display: inline-block;">
                                    {{claimService.totalPaid | currency: "$": 2}}</h5>
                            </li>
                            <li>
                                <h5 style="display:inline;">Patient Credit</h5>
                                <h5 class="m-0 pull-right">{{claimService.patientCredit | currency: "$": 2}}</h5>
                            </li>
                            <li>
                                <h5 style="display:inline;">Sent To Collections</h5>
                                <h5 class="m-0 pull-right">{{claimService.sentToCollections | currency: "$": 2}}</h5>
                            </li>
                        </ul>
                    </div>
                </section>
                <section class="tile tile-simple">
                    <div class="tile-header">
                        <h1 class="custom-font"><strong>Patient</strong> {{utilService.prescriptionNoun}}s</h1>
                        <i class="fa fa-spinner fa-spin" class="pull-right mt-5"
                           ng-show="prescriptionService.loading"></i>
                        <a role="button" id="new-prescription" class="btn btn-sm btn-rounded btn-primary pull-right"
                           style="margin-right:0px"
                           ng-click="prescriptionService.openPrescription(undefined, undefined)">New
                            {{utilService.prescriptionNoun}}</a>
                    </div>
                    <div class="tile-body mt-0">
                        <prescription-list ng-model="prescriptionService.prescriptions"></prescription-list>
                        <ng-include src="'views/tmpl/prescription/_add_update_modal_form.html'"></ng-include>
                        <div>
                            <label class="checkbox-inline checkbox-custom pt-25" ng-if="!patientService.loading && !prescriptionService.loading && prescriptionService.numberOfArchivedPrescriptions > 0">
                                <input type="checkbox" ng-model="prescriptionService.showArchivedItems"
                                       ng-click="init()">
                                <i></i>
                                <span class="ml-10">{{'Show Archived Prescriptions (' + prescriptionService.numberOfArchivedPrescriptions + ')'}}</span>
                            </label>
                        </div>
                        <div>
                            <label class="checkbox-inline checkbox-custom pt-25" ng-if="!patientService.loading && !prescriptionService.loading && prescriptionService.numberOfInactivePrescriptions > 0">
                                <input type="checkbox" ng-model="prescriptionService.showInactivePrescriptions"
                                       ng-click="init()">
                                <i></i>
                                <span class="ml-10">{{'Show Inactive Prescriptions (' + prescriptionService.numberOfInactivePrescriptions + ')'}}</span>
                            </label>
                        </div>
                        <button class="btn btn-sm btn-rounded btn-info" id="edit-prescription"
                                ng-click="patientService.openPickPatientPrimaryBranch(patientService.patient.id)"
                                ng-hide="patientService.patient.primaryBranchId!=null">
                            <i class="fa fa-edit"></i>
                            Pick Patient Primary Branch
                        </button>
                    </div>
                </section>
            </div>
            <div ng-class="{'col-lg-12' : displayExpanded, 'col-lg-8' : !displayExpanded}" style="padding-right: 0;">
                <div class="tile tile-simple">
                    <div class="tile-body p-0">
                        <div role="tabpanel">
                            <ul id="patient-tabs" class="nav nav-tabs tabs-dark" role="tablist"
                                ng-style="{'pointer-events': patientService.loading || prescriptionService.loading ? 'none' : '', 'opacity': patientService.loading || prescriptionService.loading ? '0.5' : '1.0'}">
                                <li role="presentation" id="the-tab-1" ng-click="openTab('patient_summary')"
                                    ng-class="activeTab('patient_summary')">
                                    <a data-target="#patient-summary" data-tab="patient-summary"
                                       role="tab" data-toggle="tab">Patient Summary</a>
                                </li>
                                <li role="presentation" id="the-tab-2" ng-click="openTab('personal_information')"><a
                                        data-target="#personal-information" data-tab="personal-information" role="tab"
                                        data-toggle="tab">Personal Information</a></li>
                                <li role="presentation" id="the-tab-3" ng-click="openTab('forms')"><a
                                        data-target="#esignature-forms" data-tab="esignature-forms" role="tab"
                                        data-toggle="tab">Forms</a></li>
                                <li role="presentation" id="the-tab-4" ng-click="openTab('contacts')"><a
                                        data-target="#contacts" data-tab="contacts" role="tab" data-toggle="tab">Contacts</a>
                                </li>
                                <li role="presentation" id="the-tab-5" ng-click="openTab('insurance')"><a
                                        data-target="#insurance" data-tab="insurance" role="tab" data-toggle="tab">Insurance</a>
                                </li>
                                <li role="presentation" id="the-tab-6" ng-click="openTab('medical_history')"><a
                                        data-target="#medical-history" data-tab="medical-history" role="tab"
                                        data-toggle="tab">Medical History</a></li>
                                <li role="presentation" id="the-tab-7" ng-click="openTab('work_in_progress')"
                                    ng-class="activeTab('work-in-progress')"><a
                                        data-target="#work-in-progress" data-tab="work-in-progress" role="tab"
                                        data-toggle="tab">Work in Progress</a></li>
                                <!--                                <li role="presentation" id="the-tab-8" ng-click="openTab('transaction_history')"-->
                                <!--                                    ng-class="activeTab('transaction-history')"><a-->
                                <!--                                        data-target="#transaction-history" data-tab="transaction-history" role="tab"-->
                                <!--                                        data-toggle="tab">Transaction History</a></li>-->
                                <li role="presentation" id="the-tab-8" ng-click="openTab('transaction_history')"
                                    ng-class="activeTab('transaction-history')"><a
                                        data-target="#transaction-history" data-tab="transaction-history" role="tab"
                                        data-toggle="tab">Transaction History</a></li>
                                <li role="presentation" id="the-tab-9" ng-click="openTab('notes')"
                                    ng-class="activeTab('notes')"><a
                                        data-target="#notes" data-tab="notes" role="tab" data-toggle="tab">Notes</a>
                                </li>
                                <li role="presentation" id="the-tab-10" ng-click="openTab('appointments')"><a
                                        data-target="#appointments" data-tab="appointments" role="tab"
                                        data-toggle="tab">Appointments</a></li>
                                <li role="presentation" id="the-tab-11" ng-click="openTab('files_and_documents')"><a
                                        data-target="#files-and-documents" data-tab="files-and-documents" role="tab"
                                        data-toggle="tab">Files and Documents</a></li>
                                <li role="presentation" id="the-tab-12" ng-click="openTab('purchasing')"><a
                                        data-target="#patient-purchasing" data-tab="patient-purchasing" role="tab"
                                        data-toggle="tab">Purchase Orders</a></li>
                                <li role="presentation" id="the-tab-13" ng-click="openTab('shopping_cart')"
                                    ng-if="hasPermission('shopping_cart_patient_chart_view')">
                                    <a data-target="#patient-cart" data-tab="patient-cart" role="tab"
                                       data-toggle="tab">Shopping Cart</a></li>
                                <li role="presentation" id="the-tab-14" ng-click="openTab('fabrication')"><a
                                        data-target="#fabrication" data-tab="fabrication" role="tab" data-toggle="tab">Custom
                                    Fabrication</a>
                                </li>
                                <li role="presentation" id="the-tab-15" ng-click="openTab('checklist')"><a
                                        data-target="#checklist" data-tab="checklist" role="tab" data-toggle="tab">Checklist</a>
                                </li>
                                <li role="presentation" id="the-tab-16" ng-click="openTab('outcome_measures')"><a
                                        data-target="#outcome-measures" data-tab="outcome-meausres" role="tab"
                                        data-toggle="tab">Outcome Measures</a></li>
                                <li role="presentation" id="the-tab-17" ng-click="openTab('fma')"
                                    ng-if="forbinService.forbinSystemSetting['is_forbin_user'] === 'Y'"><a
                                        data-target="#forbin" data-tab="forbin" role="tab" data-toggle="tab">FMA</a>
                                </li>
                                <li role="presentation" id="the-tab-18" ng-click="openTab('alerts')"><a
                                        data-target="#alerts" data-tab="alerts" role="tab" data-toggle="tab">Notifications</a>
                                </li>
                                <li role="presentation" id="the-tab-19" ng-click="openTab('tasks')">
                                    <a data-target="#tasks" data-tab="tasks" role="tab" data-toggle="tab">Tasks</a>
                                </li>
                                <li role="presentation" id="the-tab-20" ng-click="openTab('loaners')"
                                    ng-if="enableLoaners">
                                    <a data-target="#loaners" data-tab="loaners" role="tab"
                                       data-toggle="tab">Loaners and Rentals</a>
                                </li>
                                <li role="presentation" id="the-tab-21" ng-click="openTab('measurements')"
                                    ng-hide="prescriptionService.CRT_hideDeviceType">
                                    <a data-tab="measurements" data-target="#measurements" data-toggle="tab" role="tab">Measurements</a>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div role="tabpanel" ng-class="activeTabPane('patient_summary')" id="patient-summary" >
                                    <div class="wrap-reset" >
                                        <ng-include
                                                src="'views/tmpl/patient/personal_information/_patient_summary_form.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="personal-information">
                                    <div class="wrap-reset"  ng-if="isActiveTab('personal_information')">
                                        <ng-include ng-if="!tabService.editingPersonalInfoTab"
                                                    src="'views/tmpl/patient/personal_information/_view_form.html'"></ng-include>
                                        <ng-include ng-if="tabService.editingPersonalInfoTab"
                                                    src="'views/tmpl/patient/personal_information/_update_form.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="esignature-forms" >
                                    <div class="wrap-reset" ng-if="isActiveTab('forms')">
                                        <ng-include src="'views/tmpl/patient/_esignature_forms.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="contacts" >
                                    <div class="wrap-reset" ng-if="isActiveTab('contacts')">
                                        <ng-include ng-if="!tabService.editingContactsTab"
                                                    src="'views/tmpl/patient/contacts/_view_form.html'"></ng-include>
                                        <ng-include ng-if="tabService.editingContactsTab"
                                                    src="'views/tmpl/patient/contacts/_update_form.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="insurance" >
                                    <div class="wrap-reset" ng-if="isActiveTab('insurance')">
                                        <ng-include src="'views/tmpl/patient/insurances/index.html'"></ng-include>
                                        <!--<ng-include ng-hide="tabService.editingInsurancesTab" src="'views/tmpl/patient/insurances/_view_form.html'"></ng-include>-->
                                        <!--<ng-include ng-show="tabService.editingInsurancesTab" src="'views/tmpl/patient/insurances/_update_form.html'"></ng-include>-->
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="medical-history" >
                                    <div class="wrap-reset" ng-if="isActiveTab('medical_history')">
                                        <ng-include
                                                src="'views/tmpl/patient/medical_histories/_form.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="work-in-progress"
                                     ng-class="activeTabPane('work-in-progress')" >
                                    <div class="wrap-reset" ng-if="isActiveTab('work_in_progress')">
                                        <ng-include src="'views/tmpl/patient/work_in_progress/index.html'"></ng-include>
                                    </div>
                                </div>
                                <!--                                <div role="tabpanel" class="tab-pane" id="transaction-history"-->
                                <!--                                     ng-class="activeTabPane('transaction-history')" >-->
                                <!--                                    <div class="wrap-reset" id="wrap-reset-transaction-history" ng-if="isActiveTab('transaction_history')">-->
                                <!--                                        <ng-include-->
                                <!--                                                src="'views/tmpl/patient/transaction_history/index.html'"></ng-include>-->
                                <!--                                    </div>-->
                                <!--                                </div>-->
                                <div role="tabpanel" class="tab-pane" id="transaction-history"
                                     ng-class="activeTabPane('transaction-history')" >
                                    <div class="wrap-reset" id="wrap-reset-transaction-history" ng-if="isActiveTab('transaction_history')">
                                        <ng-include
                                                src="'views/tmpl/patient/transaction_history/index_new.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" ng-class="activeTabPane('notes')" id="notes" >
                                    <div class="wrap-reset" id="wrap-reset-notes" ng-if="isActiveTab('notes')">
                                        <ng-include src="'views/tmpl/notes/index.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="appointments" >
                                    <div class="wrap-reset" id="wrap-reset-appointments" ng-if="isActiveTab('appointments')">
                                        <ng-include
                                                src="'views/tmpl/patient/appointments/_appointments.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" ng-class="activeTabPane('files_and_documents')"
                                     id="files-and-documents">
                                    <div class="wrap-reset" id="wrap-reset-files-and-documents"
                                         ng-if="isActiveTab('files_and_documents')">
                                        <ng-include src="'views/tmpl/files/index.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="patient-purchasing" >
                                    <div class="wrap-reset" id="wrap-reset-patient-purchasing" ng-if="isActiveTab('purchasing')">
                                        <ng-include src="'views/tmpl/patient/purchasing/_purchasing.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="patient-cart" >
                                    <div class="wrap-reset" id="wrap-reset-patient-cart" ng-if="isActiveTab('shopping_cart')">
                                        <ng-include src="'views/tmpl/patient/purchasing/_cart.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" id="fabrication" ng-class="activeTabPane('fabrication')" >
                                    <div class="wrap-reset" id="wrap-reset-fabrication" ng-if="isActiveTab('fabrication')">
                                        <ng-include
                                                src="'views/tmpl/patient/checklist/_fabrication.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" id="checklist" ng-class="activeTabPane('checklist')">
                                    <div class="wrap-reset" id="wrap-reset-checklist" ng-if="isActiveTab('checklist')">
                                        <ng-include
                                                src="'views/tmpl/patient/checklist/_checklist.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="outcome-measures" >
                                    <div class="wrap-reset" id="wrap-reset-outcome-measures" ng-if="isActiveTab('outcome_measures')">
                                        <ng-include
                                                src="'views/tmpl/patient/outcome-measures/outcome-measures.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="forbin" >
                                    <div class="wrap-reset" id="wrap-reset-forbin" ng-if="isActiveTab('forbin')">
                                        <ng-include src="'views/tmpl/patient/forbin/index.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="alerts">
                                    <div class="wrap-reset" id="wrap-reset-alerts" ng-if="isActiveTab('alerts')">
                                        <ng-include src="'views/tmpl/patient/alerts/index.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="tasks">
                                    <div class="wrap-reset" id="wrap-reset-tasks" ng-if="isActiveTab('tasks')">
                                        <ng-include src="'views/tmpl/patient/tasks/index.html'"></ng-include>
                                    </div>
                                </div>
                                <div role="tabpanel" class="tab-pane" id="loaners"
                                     ng-if="enableLoaners">
                                    <div class="wrap-reset" id="wrap-reset-loaners" ng-if="isActiveTab('loaners')">
                                        <ng-include src="'views/tmpl/patient/loaners/index.html'"></ng-include>
                                    </div>
                                </div>
                                <div class="tab-pane" id="measurements" ng-hide="prescriptionService.CRT_hideDeviceType"
                                     role="tabpanel">
                                    <div class="wrap-reset"
                                         id="wrap-reset-measurements"
                                         ng-if="isActiveTab('measurements')">
                                        <ng-include
                                                src="'views/tmpl/patient/measurements/_measurements.html'"></ng-include>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<ng-include src="'views/tmpl/patient/_assign_user_modal.html'"></ng-include>
<ng-include src="'views/tmpl/patient/_edit_rental_modal.html'"></ng-include>
<ng-include src="'views/tmpl/patient/_merge_patient_modal.html'"></ng-include>
