<div class="modal-content" modal-movable>
    <div ng-if="!loading">
        <div class="modal-header bg-{{noteService.noteTypes[note.noteType]['color']}}">
            <button type="button" class="close" ng-click="$dismiss()">&times;</button>
            <h4 class="modal-title">
                {{utilService.formatName(note.patient, "FL")}}
            </h4>
        </div>
        <div class="modal-body">
            <form name="modalNoteForm" novalidate>
                <div class="row">
                    <div class="col-sm-12 form-group"
                         ng-if="noteService.subjectRequired"
                         ng-class="{'has-error' : isSubmitted && modalNoteForm.subject.$invalid ,
                                    'has-success' : isSubmitted && modalNoteForm.subject.$valid}">
                        <label for="subject">Subject</label>
                        <input type="text"
                               id="subject"
                               name="subject"
                               class="form-control input-sm"
                               required
                               maxlength="100"
                               ng-disabled="disableEditingNote"
                               ng-model="note.subject">
                        <div ng-messages="modalNoteForm.subject.$error" role="alert" ng-show="isSubmitted">
                            <div class="help-block" ng-message="required">Subject is required.</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" id="note-type-field-div-{{note.prescriptionId}}">
                        <div class="form-group">
                            <div id="note-type-class-handler-{{prescription.id}}" class="select">
                                <label>Note Type</label>
                                <select ng-model="note.noteType"
                                        ng-disabled="note.published || isClinicalCorrectionAndUserIsNotSuperAdmin"
                                        chosen
                                        required
                                        class="form-control input-sm chosen-select">
                                    <option value="">Select Note Type</option>
                                    <option value="general">General</option>
                                    <option value="clinical"
                                            ng-if="userService.isClinical() || userService.isCareExtender() || userService.isSuperAdmin()">
                                        Clinical
                                    </option>
                                    <option value="billing" ng-if="userService.isBilling()">Billing</option>
                                    <option ng-if="userService.isBilling()" value="billing_ar">Billing AR</option>
                                    <option value="patient_summary"
                                            ng-if="note.prescriptionId != null && !prescription.$patientSummary">
                                        {{utilService.prescriptionAbbreviation}} Summary
                                    </option>
                                    <option value="complaint">Complaint</option>
                                    <option value="claim_comments">Claim Comment</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12" id="template-field-div-{{note.prescriptionId}}">
                        <div class="form-group">
                            <div id="template-class-handler-{{note.prescriptionId}}" class="select">
                                <label>Template</label>
                                <select name="template_{{note.prescriptionId}}"
                                        class="template_field form-control input-sm chosen-select"
                                        placeholder-text-single="'Select Template'"
                                        ng-model="noteService.noteTemplateId[note.prescriptionId]"
                                        ng-disabled="note.published"
                                        ng-change="changeTemplate(note.prescriptionId, patientId, noteService.noteTemplateId[note.prescriptionId])"
                                        ng-options="template.id as template.name for template in noteService.templates | orderBy: 'name'">
                                    <option value="">Select Template</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row" ng-if="!isPatientSummaryNote">
                    <div class="col-sm-12 form-group" id="appointment-field-div-{{note.prescriptionId}}">
                        <div id="appointment-class-handler-{{note.prescriptionId}}" class="select"
                             ng-class="{'has-error' : isSubmitted &&  displayAppointmentRequiredForClinicalNoteError,
                                    'has-success' : isSubmitted && !displayAppointmentRequiredForClinicalNoteError}">
                            <label for="appointment_{{note.prescriptionId}}">Appointment</label>
                            <select name="appointment_{{note.prescriptionId}}"
                                    id="appointment_{{note.prescriptionId}}"
                                    class="appointment_field form-control input-sm chosen-select"
                                    ng-change="selectAppointment(note); setDefaultNoteTreatingPractitioner()"
                                    ng-disabled="note.published && (note.noteType === 'clinical' || note.noteType === 'crm')"
                                    ng-options="appointment as (appointmentService.getFormattedAppointmentDate(appointment) + (appointment.prescriptionId ? (' ' + utilService.prescriptionAbbreviation + ' #' + appointment.prescriptionId) : '') + (appointment.$hasNote ? ' **Attached to Note' : '')) disable when (!appointment.patientId || moment(appointment.startDateTime).isAfter(moment(), 'day')) for appointment in appointments | filter:noteService.showAppointmentForNote(note, note.prescription) | orderBy:['-$hasNote','-startDateTime'] track by appointment.id"
                                    ng-model="note.appointment">
                                <option value="">Select Appointment</option>
                            </select>
                            <div role="alert" ng-show="isSubmitted && displayAppointmentRequiredForClinicalNoteError">
                                <div class="help-block">Appointment is required to publish a clinical note</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row" ng-if="(note.noteType === 'clinical' || note.noteType === 'crm')">
                    <div class="col-sm-12 form-group" id="treating-practitioner-field-div-{{note.prescriptionId}}">
                        <div id="treating-practitioner-class-handler-{{note.prescriptionId}}" class="select">
                            <label for="treating_practitioner_{{note.prescriptionId}}">{{'Treating ' + utilService.practitionerNoun}}</label>
                            <select name="treating_practitioner_{{note.prescriptionId}}"
                                    id="treating_practitioner_{{note.prescriptionId}}"
                                    class="treating_practitioner_field form-control input-sm chosen-select"
                                    ng-disabled="note.published || isClinicalCorrectionAndUserIsNotSuperAdmin"
                                    ng-options="user.id as user.firstAndLastName for user in validTreatingPractitioners"
                                    ng-model="note.treatingPractitionerId">
                                <option value="">{{'Select Treating ' + utilService.practitionerNoun}}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <summernote
                        class="note_field form-control input-sm no-resize"
                        required
                        id="modalNote"
                        name="modalNote"
                        height="309"
                        color="#fff"
                        editor="editor"
                        config="summernoteOptions"
                        ng-model="note.note">
                </summernote>
                <p ng-if="isSubmitted && !note.note" class="help-block" style="color: red;">
                    Note Body is required
                </p>
                <div class="row">
                    <div class="pull-left col-sm-3" ng-if="note.userSignedAt != null">
                        <p>Save & Signed on {{ moment(note.userSignedAt).format('L') }}</p>
                    </div>
                    <div class="pull-right"
                         ng-hide="noteService.isValidTreatingOrSupervisingPractitioner(note) || note.prescriptionId == 0">
                        <label class="checkbox-inline checkbox-custom ml-5 ">
                            <input type="checkbox" name="cosigned"
                                   ng-checked="note.cosigned"
                                   disabled
                                   ng-model="note.cosigned">
                            <i></i>
                            Note Approved
                        </label>
                        <i class="fa fa-info-circle text-info" uib-popover-template="'noteApprovalTooltip.html'"
                           popover-trigger="mouseenter"></i>
                    </div>
                </div>
            </form>
            <div ng-if="isPatientSummaryNote">
                <hr class="col-sm-12" style="background-color: lightgrey">
                <div class="row" ng-repeat="rxNote in prescriptionSummaryNotes | orderBy : '-createdAt'">
                    <div class="col-sm-11">
                        <label ng-style="{'color': (rxNote.id === note.id) ? 'green' : 'black'}"><b>
                            {{rxNote.createdAt | date: "short"}} by {{rxNote.user.firstName}}
                            {{rxNote.user.lastName}}
                        </b></label>
                        <p><b>{{ rxNote.subject }}</b></p>
                        <p ng-bind-html="rxNote.note"></p>
                    </div>
                    <div class="col-sm-1 pl-0">
                        <a role="button" class="action-link text-danger"
                           ng-if="userService.hasPermission('prescription_summary_comment_delete')"
                           ng-click="deleteNote(rxNote.id)">
                            <i class="fa fa-trash text-red pull-right"></i>
                        </a>
                    </div>
                    <hr class="col-sm-12" style="background-color: lightgrey">
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div ng-hide="note.published || !note.id || note.userSignedAt || note.transcriptionDetailId != undefined"
                 class="btn btn-danger btn-sm btn-rounded pull-left"
                 ng-click="action('delete', modalNoteForm.$valid)">
                Delete Draft
            </div>
            <button type="button" class="btn btn-success btn-sm btn-rounded pull-left"
                    ng-click="action('save', modalNoteForm.$valid)"
                    ng-hide="note.published
                    || !canEdit
                    || (note.userSignedAt && !noteService.isValidTreatingOrSupervisingPractitioner(note) && currentUserId !== note.appointment.userId)">
                Save Draft
            </button>
            <button type="button" class="btn btn-warning btn-sm btn-rounded pull-left"
                    ng-click="noteService.openV2AINote(note.transcriptionDetailId)"
                    ng-hide="note.transcriptionDetailId == undefined">
                View Original AI Transcript
            </button>
            <button type="button" class="btn btn-primary btn-sm btn-rounded"
                    ng-click="action('publish', modalNoteForm.$valid)"
                    ng-hide="(note.published && !isPatientSummaryNote)
                    || (note.noteType === 'clinical' && currentUserId !== note.treatingPractitionerId)
                    || !canEdit">
                Save & Publish
            </button>
            <button type="button" class="btn btn-primary btn-sm btn-rounded"
                    ng-click="action('sign', modalNoteForm.$valid)"
                    ng-show="!note.published && note.noteType === 'clinical' &&
                    !note.userSignedAt && noteService.isValidCareExtenderOrResident(note)">
                Save & Sign
            </button>
            <button type="button" class="btn btn-primary btn-sm btn-rounded"
                    ng-click="action('unsign', modalNoteForm.$valid)"
                    ng-if="!note.published && note.noteType === 'clinical' &&
                    note.userSignedAt && (noteService.isValidCareExtenderOrResident(note) || userService.isSuperAdmin())">
                Unsign
            </button>
            <button type="button" class="btn btn-primary btn-sm btn-rounded"
                    ng-if="note.published && (userService.isSuperAdmin() || (userService.isAdmin() && note.noteType !== 'clinical'))"
                    ng-disabled="note.published && note.noteType === 'crm'"
                    ng-click="action('unpublish', modalNoteForm.$valid)">
                Unpublish
            </button>
            <button type="button" class="btn btn-default btn-sm btn-rounded"
                    ng-click="$dismiss(); action('close', modalNoteForm.$valid)">
                Close
            </button>
        </div>
    </div>
    <div ng-if="loading">
        <div class="row text-center">
            <i class="fa fa-spinner fa-spin fa-5x" ng-style="{ 'color': '#16A085' }"></i>
        </div>
    </div>
</div>

<script type="text/ng-template" id="noteApprovalTooltip.html">
    <div style="width: 250px; word-wrap: break-word;">
        <span>The following conditions are required for a resident/care extender to send a note approval notification to the treating practitioner:</span>
        <ul>
            <li>Current user is a resident/ care extender</li>
            <li>Current user is the care extender on the prescription ({{utilService.prescriptionAbbreviation}}
                details)
            </li>
            <li>Note type is clinical</li>
            <li>Note is saved and published</li>
            <li>Treating practitioner on the note must have note approval notifications turned on</li>
            <li>Note is not already cosigned</li>
        </ul>
    </div>
</script>
