<ng-include src="'views/tmpl/maintenance/_view_denied.html'"></ng-include>
<div class="page page-forms-common"
     ng-if="userService.hasInventory() && userService.hasPermission(page.view + '_view')">
    <!-- page header -->
    <div class="pageheader">
        <h2>{{page.title}} <span ng-bind-html="page.subtitle"></span></h2>
        <div class="alert" id="header-alert-container" style="display: none;"></div>
        <div class="page-bar">
            <ul class="page-breadcrumb">
                <li>
                    <a branch-dropdown></a>
                </li>
                <li>
                    <a ui-sref="app.inventory.all">{{page.title}}</a>
                </li>
                <li>
                    <a ui-sref="app.inventory.all">{{page.subtitle}}</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="panel panel-primary mb-10" id="filter-form">
        <div class="panel-body pt-10 pb-10">
            <div class="row">
                <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12 form-group">
                    <label for="search-term">Search by item</label>
                    <input class="form-control input-sm"
                           id="search-term"
                           ng-change="resetPagination()"
                           ng-disabled="loading || filter.needsReStock"
                           ng-model="filter.keyword"
                           type="text">
                </div>
                <div class="col-xl-2 col-lg-4 col-md-4 col-sm-8 form-group" title="Branch #{{filter.branchId}}">
                    <label for="branch">Filter by Branch</label>
                    <select chosen class="form-control input-sm chosen-select"
                            id="branch"
                            ng-change="resetPagination()"
                            ng-disabled="loading"
                            ng-model="filter.branchId"
                            ng-options="b.id as b.name for b in branchService.allUserBranches | orderBy: 'name'">
                        <option ng-if="branchService.enableFilterAll()" value="">All</option>
                    </select>
                </div>
                <div class="col-xl-2 col-lg-2 col-md-2 col-sm-4 form-group mt-25" title="Include all child branches">
                    <label class="checkbox-inline checkbox-custom">
                        <input ng-change="resetPagination()"
                                ng-disabled="loading"
                                ng-model="filter.useChildBranches"
                                type="checkbox"><i></i>
                        Child Branches
                    </label>
                </div>
                <div class="col-xl-1 col-lg-2 col-md-2 col-sm-6 form-group">
                    <label for="min">Min Quantity</label>
                    <input class="form-control input-sm"
                           id="min"
                           max="{{filter.max == 0 ? 0 : 99999}}"
                           min="0"
                           ng-change="minChanged()"
                           ng-disabled="loading || filter.needsReStock || filter.max == 0"
                           ng-model="filter.min"
                           type="number">
                </div>
                <div class="col-xl-1 col-lg-2 col-md-2 col-sm-6 form-group">
                    <label for="max">Max Quantity</label>
                    <input class="form-control input-sm"
                           id="max"
                           min="{{filter.min > 0 ? filter.min : 0}}"
                           ng-change="maxChanged()"
                           ng-disabled="loading || filter.needsReStock"
                           ng-model="filter.max"
                           type="number">
                </div>
                <div class="col-xl-1 col-lg-2 col-md-2 col-sm-6 form-group" title="Number of items to display per page">
                    <div class="select">
                        <label>Page Size</label>
                        <select chosen class="col-sm-10 form-control input-sm chosen-select"
                                ng-change="filter.displayPage = 1"
                                ng-disabled="loading"
                                ng-model="filter.pageSize"
                                ng-options="i as i for i in pageSizes">
                        </select>
                    </div>
                </div>
                <div class="col-xl-1 col-lg-2 col-md-2 col-sm-6 form-group" title="Count if Items on this page">
                    <label for="items-shown">Items Shown</label>
                    <input class="form-control input-sm" disabled id="items-shown" ng-model="inventoryItems.length" type="text">
                </div>
                <div class="col-xl-1 col-lg-2 col-md-2 col-sm-6 form-group" title="Total count of Items matching the search criteria">
                    <label for="matches">Total matches</label>
                    <input class="form-control input-sm" disabled id="matches" ng-model="filter.totalCount" type="text">
                </div>
            </div>
            <div class="row">
                <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 form-group">
                    <label for="search-term">Part Number</label>
                    <input class="form-control input-sm"
                           id="partNumber"
                           ng-change="resetPagination()"
                           ng-disabled="loading || filter.needsReStock"
                           ng-model="filter.partNumber"
                           type="text">
                </div>
                <div class="col-sm-2 form-group mt-25" title="Quantity on hand at or below Low Stock">
                    <label class="checkbox-inline checkbox-custom">
                        <input ng-change="stockCheckChanged()"
                               ng-disabled="loading || filter.needsReStock"
                               ng-model="filter.lowStock"
                               type="checkbox"><i></i> Low Stock
                    </label>
                </div>
                <div class="col-sm-2 form-group mt-25" title="Preferred Stock is defined">
                    <label class="checkbox-inline checkbox-custom">
                        <input ng-change="stockCheckChanged()"
                               ng-disabled="loading || filter.needsReStock"
                               ng-model="filter.preferredStock"
                               type="checkbox"><i></i> Preferred Stock
                    </label>
                </div>
                <div class="col-sm-2 form-group mt-25" title="Quantity on hand and being ordered at or below Low Stock, and Preferred Stock is defined">
                    <label class="checkbox-inline checkbox-custom">
                        <input ng-change="setNeedsReStock()"
                               ng-disabled="loading"
                               ng-model="filter.needsReStock"
                               type="checkbox"><i></i>
                        Needs Restock
                    </label>
                </div>
                <div class="col-sm-2 form-group mt-25" title="Show Inventory Items whose 'Low Stock'/'Preferred Stock' are not set">
                    <label class="checkbox-inline checkbox-custom">
                        <input ng-change="resetPagination()"
                               ng-disabled="loading || filter.needsReStock || filter.preferredStock || filter.lowStock"
                               ng-model="filter.includeUndefined"
                               type="checkbox"><i></i>
                        Include Undefined
                    </label>
                </div>
                <div class="col-sm-1 form-group mt-25">
                    <button class="btn btn-rounded btn-sm btn-default pull-right"
                            ng-click="search()"
                            ng-disabled="loading"
                            type="button">
                        <i class="fa fa-search"></i> Search
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="row mb-10">
        <div class="col-sm-5 mt-5">
            <span class="bg-warning p-5 text-strong">Low Stock</span>
            <span class="bg-danger p-5 text-strong">Out of Stock</span>
            <span class="bg-darkgray p-5 text-strong">Zero in Stock</span>
        </div>
        <div class="col-sm-7">
            <button class="btn btn-primary btn-rounded btn-sm ml-10 pull-right"
                    id="export-report"
                    ng-click="exportInventory()"
                    ng-disabled="loading"
                    ng-if="!exporting"
                    title="Export this report to Excel"
                    type="button">Export to Excel
            </button>
            <button class="btn btn-primary btn-rounded btn-sm ml-10 pull-right"
                    disabled
                    id="export-loading"
                    ng-if="exporting"
                    title="Export this report to Excel"
                    type="button"><i class='fa fa-spinner fa-spin fa-pulse'></i> Fetching data...
            </button>
            <button class="btn btn-primary btn-rounded btn-sm ml-10 pull-right"
                    ng-click="importUpdatedInventory()"
                    ng-disabled="loading"
                    title="Import previously exported Excel file with up to 1,000 Inventory Items"
                    type="button">
                <i class="fa fa-upload"></i> Import Inventory
            </button>
            <button class="btn btn-primary btn-rounded btn-sm pull-right" disabled
                    id="reorder-button"
                    ng-click="reOrderItems()"
                    title="Add selected items to the shopping cart."
                    type="button">
                <i class="fa fa-shopping-cart"></i> Re-Order Preferred Stock
            </button>
        </div>
    </div>
    <div ng-if="!loading">
        <div class="row">
            <div class="col-sm-12 text-center">
                <uib-pagination boundary-links="true"
                                class="m-0"
                                first-text="<<"
                                force-ellipses="true"
                                items-per-page="filter.pageSize"
                                last-text=">>"
                                max-size="10"
                                next-text=">"
                                ng-change="search()"
                                ng-if="totalPages > 1"
                                ng-model="filter.displayPage"
                                previous-text="<"
                                total-items="filter.totalCount">
                </uib-pagination>
            </div>
        </div>
        <div class="tile">
            <div class="tile-body p-0">
                <div class="alt-table-responsive" style="overflow: scroll;">
                    <table class="table table-condensed" id="inventory-list"
                           style="table-layout: fixed; min-width: 1450px;">
                        <thead>
                        <tr class="bg-primary">
                            <!--                            <th class="w-40">-->
                            <th>
                                <div class="input-group" style="line-height: 16px;">
                                    <input id="check-all"
                                           ng-click="toggleAllChecked()"
                                           ng-model="checkAll.value"
                                           type="checkbox">
                                </div>
                            </th>
                            <th ng-click="sortBy('ibm.name')">Item</th>
                            <th class="w-150" ng-click="sortBy('ibm.description')">Description</th>
                            <th class="w-150 text-right" ng-click="sortBy('ibv.sku')">SKU</th>
                            <th class="w-150 text-right" ng-click="sortBy('ibm.part_number')">Part Number</th>
                            <th ng-click="sortBy('v.name')">Vendor</th>
                            <th class="w-150" ng-click="sortBy('m.name')">Manufacturer</th>
                            <th ng-click="sortBy('b.name')">Branch</th>
                            <th>Category</th>
                            <th>Price</th>
                            <!-- <th ng-click="sortBy('quantity')" class="w-100">In Stock</th>-->
                            <th ng-click="sortBy('ii.quantity')">In Stock</th>
                            <!-- <th ng-click="sortBy('low_stock')" class="w-100">Low Stock</th>-->
                            <th ng-click="sortBy('ii.low_stock')">Low Stock</th>
                            <!-- <th ng-click="sortBy('preferred_stock')" class="w-100">Preferred Stock</th>-->
                            <th ng-click="sortBy('ii.preferred_stock')">Preferred Stock</th>
                            <th class="text-right text-nowrap">In Cart</th>
                            <th class="text-right">Ordered</th>
                            <th class="text-right">Back- ordered</th>
                            <th class="text-right">
                                <i class="fa fa-times" ng-click="cancelAll()" ng-show="bulkEditing"
                                   title="Cancel Edits"></i>
                                <i class="fa fa-save" ng-click="saveAll()" ng-show="bulkEditing" title="Save All"></i>
                                <i class="fa fa-pencil" ng-click="editAll()" ng-show="!bulkEditing"
                                   title="Edit All"></i>
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-id="inventoryItem.id"
                            ng-class="{'bg-warning' : inventoryItem.quantity > 0 && inventoryItem.quantity <= inventoryItem.lowStock, 'bg-danger' : inventoryItem.quantity === 0 && inventoryItem.lowStock > 0, 'bg-darkgray' : inventoryItem.quantity === 0}"
                            ng-repeat="(i, inventoryItem) in inventoryItems"
                            ng-show="inventoryItems.length > 0">
                            <td>
                                <div class="input-group">
                                    <input class="reorder-item mt-2"
                                           data-item-id="{{inventoryItem.id}}"
                                           ng-click="toggleChecked()"
                                           ng-disabled="inventoryItem.preferredStock === 0"
                                           type="checkbox">
                                </div>
                            </td>
                            <td ng-bind-html="inventoryItem.item.name" title="Item #{{inventoryItem.itemId}}"></td>
                            <td ng-bind-html="inventoryItem.item.description" title="Item #{{inventoryItem.itemId}}"></td>
                            <td class="text-right" ng-bind-html="inventoryItem.item.sku" title="Item #{{inventoryItem.itemId}}"></td>
                            <td class="text-right" ng-bind-html="inventoryItem.item.partNumber" title="Item #{{inventoryItem.itemId}}"></td>
                            <td ng-bind-html="inventoryItem.item.vendor.name" title="Vendor #{{inventoryItem.item.vendorId}}"></td>
                            <td ng-bind-html="inventoryItem.item.manufacturer.name" title="Vendor #{{inventoryItem.item.manufacturerId}}"></td>
                            <td ng-bind-html="branchService.getName(inventoryItem.branch, '')" title="Branch #{{inventoryItem.branchId}}"></td>
                            <td ng-bind-html="inventoryItem.item.lCodeCategory.category" title="LCodeCategory #{{inventoryItem.item.lCodeCategoryId}}"></td>
                            <td ng-bind-html="inventoryItem.item.price | currency:'$':2"></td>
                            <td title="InventoryItem #{{inventoryItem.id > 0 ? inventoryItem.id : 'Undefined'}}">
                                <input class="form-control input-sm"
                                       ng-change="editInventory(inventoryItem)"
                                       ng-disabled="!inventoryItem.$editing && !bulkEditing"
                                       ng-model="inventoryItem.quantity"
                                       type="number">
                            </td>
                            <td title="InventoryItem #{{inventoryItem.id > 0 ? inventoryItem.id : 'Undefined'}}">
                                <input class="form-control input-sm"
                                       ng-change="editInventory(inventoryItem)"
                                       ng-disabled="!inventoryItem.$editing && !bulkEditing"
                                       ng-model="inventoryItem.lowStock"
                                       type="number">
                            </td>
                            <td title="InventoryItem #{{inventoryItem.id > 0 ? inventoryItem.id : 'Undefined'}}">
                                <input class="form-control input-sm"
                                       ng-change="editInventory(inventoryItem)"
                                       ng-disabled="!inventoryItem.$editing && !bulkEditing"
                                       ng-model="inventoryItem.preferredStock"
                                       type="number">
                            </td>
                            <td class="text-right" ng-bind-html="inventoryItem.inCart" title="Reorder: {{inventoryItem.preferredStock > 0 ? (inventoryItem.preferredStock - inventoryItem.quantity - inventoryItem.inCart - inventoryItem.ordered) : 0}}"></td>
                            <td class="text-right" ng-bind-html="inventoryItem.ordered"></td>
                            <td class="text-right">{{inventoryItem.backordered}}</td>
                            <td class="text-right">
                                <a class="action-link text-danger text-strong text-uppercase text-xs m-0" href
                                   ng-click="cancel(i)"
                                   ng-show="inventoryItem.$editing && !bulkEditing"
                                   role="button" title="Cancel">
                                    <i class="fa fa-times fa-2x"></i>
                                </a>
                                <a class="action-link text-success text-strong text-uppercase text-xs m-0" href
                                   ng-click="inventoryItem.$editing = !inventoryItem.$editing"
                                   ng-show="!inventoryItem.$editing && !bulkEditing"
                                   role="button" title="Edit">
                                    <i class="fa fa-edit fa-2x"></i>
                                </a>
                                <a class="action-link text-success text-strong text-uppercase text-xs m-0" href
                                   ng-click="save(i)"
                                   ng-show="inventoryItem.$editing && !bulkEditing"
                                   role="button" title="Save">
                                    <i class="fa fa-save fa-2x"></i>
                                </a>
                            </td>
                        </tr>
                        <tr ng-hide="inventoryItems.length > 0">
                            <td class="bg-warning text-center" colspan="17" ng-show="!inventoryItems.length">
                                No items found
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 text-center">
                <uib-pagination boundary-links="true"
                                class="m-0"
                                first-text="<<"
                                force-ellipses="true"
                                items-per-page="filter.pageSize"
                                last-text=">>"
                                max-size="10"
                                next-text=">"
                                ng-change="search()"
                                ng-if="totalPages > 1"
                                ng-model="filter.displayPage"
                                previous-text="<"
                                total-items="filter.totalCount">
                </uib-pagination>
            </div>
        </div>
    </div>
    <div ng-if="loading">
        <div class="row text-center">
            <i class="fa fa-spinner fa-spin fa-5x" ng-style="{ 'color': '#16A085' }"></i>
        </div>
    </div>
</div>
