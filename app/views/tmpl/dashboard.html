<div class="page page-dashboard">
    <div class="pageheader">
        <h2>{{page.title}} <span ng-bind-html="page.subtitle"></span></h2>
        <div id="header-alert-container" class="alert" style="display: none;"></div>
        <div class="page-bar">
            <ul class="page-breadcrumb">
                <li>
                    <a branch-dropdown></a>
                </li>
                <li>
                    <a ui-sref="app.dashboard">{{page.title}}</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="row">
        <div class="card-container col-lg-6 col-sm-6 col-sm-12">
            <div class="card">
                <div class="front bg-lightred">
                    <div class="row">
                        <div class="col-xs-4">
                            <i class="fa fa-calendar fa-4x"></i>
                        </div>
                        <div class="col-xs-8">
                            <p class="text-elg text-strong mb-0" ng-bind-html="appointmentCount"></p>
                            <span>Appointments</span>
                        </div>
                    </div>
                </div>
                <div class="back bg-lightred">
                    <div class="row">
                        <div class="col-xs-4">
                            <a ui-sref="app.appointments.add">
                                <i class="fa fa-pencil-square-o fa-2x"></i>
                                Add Appointment</a>
                        </div>
                        <div class="col-xs-4">
                            <a ui-sref="app.calendar">
                                <i class="fa fa-calendar-o fa-2x"></i>
                                Calendar</a>
                        </div>
                        <div class="col-xs-4">
                            <a ui-sref="app.appointments.all">
                                <i class="fa fa-check-square-o fa-2x"></i>
                                Appointments</a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="card-container col-lg-6 col-sm-6 col-sm-12">

            <div class="card">
                <div class="front bg-greensea">
                    <div class="row">
                        <div class="col-xs-4">
                            <i class="fa fa-users fa-4x"></i>
                        </div>
                        <div class="col-xs-8">
                            <p class="text-elg text-strong mb-0" ng-bind-html="patientCount"></p>
                            <span>Active Patients</span>
                        </div>
                    </div>
                </div>
                <div class="back bg-greensea">
                    <div class="row">
                        <div class="col-xs-3">
                            <a ui-sref="app.patient.add">
                                <i class="fa fa-pencil-square-o fa-2x"></i>
                                {{ 'Menu.NEW_PATIENT' | translate }}</a>
                        </div>
                        <div class="col-xs-3">
                            <a ui-sref="app.patient.all">
                                <i class="fa fa-tasks fa-2x"></i>
                                {{ 'Menu.ALL_PATIENTS' | translate }}</a>
                        </div>
                        <div class="col-xs-3">
                            <a ui-sref="app.patient.summary">
                                <i class="fa fa-briefcase fa-2x"></i>
                                {{ 'Menu.PATIENT_SUMMARY' | translate }}</a>
                        </div>
                        <div class="col-xs-3">
                            <a href="#" ng-click="createPatientIntakeLink()">
                                <i class="fa fa-link fa-2x"></i>
                                Patient Intake Link</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6" ng-if="currentUserWidgets.includes('upcoming_appointments')">
            <section class="tile">
                <div class="tile-header bg-greensea dvd dvd-btm">
                    <h1 class="custom-font"><strong>Upcoming Appointments</strong></h1>
                </div>
                <div class="tile-widget bg-greensea">
                    <!--<flot dataset="zipDataset" options="zipOptions" height="250px"></flot>-->
                    <div style="height:250px;" ng-hide="upcomingAppointments.length">
                        You currently have no upcoming appointments.
                    </div>
                    <table class="table" ng-show="upcomingAppointments.length"
                           style="display: block; height:250px; overflow-y: scroll">
                        <thead>
                        <th>Patient</th>
                        <th>Type</th>
                        <th>Date</th>
                        <th>Time</th>
                        </thead>
                        <tbody>
                        <tr ng-repeat="appointment in upcomingAppointments">
                            <td><a ui-sref="app.patient.profile({patientId: appointment.patient.id})" target="_blank">{{
                                utilService.formatName(appointment.patient, "FL") }}</a></td>
                            <td>{{ appointment.appointmentDto.appointmentType }}</td>
                            <td>{{ moment(appointment.appointmentDto.startDateTime).format("L") }}</td>
                            <td>
                                <strong>{{ moment(appointment.appointmentDto.startDateTime).format('h:mm a')}}</strong>
                                -
                                {{moment(appointment.appointmentDto.endDateTime).format('h:mm a') + ' ' + dateService.getLocalTimeZoneAbbreviation()}}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </div>
        <div class="col-sm-6" ng-if="currentUserWidgets.includes('missing_notes')">
            <section class="tile" fullscreen="isFullscreen02">
                <div class="tile-header bg-lightred dvd dvd-btm">
                    <h1 class="custom-font"><strong>Missing Notes (30 oldest)</strong></h1>
                </div>
                <div class="tile-widget bg-lightred">
                    <div ng-if="!notesLoading">
                        <div style="height:250px;" ng-hide="missingNotes.length">You currently have no missing notes.</div>
                        <table class="table" ng-show="missingNotes.length"
                               style="display: block; height:250px; overflow-y: scroll">
                            <thead>
                            <th>Patient</th>
                            <th>{{utilService.prescriptionAbbreviation}} #</th>
                            <th>Type</th>
                            <th>Date</th>
                            <th>Note Aging</th>
                            <th ng-show="aiNotesEnabled">AI note</th>
                            </thead>
                            <tbody>
                            <tr ng-repeat="missingNote in missingNotes">
                                <td><a ng-href="#/app/patient/profile/{{missingNote.patient.id}}?tab=notes"
                                       target="_blank">{{utilService.formatName(missingNote.patient, "FL") }}</a></td>
                                <td ng-if="!missingNote.notesDto.prescriptionId && !missingNote.notesDto.prescriptionTwoId"
                                    ng-bind-html="'None'"></td>
                                <td ng-if="missingNote.notesDto.prescriptionId && !missingNote.notesDto.prescriptionTwoId"
                                    ng-bind-html="'#' + missingNote.notesDto.prescriptionId "></td>
                                <td ng-if="missingNote.notesDto.prescriptionId && missingNote.notesDto.prescriptionTwoId"
                                    ng-bind-html="'#' + missingNote.notesDto.prescriptionId + ', #' + missingNote.notesDto.prescriptionTwoId "></td>
                                <td>{{ missingNote.notesDto.appointmentType }}</td>
                                <td>{{ moment(missingNote.notesDto.startDateTime).format("L") }}</td>
                                <td align="center">
                                    {{moment().diff(moment(missingNote.notesDto.startDateTime).format('YYYY-MM-DD'), 'days')}}
                                </td>
                                <td>
                                    <ai-note-button
                                            ng-show="missingNote.notesDto.transcriptionDetailId"
                                            patient-id="missingNote.patientId"
                                            prescription-id="missingNote.prescriptionId"
                                            enabled="aiNotesEnabled"
                                            transcription-id="missingNote.notesDto.transcriptionDetailId">
                                    </ai-note-button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div ng-if="notesLoading">
                        <div class="row text-center">
                            <i class="fa fa-spinner fa-5x fa-spin"></i>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <div class="col-sm-6" ng-if="showWip && currentUserWidgets.includes('prescription_summary')">
            <section class="tile" fullscreen="isFullscreen02">
                <div class="tile-header bg-primary dvd dvd-btm">
                    <h1 class="custom-font"><strong>{{utilService.prescriptionNoun}} Summary</strong></h1>
                    <ul class="controls">
                        <li class="dropdown" uib-dropdown>
                            <a uib-dropdown-toggle class="dropdown-toggle settings">
                                <i class="fa fa-cog"></i>
                                <i class="fa fa-spinner fa-spin"></i>
                            </a>
                            <ul class="dropdown-menu pull-right with-arrow animated littleFadeInUp">
                                <li>
                                    <a href tile-control-fullscreen
                                       ng-click="getWips(0)">0-30</a>
                                </li>
                                <li>
                                    <a href tile-control-fullscreen
                                       ng-click="getWips(30)">30-60</a>
                                </li>
                                <li>
                                    <a href tile-control-fullscreen
                                       ng-click="getWips(60)">60-90</a>
                                </li>
                                <li>
                                    <a href tile-control-fullscreen
                                       ng-click="getWips(90)">90+</a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
                <div class="tile-widget bg-primary">
                    <div ng-if="!wipsLoading">
                        <div style="height:250px;" ng-hide="wips.length"> There are no incomplete WIPs for this time
                            range.
                        </div>
                        <table class="table" ng-show="wips.length"
                               style="display: block; height:250px; overflow-y: scroll">
                            <thead>
                            <th>Patient</th>
                            <th>Device Type</th>
                            <th>Created Date, {{utilService.prescriptionAbbreviation}} Aging</th>
                            <th>{{utilService.prescriptionAbbreviation}} Date</th>
                            <th>Est. Value</th>
                            <th>Incomplete Sections</th>
                            </thead>
                            <tbody>
                            <tr ng-repeat="dto in wips">
                                <td>
                                    <a ui-sref="app.patient.profile({patientId: dto.patient.id})" target="_blank">{{ utilService.formatName(dto.patient, "FL") }}</a>
                                </td>
                                <td>{{ dto.prescription.deviceType }}</td>
                                <td align="center">{{ moment(dto.prescription.createdAt).format("YYYY/MM/DD") + ', ' + moment().diff(moment(dto.prescription.createdAt).format("YYYY-MM-DD"), 'days') }}</td>
                                <td>{{ moment(dto.prescription.prescriptionDate).format("L") }}</td>
                                <td align="center">{{ '$' + dto.estimatedValue }}</td>
                                <td>
                                    <span ng-show="dto.prescriptionSections.includes('Prescription Details')">
                                        <a ng-href="#/app/patient/profile/{{dto.prescription.patientId}}?tab=work-in-progress&prescriptionId={{dto.prescription.id}}&section=prescription_details"
                                           target="_blank">PD |</a>
                                    </span>
                                    <span ng-show="dto.prescriptionSections.includes('Deductible Information')">
                                        <a ng-href="#/app/patient/profile/{{dto.prescription.patientId}}?tab=work-in-progress&prescriptionId={{dto.prescription.id}}&section=insurance_verification"
                                           target="_blank">DI |</a>
                                    </span>
                                    <span ng-show="dto.prescriptionSections.includes('Pre-Authorization')">
                                        <a ng-href="#/app/patient/profile/{{dto.prescription.patientId}}?tab=work-in-progress&prescriptionId={{dto.prescription.id}}&section=pre-authorization"
                                           target="_blank">PA |</a>
                                    </span>
                                    <span ng-show="dto.prescriptionSections.includes('HCPCS Selection')">
                                        <a ng-href="#/app/patient/profile/{{dto.prescription.patientId}}?tab=work-in-progress&prescriptionId={{dto.prescription.id}}&section=l_code_selection"
                                           target="_blank">HCPCS |</a>
                                    </span>
                                    <span ng-show="dto.prescriptionSections.includes('HCPCS Justification')">
                                        <a ng-href="#/app/patient/profile/{{dto.prescription.patientId}}?tab=work-in-progress&prescriptionId={{dto.prescription.id}}&section=l_code_justification"
                                           target="_blank">HCPCJ |</a>
                                    </span>
                                    <span ng-show="dto.prescriptionSections.includes('Standard Written Order')">
                                        <a ng-href="#/app/patient/profile/{{dto.prescription.patientId}}?tab=work-in-progress&prescriptionId={{dto.prescription.id}}&section=standard_written_order"
                                           target="_blank">SWO |</a>
                                    </span>
                                    <span ng-show="dto.prescriptionSections.includes('Covered Codes')">
                                        <a ng-href="#/app/patient/profile/{{dto.prescription.patientId}}?tab=work-in-progress&prescriptionId={{dto.prescription.id}}&section=covered_codes"
                                           target="_blank">CC |</a>
                                    </span>
                                    <span ng-show="dto.prescriptionSections.includes('Service Estimate')">
                                            <a ng-href="#/app/patient/profile/{{dto.prescription.patientId}}?tab=work-in-progress&prescriptionId={{dto.prescription.id}}&section=service_estimate"
                                               target="_blank">SE |</a>
                                    </span>
                                    <span ng-show="dto.prescriptionSections.includes('Financial Responsibility')">
                                            <a ng-href="#/app/patient/profile/{{dto.prescription.patientId}}?tab=work-in-progress&prescriptionId={{dto.prescription.id}}&section=financial_responsibility"
                                               target="_blank">FR |</a>
                                    </span>
                                    <span ng-show="dto.prescriptionSections.includes('Proof of Delivery')">
                                            <a ng-href="#/app/patient/profile/{{dto.prescription.patientId}}?tab=work-in-progress&prescriptionId={{dto.prescription.id}}&section=proof_of_delivery"
                                               target="_blank">POD |</a>
                                    </span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div ng-if="wipsLoading">
                        <div class="row text-center">
                            <i class="fa fa-spinner fa-5x fa-spin"></i>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <div class="col-sm-6" ng-if="currentUserWidgets.includes('tasks')">
            <section class="tile" fullscreen="isFullscreen02">
                <div class="tile-header bg-slategray dvd dvd-btm">
                    <h1 class="custom-font"><strong>Tasks</strong></h1>
                    <span class="pull-right" style="margin-right: 40px; margin-top: 3px;">
                        <i class="fa fa-question-circle" popover-trigger="mouseenter" popover-placement="{{'left'}}"
                           uib-popover-template="'tasksPopover.html'"></i>
                    </span>
                    <ul class="controls">
                        <li class="dropdown" uib-dropdown>
                            <a uib-dropdown-toggle class="dropdown-toggle settings">
                                <i class="fa fa-cog"></i>
                                <i class="fa fa-spinner fa-spin"></i>
                            </a>
                            <ul class="dropdown-menu pull-right with-arrow animated littleFadeInUp">
                                <li>
                                    <a href tile-control-fullscreen
                                       ng-click="getTasks(userService.getCurrentUserId(), 'userTasks')">My Tasks</a>
                                </li>
                                <!--                                <li>-->
                                <!--                                    <a href tile-control-fullscreen-->
                                <!--                                       ng-click="getTasks(userService.getCurrentUserId(), 'employeeTasks')">Employee-->
                                <!--                                        s</a>-->
                                <!--                                </li>-->
                            </ul>
                        </li>
                    </ul>
                </div>
                <div class="tile-widget bg-slategray">
                    <div ng-if="!tasksLoading">
                        <div style="height:250px;" ng-hide="tasks.length"> You currently don't have any tasks for this
                            week.
                        </div>
                        <div>
                            <table class="table" ng-show="tasks.length && !tasksLoading" style="display: block; height:250px; overflow-y: scroll">
                                <thead>
                                    <th></th>
                                    <th>Name</th>
                                    <th>Priority</th>
                                    <th>Date</th>
                                    <th>Patient</th>
                                </thead>
                                <tbody>
                                <tr ng-repeat-start="task in tasks">
                                    <td>
                                        <i class="fa fa-circle"></i>
                                    </td>
                                    <td col-span="2"><strong>{{ task.name }}</strong></td>
                                    <td>{{ task.priority }}</td>
                                    <td>{{ moment(task.dueDate).format("L") }}</td>
                                    <td>
                                        <a ng-if="task.patientId"
                                           ui-sref="app.patient.profile({patientId: task.patientId})" target="_blank">{{utilService.formatName(task,
                                            "FL") }}</a>
                                    </td>
                                </tr>
                                <tr ng-show="task.description" ng-repeat-end>
                                    <td ng-if="task.roleId">"GROUP"</td>
                                    <td ng-if="!task.roleId"></td>
                                    <td colspan="5" ng-bind-html="task.description"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div ng-if="tasksLoading">
                        <div class="row text-center">
                            <i class="fa fa-spinner fa-5x fa-spin"></i>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <div class="col-sm-6" ng-if="currentUserWidgets.includes('outstanding_claims_with_no_activity')">
            <section class="tile">
                <div class="tile-header bg-lightred dvd dvd-btm">
                    <h1 class="custom-font"><strong>Outstanding Claims With No Activity </strong></h1>
                    <span ng-show="noActivityOnClaims.length === 50"> Top</span>
                    <span>{{ ' ' + noActivityOnClaims.length}}</span>
                    <ul class="controls">
                        <li class="dropdown" uib-dropdown>
                            <a uib-dropdown-toggle class="dropdown-toggle settings">
                                <i class="fa fa-cog"></i>
                                <i class="fa fa-spinner fa-spin"></i>
                            </a>
                            <ul class="dropdown-menu pull-right with-arrow animated littleFadeInUp">
                                <li>
                                    <a href tile-control-fullscreen ng-click="getNoActivityOnClaims('')">All</a>
                                </li>
                                <li ng-repeat="branch in userBranches ">
                                    <a href tile-control-fullscreen ng-click="getNoActivityOnClaims(branch.id)">{{branch.name}}</a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
                <div class="tile-widget bg-lightred">
                    <div style="height:250px;" ng-hide="noActivityOnClaims.length">You currently have no outstanding claims with no activity.</div>
                    <table class="table" ng-show="noActivityOnClaims.length"
                           style="display: block; height:250px; overflow-y: scroll">
                        <thead>
                            <th>Patient</th>
                            <th>Device Type</th>
                            <th>Claim Id</th>
                            <th>Days Since Last Activity</th>
                        </thead>
                        <tbody>
                            <tr ng-repeat="claim in noActivityOnClaims">
                                <td>{{ claim.lastName + ', ' + claim.firstName }}</td>
                                <td>{{ claim.deviceType }}</td>
                                <td><a ui-sref="app.billing.claim({ claimId: claim.id })" target="_blank">{{ claim.id }}</a></td>
                                <td align="center">{{moment.duration(moment().startOf('day').diff(claim.updatedAt)).asDays().valueOf().toFixed(0)}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </div>
        <div class="col-sm-6" ng-if="currentUserWidgets.includes('estimated_monthly_delivery')">
            <section class="tile">
                <div class="tile-header bg-greensea dvd dvd-btm">
                    <h1 class="custom-font"><strong>Estimated Monthly Delivery</strong></h1>
                    <ul class="controls">
                        <li class="dropdown" uib-dropdown>
                            <a uib-dropdown-toggle class="dropdown-toggle settings">
                                <i class="fa fa-cog"></i>
                                <i class="fa fa-spinner fa-spin"></i>
                            </a>
                            <ul class="dropdown-menu pull-right with-arrow animated littleFadeInUp">
                                <li>
                                    <a href tile-control-fullscreen ng-click="getEstimatedMonthlyDelivery('month')">This
                                        Month</a>
                                </li>
                                <li>
                                    <a href tile-control-fullscreen ng-click="getEstimatedMonthlyDelivery('future')">Future</a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                    <span class="pull-right" style="margin-right: 40px; margin-top: 3px;">
                        <i class="fa fa-question-circle" popover-trigger="mouseenter" popover-placement="{{'left'}}"
                           uib-popover-template="'estimatedMonthlyDeliveryPopover.html'"></i>
                    </span>
                </div>
                <div class="tile-widget bg-greensea">
                    <div ng-if="!estimatedMonthlyDeliveryLoading">
                        <div style="height:250px;" ng-hide="estimatedMonthlyDeliveryDTO.estimatedMonthlyPractitionerDeliveryDTOList.length">There are no prescriptions currently projected to be delivered in this date range.</div>
                        <table class="table" style="display: block; height:250px; overflow-y: scroll" ng-show="estimatedMonthlyDeliveryDTO.estimatedMonthlyPractitionerDeliveryDTOList.length">
                            <thead>
                                <th>{{utilService.practitionerNoun}}</th>
                                <th>Total Allowable</th>
                            </thead>
                            <tbody>
                            <tr ng-repeat="dto in estimatedMonthlyDeliveryDTO.estimatedMonthlyPractitionerDeliveryDTOList">
                                <td>{{ utilService.formatName(dto.practitioner) }}</td>
                                <td class="text-right">{{ dto.totalAllowable | currency:'$' }}</td>
                            </tr>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td></td>
                                    <td class="text-right">
                                        <strong>{{ estimatedMonthlyDeliveryDTO.totalAllowableSum | currency:'$' }}</strong>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <div ng-if="estimatedMonthlyDeliveryLoading">
                        <div class="row text-center">
                            <i class="fa fa-spinner fa-5x fa-spin"></i>
                        </div>
                    </div>
                </div>
            </section>
        </div>
<!--        <div class="col-sm-6" ng-if="currentUserWidgets.includes('daily_sales_outstanding')">-->
<!--            <section class="tile">-->
<!--                <div class="tile-header bg-greensea dvd dvd-btm">-->
<!--                    <h1 class="custom-font"><strong>Daily Sales Outstanding</strong></h1>-->
<!--                    <span class="pull-right">-->
<!--                        <i class="fa fa-question-circle" popover-trigger="mouseenter" popover-placement="{{'left'}}"-->
<!--                           uib-popover-template="'dailySalesOutstandingPopover.html'"></i>-->
<!--                    </span>-->
<!--                </div>-->
<!--                <div class="tile-widget bg-greensea">-->
<!--                    <div ng-if="!dailySalesOutstandingLoading">-->
<!--                        <div style="height:250px;" ng-hide="dailySalesOutstandingDTOs.length">-->
<!--                            No results found-->
<!--                        </div>-->
<!--                        <table class="table" style="display: block; height:250px; overflow-y: scroll"-->
<!--                               ng-show="dailySalesOutstandingDTOs.length">-->
<!--                            <thead>-->
<!--                                <th>Branch</th>-->
<!--                                <th>Days Outstanding</th>-->
<!--                            </thead>-->
<!--                            <tbody>-->
<!--                                <tr ng-repeat="dto in dailySalesOutstandingDTOs">-->
<!--                                    <td>{{ dto.branchName }}</td>-->
<!--                                    <td class="text-right">{{ dto.daysOutstanding }}</td>-->
<!--                                </tr>-->
<!--                            </tbody>-->
<!--                        </table>-->
<!--                    </div>-->
<!--                    <div ng-if="dailySalesOutstandingLoading">-->
<!--                        <div class="row text-center">-->
<!--                            <i class="fa fa-spinner fa-5x fa-spin"></i>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </section>-->
<!--        </div>-->
        <div class="col-sm-12"
             ng-if="currentUserWidgets.includes('monthly_sales_goal') || currentUserWidgets.includes('projected_vs_billed')">
            <section class="tile" fullscreen="isFullscreen02">
                <div class="tile-header bg-slategray dvd dvd-btm">
                    <h1 class="custom-font"><strong>Projected vs. Billed</strong></h1>
                    <ul class="controls">
                        <li class="dropdown" uib-dropdown>
                            <a uib-dropdown-toggle class="dropdown-toggle settings">
                                <i class="fa fa-cog"></i>
                                <i class="fa fa-spinner fa-spin"></i>
                            </a>
                            <ul class="dropdown-menu pull-right with-arrow animated littleFadeInUp">
                                <li>
                                    <a href tile-control-fullscreen
                                       ng-click="getProjectedVsBilled('patient')">Patient</a>
                                </li>
                                <li>
                                    <a href tile-control-fullscreen
                                       ng-click="getProjectedVsBilled('billing')">Billing</a>
                                </li>
                                <li>
                                    <a href tile-control-fullscreen
                                       ng-click="getProjectedVsBilled('prescription')">{{utilService.prescriptionNoun}}</a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                    <span class="pull-right" style="margin-right: 40px; margin-top: 3px;">
                        <i class="fa fa-question-circle" popover-trigger="mouseenter" popover-placement="{{'left'}}"
                           style="max-width: 400px; float: right;"
                           uib-popover-template="'projectedVsBilledPopover.html'"></i>
                    </span>
                </div>
                <div class="tile-widget bg-slategray table-responsive" style="overflow-x: auto;">
                    <table class="table" style="height:250px; overflow-y: scroll" ng-if="!projectedVsBilledLoading">
                        <thead>
                            <th>Branch</th>
                            <th class="text-right">Orthotics(B)</th>
                            <th class="text-right">Prosthetics(B)</th>
                            <th class="text-right">Pedorthic(B)</th>
                            <th class="text-right">Mastectomy(B)</th>
                            <th class="text-right">Misc(B)</th>
                            <th class="text-right">Total Billed</th>

                            <th class="text-right">Orthotics(P)</th>
                            <th class="text-right">Prosthetics(P)</th>
                            <th class="text-right">Pedorthic(P)</th>
                            <th class="text-right">Mastectomy(P)</th>
                            <th class="text-right">Misc(P)</th>
                            <th class="text-right">Total Projected</th>

                            <th class="text-right">% Met Projected</th>
                            <th class="text-right">Branch Goal</th>
                            <th class="text-right">% Met Branch Goal</th>
                        </thead>
                        <tbody>
                            <tr ng-repeat="branch in projectedVsBilled" ng-if="projectedVsBilled.length > 0">
                                <td col-span="2"><strong>{{ branch.branchName }}</strong></td>
                                <td class="text-right">{{ branch["billedOrthoticAllowable"] | currency:'$':2}}</td>
                                <td class="text-right">{{ branch["billedProstheticAllowable"] | currency:'$':2}}</td>
                                <td class="text-right">{{ branch["billedPedorthicAllowable"] | currency:'$':2}}</td>
                                <td class="text-right">{{ branch["billedMastectomyAllowable"] | currency:'$':2}}</td>
                                <td class="text-right">{{ branch["billedMiscAllowable"] | currency:'$':2}}</td>
                                <td class="text-right">{{ branch["billed"] | currency:'$':2 }}</td>
                                <td class="text-right">{{ branch["projectedOrthoticAllowable"] | currency:'$':2}}</td>
                                <td class="text-right">{{ branch["projectedProstheticAllowable"] | currency:'$':2}}</td>
                                <td class="text-right">{{ branch["projectedPedorthicAllowable"] | currency:'$':2}}</td>
                                <td class="text-right">{{ branch["projectedMastectomyAllowable"] | currency:'$':2}}</td>
                                <td class="text-right">{{ branch["projectedMiscAllowable"] | currency:'$':2}}</td>
                                <td class="text-right">{{ branch["projected"] | currency:'$':2 }}</td>
                                <td class="text-right">{{ ((branch["billed"]) / (branch["projected"])) * 100 | number: 2 }}
                                    %
                                </td>
                                <td class="text-right">{{ branch.branchMonthlySalesGoal | currency }}</td>
                                <td class="text-right">
                                    {{branch.branchMonthlySalesGoal !== "0" ? (((branch["billed"]) /
                                        branch.branchMonthlySalesGoal) * 100 | number: 2) : ('NA')}}
                                    %
                                </td>
                            </tr>
                        <tr ng-if="!projectedVsBilled.length > 0">
                            <td>No results found</td>
                        </tr>
                        </tbody>
                    </table>
                    <div ng-if="projectedVsBilledLoading">
                        <div class="row text-center">
                            <i class="fa fa-spinner fa-5x fa-spin"></i>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>

<script type="text/ng-template" id="projectedVsBilledPopover.html">
    <div>
        Billed calculation is based on the sum of allowable fees for prescriptions that have a claim submission date
        within the current month.<br>
        Projected calculation is based on the sum of allowable fees for prescriptions that have a projected delivery
        date within the current month and that do not have an attached claim submission plus the sum of allowable fees
        for prescriptions that have a claim submission date within the current month.<br>
        <i>Projected delivery dates must be entered for accurate results.</i>
        <br>
    </div>
</script>
<script type="text/ng-template" id="estimatedMonthlyDeliveryPopover.html">
    <div>
        Estimates the amount received from devices that are projected to be delivered within the current date range.
        "This Month" refers to the current calendar month, and "Future" refers to the next calendar month and later.
        Calculations are based off of the total allowable amount for the primary insurance for each of the
        prescriptions.
        Projected delivery dates must be entered for accurate results.
        <br>
        <br>
        <i>This is an estimate and is subject to change.</i>
    </div>
</script>
<script type="text/ng-template" id="arAgingPopover.html">
    <div>
        The report also gives a visual representation of all outstanding claim balances categorized by FIRST submission
        date. This claim balance outstanding total by differ from the calculated AR in the top section of the report due
        to users changing claim balances.
        <br>
        <br>
        <i>Claim balances can be manually changed and may not perfectly reflect AR.</i>
    </div>
</script>
<script type="text/ng-template" id="monthlySalesGoalPopover.html">
    <div>
        Set a monthly sales goal in Maintenance Branch settings and compare your total billed amounts throughout the month.
    </div>
</script>
<script type="text/ng-template" id="tasksPopover.html">
    <div>
        Shows incomplete tasks by due date from the previous 3 months up to and including the end of the current month. Limit of 50.
    </div>
</script>
<script type="text/ng-template" id="dailySalesOutstandingPopover.html">
    <div>
        Shows the average number of days it takes each branch to collect on a claim. All AR information is calculated YTD.
        <br>
        <br>
        If you are seeing 'No results found', it is caused by restricted branch access or the widget is not added to your user profile.
    </div>
</script>
