'use strict';

app.directive('branchDropdown', function () {
	return {
		restrict: 'A',
		scope: '=',
		controller: 'BranchSelectCtrl',
		controllerAs: 'ctrl',
		bindToController: true,
		template: '<span uib-dropdown>' +
      ' <a href style="color: white;" id="simple-dropdown" uib-dropdown-toggle><i class="fa fa-home"></i> <span ng-bind-html="ctrl.branch.name"></a>' +
      ' <ul class="dropdown-menu" uib-dropdown-menu aria-labelledby="simple-dropdown">' +
			'   <li ng-repeat="b in ctrl.branchService.userBranches | orderBy: \'name\'" class="branch-dropdown-list-item">' +
			'     <a class="branch-dropdown-item" ng-click="ctrl.selectBranch(b)">{{b.name}}</a>' +
			'   </li>' +
			' </ul>' +
			'</span>'
	}
});

app.controller('BranchSelectCtrl', ['$rootScope', 'UtilService', 'SessionService', 'BranchService',
	function ($rootScope, UtilService, SessionService, BranchService) {
		var vm = this;

		vm.branchService = BranchService;

		vm.selectBranch = function (branch) {
			vm.branch = branch;
			BranchService.changeBranch(branch);
		};

		// Now set with a modal each time the user enters if not set...
		// if (!$rootScope.branchId)
		// 	UtilService.displayAlert("danger", "<p><i class='fa fa-home fa-2x' ></i> Select a default branch from the icon below.</p>", "#header-alert-container");

		$rootScope.$on('initial-branch-selected', function () {
			vm.branch = $rootScope.branch;
		});

		$rootScope.$on('setCurrentBranchSessionService', function () {
			if($rootScope.branch)
			{
				vm.branch = $rootScope.branch;
			}
		});

		$rootScope.$on('readyToLoadLeftNav', function () {
			if($rootScope.branch)
			{
				vm.branch = $rootScope.branch;
			}
		});

		$rootScope.$on('localStorageObjectLoadComplete', function () {
			if(!$rootScope.branch && BranchService.userBranches.length === 1)
			{
				vm.selectBranch(BranchService.userBranches[0]);
			} else {
				vm.branch = $rootScope.branch;
			}
		});
	}]);
