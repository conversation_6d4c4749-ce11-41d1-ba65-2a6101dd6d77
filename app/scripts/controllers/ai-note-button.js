"use strict";

angular.module("minovateApp").component("aiNoteButton", {
  bindings: {
    patientId: "<",
    prescriptionId: "<",
    transcriptionId: "<",
    enabled: "<",
  },
  template: `

      <button type="button"
              name="show-ai-note"
              class="btn ai-bg btn-rounded btn-sm"
              ng-click="$ctrl.onClick()"
              ng-show="$ctrl.enabled && $ctrl.prescriptionId != 0">
        <i class="pi pi-sparkle"></i>
        {{!$ctrl.transcriptionId ? 'New AI Note' : ''}}
      </button>
      <style>
        :root {
            --ai-gradient: linear-gradient(90deg, #649FFF 0%, #AB63F2 48%, #D95BB7 100%);
        }
  
        button.ai-bg {
            background-color: transparent;
            background-image: var(--ai-gradient);
            border: 1px solid transparent;
            color: #fff;
        }
  
        button.ai-bg:hover,
        button.ai-bg:active {
            background-color: transparent;
            background-image: var(--ai-gradient) !important;
            color: #fff;
        }
  
        button.ai-bg:hover::after,
        button.ai-bg:active::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            background: hsl(0 0% 0%);
            opacity: 0.2;
            mix-blend-mode: overlay;
        }
  
        button.ai-bg i.pi-sparkle::before {
            content: url('images/sparkle.svg');
        }
      </style>

    `,
  controller: function (UtilService) {
    var $ctrl = this;
    let queryParams =  "patientId=" +
        this.patientId +
        "&prescriptionId=" +
        this.prescriptionId;
    let page ="ai-notes";

    if(this.transcriptionId) {
      queryParams = '';
      page += '/' + this.transcriptionId
    }

    $ctrl.onClick = function () {
      UtilService.openV2Link(
        "patients/",
         page,
          queryParams
      );
    };
  },
});
