'use strict';
app.controller('NotesSummaryCtrl', NotesSummaryCtrl);
NotesSummaryCtrl.$inject = ['$rootScope', '$scope', '$filter', '$moment', 'UtilService', 'BranchService', 'AppointmentFactory', 'UserFactory', 'UserService', 'DateService', 'SystemSettingFactory'];

function NotesSummaryCtrl($rootScope, $scope, $filter, $moment, UtilService, BranchService, AppointmentFactory, UserFactory, UserService, DateService, SystemSettingFactory) {

	$rootScope.page = {
		title: 'Patients',
		subtitle: 'Patient Notes'
	};

	$scope.dateService = DateService;
	var currentUser = UserService.getCurrentUser();
	$scope.filter = {
		userId: '',
	  branchId: null,
	  appointmentTypeId: '',
	  startDateTime: null,
	  endDateTime: null,
	  // branchId: $rootScope.branchId,
	  columnName: "startDateTime",
	  direction: "asc"
  };
	$scope.moment = $moment;

	$scope.sortBy = function (columnName) {
		$scope.filter.columnName = columnName;
		$scope.filter.direction = $scope.filter.direction === 'ASC' ? 'DESC' : 'ASC';
		$scope.clientSortColumn = null;
		$scope.search();
	};

	$scope.reverseOrder = false;
	$scope.clientSortBy = function (columnName) {
		$scope.clientSortColumn = columnName;
		$scope.reverseOrder = !$scope.reverseOrder;
	};

	// Datepicker
	$scope.startDate = $moment().subtract(29, "days").format("MMMM D, YYYY");
	$scope.endDate = $moment().format("MMMM D, YYYY");
	$scope.rangeOptions = {
		ranges: {
			"3 Days": [$moment().subtract(2, "days"), $moment()],
			"5 Days": [$moment().subtract(4, "days"), $moment()],
			"7 Days": [$moment().subtract(6, "days"), $moment()],
			"14 Days": [$moment().subtract(13, "days"), $moment()],
			"30 Days": [$moment().subtract(29, "days"), $moment()]
		},
		opens: "left",
		startDate: $moment().subtract(29, "days"),
		endDate: $moment(),
		parentEl: "#content"
	};
	// End Datepicker

	// SCRUM-5242... $scope.treatingPractitioners = UserFactory.getPractitionerUsers({companyId: UserService.getCompanyId()});
	$scope.treatingPractitioners = UserFactory.getPractitionersAndCareExtenders({companyId: UserService.getCompanyId()});

	$scope.branches = BranchService.userBranches;

  $scope.search = function () {
	  $scope.loading = true;
	  $scope.appointments = [];
	  $scope.filter.startDateTime = $moment($scope.startDate, "MMM DD, YYYY").startOf('day').toISOString();
	  $scope.filter.endDateTime = $moment($scope.endDate, "MMM DD, YYYY").endOf('day').toISOString();
	  AppointmentFactory.findByMissingNotesDto($scope.filter).$promise.then(function (response) {
		  $scope.loading = false;
		  angular.forEach(response, function (appointment, index) {
			  var startDay = $moment(appointment.notesDto.startDateTime).format("YYYY-MM-DD");
			  appointment.$age = $moment().diff(startDay, 'days');
			  if ($moment().diff(startDay, 'days') >= 0 && appointment.patient) {
				  $scope.appointments.push(appointment);
			  }
		  });
	  });
	};

	$scope.aiNotesEnabled = false;

	SystemSettingFactory.findBySectionAndField({
		section: "general",
		field: "ai_notes_on"
	}).$promise.then(function (response) {
		$scope.aiNotesEnabled = response.value === 'Y';
	});
}
