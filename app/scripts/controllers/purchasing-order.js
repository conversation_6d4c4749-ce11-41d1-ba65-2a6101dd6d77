app.controller('PurchasingOrderCtrl', PurchasingOrderCtrl);
PurchasingOrderCtrl.$inject = ['$filter', '$moment', '$rootScope', '$scope', '$state', '$stateParams', '$timeout', 'BranchFactory',
	'CascadeWarehouseFactory', 'FileService', 'InventoryItemFactory', 'isModal', 'ItemService', 'ItemTypesFactory',
	'LCodeCategoryFactory', 'NotificationService', 'PatientFactory', 'PatientService', 'PrescriptionFactory',
	'PrescriptionLCodeFactory', 'purchaseOrderDto', 'PurchaseOrderFactory', 'PurchaseOrderInvoiceFactory',
	'PurchaseOrderItemFactory', 'PurchaseOrderItemStatusesFactory', 'PurchasingService', 'SessionService',
	'ShoppingCartFactory', 'StatesFactory', 'SystemSettingFactory', 'ThirdPartyShippingMethodFactory',
	'toastr', 'UOMFactory', 'UserFactory', 'UserService', 'UtilService', 'VendorFactory'];

function PurchasingOrderCtrl($filter, $moment, $rootScope, $scope, $state, $stateParams, $timeout, BranchFactory,
                             CascadeWarehouseFactory, FileService, InventoryItemFactory, isModal, ItemService, ItemTypesFactory,
                             LCodeCategoryFactory, NotificationService, PatientFactory, PatientService, PrescriptionFactory,
                             PrescriptionLCodeFactory, purchaseOrderDto, PurchaseOrderFactory, PurchaseOrderInvoiceFactory,
                             PurchaseOrderItemFactory, PurchaseOrderItemStatusesFactory, PurchasingService, SessionService,
                             ShoppingCartFactory, StatesFactory, SystemSettingFactory, ThirdPartyShippingMethodFactory,
                             toastr, UOMFactory, UserFactory, UserService, UtilService, VendorFactory) {

	$rootScope.page = {
		title: 'Purchasing',
		subtitle: $stateParams.purchaseOrderId ? 'Purchase Order #' + $stateParams.purchaseOrderId : 'New Purchase Order',
		view: 'purchase_order'
	};

	$scope.branches = BranchFactory.active();
	$scope.calendar = {
		opened: {},
		dateOptions: {
			formatYear: 'yy',
			minDate: new Date().setFullYear(new Date().getFullYear() - 130),
			startingDay: 1
		},
		open: function ($event, which) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.calendar.opened[which] = true;
		}
	};
	$scope.canCalculateTotals = true;
	$scope.cascadeLocations = [];
	$scope.cascadeWarehouses = CascadeWarehouseFactory.get();
	$scope.currentUser = UserFactory.getCurrentUser();
	$scope.delivery_location = undefined;
	$scope.files = [];
	$scope.fileService = FileService;
	$scope.full_address = undefined;
	$scope.hasPermission = UserService.hasPermission;
	$scope.inventoryStatuses = ['from_inventory', 'inventory'];
	// invoicesByPoiIdMap is a map of key/value pairs, where key is poiId and value is array of invoices
	$scope.invoicesByPoiIdMap = new Map();
	// invoicesOriginal is a copy of the list of invoices originally loaded from the DB, so they all have valid IDs
	$scope.invoicesOriginal = [];
	$scope.isCascade = false;
	$scope.isSPS = false;
	$scope.itemTypes = ItemTypesFactory.query();
	$scope.LCodeCategoryOptions = LCodeCategoryFactory.getCategoryOptions().$promise.then(function (response) {
		$scope.LCodeCategoryOptions = response;
	});
	$scope.newItemIndex = null;
	$scope.nymblLocations = [];
	$scope.patientService = PatientService;
	// physicalItemCountsByPoiId is map of id/array pairs, where id is POI id and array is list of physical items
	$scope.physicalItemCountsByPoiId = new Map();
	// poInvoices is a map of id/array pairs, where id is POI id and array is list of invoices
	$scope.poInvoices = {};
	// poInvoicesOriginal is a copy of the list of invoices originally loaded from the DB, so they all have IDs
	$scope.poInvoicesOriginal = [];
	$scope.poInvoicesToDelete = [];
	$scope.poiStatusesThatGetUpdated = ['approved', 'awaiting_approval', 'backordered', 'closed', 'completed', 'credit_approved', 'from_inventory', 'inventory', 'on_hold', 'open_cart', 'open', 'ordered', 'partially_received', 'pending_review', 'purchase_order_error', 'received', 'reconciled', 'return_requested', 'shipped', 'transfer_order', 'transfer_pending', 'transfer_received'];
	$scope.poItemsToDelete = [];
	$scope.poStatusesThatUpdate = ['approved', 'awaiting_approval', 'canceled', 'closed', 'completed', 'credit_approved', 'denied', 'from_inventory', 'inventory', 'loaner', 'on_hold', 'open_cart', 'open', 'ordered', 'pending_review', 'purchase_order_error', 'received', 'reconciled', 'return_rejected', 'returned', 'shipped', 'transfer_order', 'transfer_pending', 'transfer_received'];
	$scope.purchaseOrder = null;
	$scope.purchaseOrderDto = purchaseOrderDto;
	$scope.purchaseOrderItems = [];
	$scope.purchaseOrderItemsOriginal = [];
	$scope.purchaseOrderOriginal = null;
	$scope.purchasingService = PurchasingService;
	$scope.selected = {location: ""};
	$scope.shipping = {branch: null};
	$scope.shippingMethod = undefined;
	$scope.shippingMethods = [];
	$scope.showXML = false;
	$scope.states = StatesFactory.get();
	$scope.statuses = PurchaseOrderItemStatusesFactory.get();
	$scope.thirdPartyLocationId = undefined;
	$scope.unitsOfMeasure = UOMFactory.query();
	$scope.utilService = UtilService;
	$scope.vendors = VendorFactory.search();

	$scope.init = function () {
		$scope.loading = true;

		$scope.practitioners = UserFactory.getPractitionerUsers({companyId: UserService.getCompanyId()});

		if ($stateParams.purchaseOrderId) {
			$scope.purchaseOrderId = $stateParams.purchaseOrderId;
			PurchaseOrderFactory.get({id: $scope.purchaseOrderId}).$promise.then(function (purchaseOrder) {
				$scope.purchaseOrderOriginal = angular.copy(purchaseOrder);
				$scope.purchaseOrder = purchaseOrder;
				$scope.purchaseOrder.singleVendor = $scope.purchaseOrder.vendorId ? true : false;
				$scope.poom = $scope.purchaseOrder.poom ? UtilService.prettifyXML($scope.purchaseOrder.poom) : '';
				if ($scope.purchaseOrder.vendor !== undefined) {
					var name = $scope.purchaseOrder.vendor.name;
					$scope.isCascade = name === 'Cascade Orthopedic Supply' && !!$scope.poom;
					$scope.isSPS = name === 'SPS' && !!$scope.poom;
					if ($scope.isCascade || $scope.isSPS) {
						ThirdPartyShippingMethodFactory.findAllByVendor({vendor: $scope.isCascade ? 'COS' : 'SPS'}).$promise.then(function (result) {
							$scope.shippingMethods = result;
						});
						if ($scope.isCascade) {
							PurchaseOrderFactory.cascadeGetLocations().$promise.then(function (response) {
								$scope.cascadeLocations = response;
								angular.forEach($scope.cascadeLocations, function (entry, index) {
									if (entry.id === $scope.purchaseOrder.thirdPartyLocationId)
										$scope.selected.location = entry;
								});
							}, function (error) {
								console.error(JSON.stringify(error));
							});
						}
					}
				}
				loadInvoices();
				PurchaseOrderItemFactory.findByPurchaseOrderId({purchaseOrderId: $scope.purchaseOrderId}).$promise.then(function (purchaseOrderItems) {
					$scope.purchaseOrderItemsOriginal = angular.copy(purchaseOrderItems);
					$scope.purchaseOrderItems = purchaseOrderItems;
					loadPhysicalItems();
					if ($scope.purchaseOrderItems.length > 0 && $scope.purchaseOrderItems[0].branch &&
						$scope.purchaseOrderItems[0].branch.purchaseOrderPrefix &&
						$scope.purchaseOrderItems[0].branch.purchaseOrderPrefix !== "") {
						$scope.purchaseOrderPrefix = $scope.purchaseOrderItems[0].branch.purchaseOrderPrefix;
					}
					angular.forEach($scope.purchaseOrderItems, function (poi, index) {
						if (poi.patientId) {
							if ($scope.purchaseOrder.patientId === undefined && $scope.purchaseOrder.patient === undefined) {
								$scope.purchaseOrder.patientId = poi.patientId;
								$scope.purchaseOrder.patient = poi.patient;
							} else if ($scope.purchaseOrder.patientId !== poi.patientId) {
								$scope.purchaseOrder.patient = undefined;
								$scope.purchaseOrder.patientId = undefined;
							}
							// prescriptions
							PrescriptionFactory.findByPatientIdActiveIsTrue({patientId: poi.patientId}).$promise.then(function (prescriptions) {
								poi.prescriptions = prescriptions;
								poi.prescriptions.forEach(function (rx) {
									PrescriptionLCodeFactory.findByPrescriptionId({prescriptionId: rx.id, stripDown: true}).$promise.then(function (prescriptionLCodes) {
										rx.prescriptionLCodes = prescriptionLCodes;
										if (rx.id === poi.prescriptionId) {
											poi.prescription.prescriptionLCodes = prescriptionLCodes;
										}
									});
								});
							});
						}
					});
					$scope.loading = false;
					$scope.submitted = false;
					$scope.calculateTotals();
				});
			});
			FileService.getPurchaseOrderFiles($scope.purchaseOrderId);
		} else if ($stateParams.referenceNumber) {
			var orderStatus = $stateParams.orderStatus;
			if (orderStatus != null) {
				orderStatus = orderStatus.toLowerCase().replace(' ', '_');
			}
			var referenceNumber = $stateParams.referenceNumber;
			var _quantity = Number($stateParams.quantity);
			$scope.purchaseOrder = {
				additionalCharges: null,
				cog: null,
				discount: null,
				expectedAt: $stateParams.expectedAt,
				manualStatusSelection: false,
				note: decodeURI($stateParams.customer_note),
				orderedAt: $stateParams.orderedAt,
				orderedById: '',
				patient: null,
				patientId: '',
				prescriptionId: '',
				referenceNumber: referenceNumber,
				salesTax: null,
				shippingCharges: null,
				singleBranch: false,
				singlePatient: false,
				singlePractitioner: false,
				singleVendor: false,
				status: orderStatus,
				streetAddress: '',
				streetAddress2: '',
				subTotal: $scope.roundMoney(_quantity * $stateParams.price),
				totalCost: $scope.roundMoney(_quantity * $stateParams.price),
				vendorId: '',
				voidStatuses: ['canceled', 'denied', 'purchase_order_error', 'returned']
			};

			if ($stateParams.job_number != null) {
				PrescriptionFactory.get({id: $stateParams.job_number}).$promise.then(function (response) {
					$scope.purchaseOrder.patientId = response.patientId;
					$scope.purchaseOrder.prescriptions = [];
					$scope.purchaseOrder.prescriptions.push(response);
					$scope.purchaseOrder.prescriptionId = response.id;
					PatientFactory.get({id: $scope.purchaseOrder.patientId}).$promise.then(function (patient) {
						$scope.purchaseOrder.patient = patient;
						$scope.purchaseOrder.singlePatient = true;
						$scope.purchaseOrder.singlePractitioner = true;
						$scope.purchaseOrder.practitionerId = $scope.currentUser.id;
					});
				});
			}
			if ($stateParams.shipping_address != null) {
				setEmpireShippingAddress($stateParams.shipping_address);
				$scope.delivery_location = 'other';
			}

			var part_number = decodeURI($stateParams.part_number);
			$scope.activeRecord = {
				id: null,
				name: $stateParams.description,
				description: $stateParams.description,
				partNumber: part_number,
				sku: '',
				upc: '',
				upcType: 'A',
				price: $stateParams.price,
				lcodes: [],
				users: [],
				active: true
			};
			var vendor = $stateParams.vendor;
			$scope.vendor = null;
			$scope.item = null;
			// TODO: this call uses a deprecated method and needs to be replaced
			ItemService.findItemByPartNumberAndVendor(vendor, part_number).then(function (response) {
				// TODO: this part until "else" makes no sense, items returned from the DB always have id
				if (response.id == null) {
					ItemService.saveItem($scope.activeRecord).then(function (saved) {
						$scope.item = saved;
					});
				} else {
					$scope.item = response;
				}
				VendorFactory.search({"q": vendor}).$promise.then(function (response) {
					$scope.vendor = response;
					//alert("item = "+$scope.item.name+"  vendor = "+$scope.vendor[0].name);
					if ($scope.item != null && $scope.vendor[0] != null) {
						$scope.purchaseOrderItems = [{
							"itemCost": $stateParams.price,
							"quantity": _quantity,
							"item": $scope.item,
							"itemId": $scope.item.id,
							"vendor": $scope.vendor[0],
							"vendorId": $scope.vendor[0].id,
							"practitionerId": $scope.currentUser.id,
							"is_for_stock": $stateParams.is_for_stock
						}];
					}
				});
			});
			$scope.calculateTotals();
			$scope.loading = false;
			$scope.submitted = false;
		} else {
			$scope.purchaseOrder = {
				additionalCharges: null,
				cog: null,
				discount: null,
				expectedAt: '',
				manualStatusSelection: false,
				note: '',
				orderedAt: $moment().format('YYYY-MM-DD'),
				orderedById: UserService.getCurrentUserId(),
				patient: null,
				patientId: '',
				prescriptionId: '',
				referenceNumber: '',
				salesTax: null,
				shippingCharges: null,
				singleBranch: false,
				singlePatient: false,
				singlePractitioner: false,
				singleVendor: false,
				status: 'open',
				subTotal: 0,
				totalCost: 0,
				vendorId: '',
				voidStatuses: ['canceled', 'denied', 'purchase_order_error', 'returned']
			};
			$scope.purchaseOrderItems = []
			$scope.addLineItem();
			$scope.loading = false;
			$scope.submitted = false;
		}
		SystemSettingFactory.findBySection({section: "purchasing"}).$promise.then(function (response) {
			$scope.purchasing = response;
		});
		$scope.physicalItemsCreationMessage = '';
	};

	// poiId is either poi.id (for a POI) or 0 (for the PO)
	$scope.addInvoice = function(poiId) {
		if (!$scope.invoicesByPoiIdMap.get(poiId)) { $scope.invoicesByPoiIdMap.set(poiId, []); }
		$scope.invoicesByPoiIdMap.get(poiId).push({
			// this is a temp ID so that the invoice can be identified in the UI, it will be replaced with a real ID when saved
			id: (Math.abs(poiId) * 1000 + $scope.invoicesByPoiIdMap.get(poiId).length + 1) * -1,
			invoiceDate: null,
			invoiceNumber: '',
			purchaseOrderId: $scope.purchaseOrderId,
			purchaseOrderItemId: poiId != 0 ? poiId : null
		});
	};

	$scope.addLCode = function ($item, $model, $label) {
		$scope.activeRecord.lCodes.push($item);
	};

	$scope.addLineItem = function () {
		var newId = ($scope.purchaseOrderItems.length + 1) * -1;
		$scope.purchaseOrderItems.push({
			branch: $scope.purchaseOrder.singleBranch ? $scope.purchaseOrder.branch : null,
			branchId: $scope.purchaseOrder.singleBranch ? $scope.purchaseOrder.branchId : null,
			cog: null,
			discount: 0,
			id: newId,
			item: null,
			itemCost: 0,
			itemId: null,
			patient: $scope.purchaseOrder.singlePatient ? $scope.purchaseOrder.patient : null,
			patientId: $scope.purchaseOrder.singlePatient ? $scope.purchaseOrder.patientId : null,
			patientSalePrice: null,
			prescription: $scope.purchaseOrder.singlePatient ? $scope.purchaseOrder.prescription : null,
			prescriptionId: $scope.purchaseOrder.singlePatient ? $scope.purchaseOrder.prescriptionId : null,
			prescriptionLCode: null,
			prescriptionLCodeId: null,
			prescriptions: $scope.purchaseOrder.singlePatient ? $scope.purchaseOrder.prescriptions : [],
			status: 'open',
			type: '',
			vendor: $scope.purchaseOrder.singleVendor ? $scope.purchaseOrder.vendor : null,
			vendorId: $scope.purchaseOrder.singleVendor ? $scope.purchaseOrder.vendorId : null
		});
	};

	$scope.calculateTotals = function () {
		if (!$scope.canCalculateTotals) {
			return;
		}
		if ($scope.purchaseOrder.additionalCharges <= 0) $scope.purchaseOrder.additionalCharges = null;
		if ($scope.purchaseOrder.discount <= 0) $scope.purchaseOrder.discount = null;
		if ($scope.purchaseOrder.salesTax <= 0) $scope.purchaseOrder.salesTax = null;
		if ($scope.purchaseOrder.shippingCharges <= 0) $scope.purchaseOrder.shippingCharges = null;
		var additionalCharges = $scope.purchaseOrder.additionalCharges ? Number($scope.purchaseOrder.additionalCharges) : 0;
		var discount = $scope.purchaseOrder.discount ? Number($scope.purchaseOrder.discount) : 0;
		var salesTax = $scope.purchaseOrder.salesTax ? Number($scope.purchaseOrder.salesTax) : 0;
		var shippingCharges = $scope.purchaseOrder.shippingCharges ? Number($scope.purchaseOrder.shippingCharges) : 0;
		// Calculate PO subtotal as sum of all items' list prices
		var subTotal = 0;
		$scope.purchaseOrderItems.forEach(function (poi, index) {
			// clean up the item's cost values
			if (poi.quantity == null || poi.quantity <= 0) poi.quantity = 0;
			if (poi.itemCost == null) {
				poi.itemCost = 0;
				poi.totalCost = 0;
			} else {
				poi.totalCost = $scope.roundMoney(poi.itemCost * poi.quantity);
			}
			// add the item's totalCost to the subtotal
			if (!$scope.purchaseOrder.voidStatuses.includes(poi.status)) {
				poi.cog = poi.totalCost;
				subTotal += poi.totalCost;
			} else {
				poi.cog = 0;
			}
		});
		$scope.purchaseOrder.subTotal = $scope.roundMoney(subTotal);
		var total = Number(subTotal) + Number(salesTax) + Number(shippingCharges) + Number(additionalCharges) - discount;
		$scope.purchaseOrder.totalCost = $scope.roundMoney(total)

		// all additional charges and discount are calculated from total ones prorated per item.totalCost
		$scope.purchaseOrderItems.forEach(function (poi, index) {
			//
			if (!$scope.purchaseOrder.voidStatuses.includes(poi.status)) {
				if (poi.patientSalePrice <= 0) poi.patientSalePrice = null;
				if (subTotal != 0 && poi.totalCost != 0) {
					// subtotal can be 0 only if all items are 0, but even then 0/0 is not valid
					var itemWeight = subTotal > 0 ? Number(poi.itemCost * poi.quantity / subTotal) : 0;
					poi.$additionalCharges = $scope.roundMoney(itemWeight * additionalCharges);
					poi.$discount = $scope.roundMoney(itemWeight * discount);
					poi.$salesTax = $scope.roundMoney(itemWeight * salesTax);
					poi.$shippingCharges = $scope.roundMoney(itemWeight * shippingCharges);
					poi.totalCost = $scope.roundMoney(itemWeight * total);
					poi.cog = poi.totalCost;
				} else {
					poi.$additionalCharges = 0;
					poi.$discount = 0;
					poi.$salesTax = 0;
					poi.$shippingCharges = 0;
					poi.cog = 0;
					poi.totalCost = 0;
				}
			}
		});
	};

	$scope.canChangeStatus = function () {
		return UserService.isAdmin() || UserService.isInventory();
	};

	/**
	 * TODO: seems to be not used
	 * @param index
	 * @param itemId
	 */
	$scope.changeItemPrice = function (index, itemId) {
		if (itemId) {
			ItemService.getItem(itemId).then(function (item) {
				$scope.purchaseOrderItems[index].itemCost = Number(item.price);
			});
		} else {
			$scope.purchaseOrderItems[index].itemCost = 0.0;
		}
		$scope.calculateTotals();
	};

	$scope.createNewItem = function (index) {
		$scope.newItemIndex = index;
		$scope.purchasingService.openNewItemModal(null, false, false);
	};

	$scope.deleteInvoice = function (invoice, askToConfirm) {
		if (!askToConfirm || confirm('Are you sure you want to delete invoice ' + (invoice.invoiceNumber ? ('number ' + invoice.invoiceNumber) : invoice.creditMemo) + '?')) {
			var poiId = invoice.purchaseOrderItemId ? invoice.purchaseOrderItemId : 0;
			var index = $scope.invoicesByPoiIdMap.get(poiId).findIndex(i => i.id == invoice.id);
			if (index >= 0) {
				var invoiceToDelete = $scope.invoicesByPoiIdMap.get(poiId).splice(index, 1)[0];
				if (invoiceToDelete.id > 0) {
					if (!$scope.poInvoicesToDelete) { $scope.poInvoicesToDelete = []; }
					$scope.poInvoicesToDelete.push(invoiceToDelete.id);
				}
			}
		}
	};

	$scope.deleteLCode = function (lCode) {
		angular.forEach($scope.activeRecord.lCodes, function (item, index) {
			if (item === lCode)
				$scope.activeRecord.lCodes.splice(index, 1);
		});
	};

	$scope.deletePOItem = function (form, index, moveToCart) {
		if (index >= 0 && index < $scope.purchaseOrderItems.length) {
			var poi = $scope.purchaseOrderItems[index];
			if (poi != null && poi.id > 0 && confirm('Are you sure you want delete item "' + poi.item.name + '"?')) {
				if (moveToCart) {
					var shoppingCart = {
						branchId: poi.branchId,
						itemId: poi.itemId,
						neededOn: poi.dateNeeded,
						notes: poi.additionalComments,
						patientId: poi.patientId,
						patientProfile: poi.patientId != null && poi.patientId > 0,
						prescriptionLCodeId: poi.prescriptionLCodeId,
						prescriptionId: poi.prescriptionId,
						quantity: poi.quantity,
						status: 'Open',
						userId: UserService.getCurrentUserId()
					};
					ShoppingCartFactory.save(shoppingCart);
				}
				$scope.poItemsToDelete.push($scope.purchaseOrderItems[index].id);
				$scope.purchaseOrderItems.splice(index, 1);
				// delete this POI's invoices
				var poiInvoices = $scope.invoicesByPoiIdMap.get(poi.id);
				if (poiInvoices && poiInvoices.length) {
					if (!$scope.poInvoicesToDelete) { $scope.poInvoicesToDelete = []; }
					poiInvoices.forEach(function (invoice) {
						$scope.poInvoicesToDelete.push(invoice.id);
					});
					$scope.invoicesByPoiIdMap.set(poi.id, []);
				}
				$scope.save(form);
			}
		} else {
			alert("The index " + index + " is outside of $scope.purchaseOrderItems[" + $scope.purchaseOrderItems.length + "] boundary");
		}
	};

	$scope.disableManufacturer = function (id, poom) {
		if (poom) {
			var temp = '#poiManufacturerId' + id;
			$(temp).prop('disabled', true).trigger("chosen:updated");
		}
	};

	$scope.disableVendor = function (id, poom) {
		if (poom) {
			var temp = '#poiVendorId' + id;
			$(temp).prop('disabled', true).trigger("chosen:updated");
		}
	};

	var getPoStatus = function () {
		if (!$scope.purchaseOrder.manualStatusSelection) {
			var distinctStatuses = [];
			angular.forEach($scope.purchaseOrderItems, function (poi) {
				if (!distinctStatuses.includes(poi.status)) distinctStatuses.push(poi.status);
			});
			if (distinctStatuses.length == 1) {
				switch(distinctStatuses[0]) {
					case 'from_inventory':
					case 'inventory':
						return 'inventory';
					case 'received':
					case 'reconciled':
						return 'closed';
					default:
						return 'open';
				}
			} else {
				return 'open';
			}
		} else {
			return $scope.purchaseOrder.status;
		}
	};

	// I am using "poiId=0" for the PO as opposed to POIs which have their respective IDs
	$scope.getPoiId = function (poi) {
		return poi ? poi.id : 0;
	};

	$scope.getVendorShippingMethodName = function (id) {
		var name = "";
		angular.forEach($scope.shippingMethods, function (method, index) {
			if (id === method.id) {
				name = method.vendorShippingMethod;
			}
		});
		return name;
	};

	var isAllPristine = function () {
		var isPristine = isPoPristine();
		if (isPristine) {
			isPristine = isAllPoItemsPristine();
		}
		if (isPristine) {
			isPristine = isAllInvoicesPristine();
		}
		return isPristine;
	}

	var isAllInvoicesPristine = function () {
		// this includes 0 for PO-level invoices
		var poiIds = Array.from($scope.invoicesByPoiIdMap.keys());
		// Quick check: compare invoice counts against invoicesOriginal
		for (var i = 0; i < poiIds.length; i++) {
			var poiId = poiIds[i];
			var poiInvoices = $scope.invoicesByPoiIdMap.get(poiId);
			if (poiInvoices.length != $scope.invoicesOriginal
				.filter(invoice => (poiId == 0 && invoice.purchaseOrderItemId == null) || (poiId != 0 && invoice.purchaseOrderItemId == poiId)).length) {
				return false;
			}
		}
		// Detailed check: compare each invoice against its original
		for (var i = 0; i < poiIds.length; i++) {
			var poiId = poiIds[i];
			var poiInvoices = $scope.invoicesByPoiIdMap.get(poiId);
			for (var j = 0; j < poiInvoices.length; j++) {
				if (!isInvoicePristine(poiInvoices[j])) {
					return false;
				}
			}
		}
		return true;
	};

	var isAllPoItemsPristine = function () {
		if ($scope.purchaseOrderItems.length != $scope.purchaseOrderItemsOriginal.length) {
			return false;
		}
		// every POI must have a valid ID and be identical to its matching original POI
		for (var i = 0; i < $scope.purchaseOrderItems.length; i++) {
			if (!isPoItemPristine($scope.purchaseOrderItems[i])) {
				return false;
			}
		}
	};

	var isDatesEqual = function(value1, value2) {
		var OK = isEqual(value1, value2);
		if (!OK) {
			var dateString1 = value1 != null && value1 instanceof Date ? value1.toISOString() : value1;
			var dateString2 = value2 != null && value2 instanceof Date ? value2.toISOString() : value2;
			var date1 = dateString1 != null && dateString1.length >= 10 ? $moment(dateString1) : null;
			var date2 = dateString2 != null && dateString2.length >= 10 ? $moment(dateString2) : null;
			OK = (date1 != null && date1.isValid() && date2 != null && date2.isValid() &&
				date1.format('YYYY-MM-DD') == date2.format('YYYY-MM-DD'));
		}
		return OK;
	};

	var isEqual = function(value1, value2) {
		return ((value1 == null && value2 == null) || (value1 != null && value2 != null && value1 == value2));
	};

	/**
	 * To be considered pristine, invoice must have a valid ID and be identical to its matching original invoice
	 * @param invoice
	 * @returns {boolean|false|*}
	 */
	var isInvoicePristine = function (invoice) {
		var invoiceOriginal = invoice.id > 0 ? $scope.invoicesOriginal.find(i => i.id == invoice.id) : null;
		return (invoiceOriginal &&
			isEqual(invoice.creditMemo, invoiceOriginal.creditMemo) &&
			isDatesEqual(invoice.invoiceDate, invoiceOriginal.invoiceDate) &&
			isEqual(invoice.invoiceNumber, invoiceOriginal.invoiceNumber) &&
			isEqual(invoice.purchaseOrderId, invoiceOriginal.purchaseOrderId) &&
			isEqual(invoice.purchaseOrderItemId, invoiceOriginal.purchaseOrderItemId));
	}

	var isPoItemPristine = function (poi) {
		var poiOriginal = poi.id > 0 ? $scope.purchaseOrderItemsOriginal.find(item => item.id == poi.id) : null;
		return (poiOriginal &&
			isEqual(poi.additionalComments, poiOriginal.additionalComments) &&
			isEqual(poi.approved, poiOriginal.approved) &&
			isEqual(poi.branchId, poiOriginal.branchId) &&
			isDatesEqual(poi.dateExpected, poiOriginal.dateExpected) &&
			isDatesEqual(poi.dateNeeded, poiOriginal.dateNeeded) &&
			isEqual(poi.description, poiOriginal.description) &&
			isEqual(poi.itemCost, poiOriginal.itemCost) &&
			isEqual(poi.itemId, poiOriginal.itemId) &&
			isEqual(poi.name, poiOriginal.name) &&
			isEqual(poi.item.oneTimePurchase, poiOriginal.item.oneTimePurchase) &&
			isEqual(poi.parentId, poiOriginal.parentId) &&
			isEqual(poi.partNumber, poiOriginal.partNumber) &&
			isEqual(poi.patientId, poiOriginal.patientId) &&
			isEqual(poi.patientSalePrice, poiOriginal.patientSalePrice) &&
			isEqual(poi.practitionerId, poiOriginal.practitionerId) &&
			isEqual(poi.prescriptionId, poiOriginal.prescriptionId) &&
			isEqual(poi.prescriptionLCodeId, poiOriginal.prescriptionLCodeId) &&
			isEqual(poi.quantity, poiOriginal.quantity) &&
			isEqual(poi.rma, poiOriginal.rma) &&
			isEqual(poi.serialNumber, poiOriginal.serialNumber) &&
			isEqual(poi.showOnDelivery, poiOriginal.showOnDelivery) &&
			isEqual(poi.sku, poiOriginal.sku) &&
			isEqual(poi.splitPurchaseOrderItems, poiOriginal.splitPurchaseOrderItems) &&
			isEqual(poi.status, poiOriginal.status) &&
			isEqual(poi.stocked, poiOriginal.stocked) &&
			isEqual(poi.thirdPartyLocationId, poiOriginal.thirdPartyLocationId) &&
			isEqual(poi.thirdPartyShippingMethodId, poiOriginal.thirdPartyShippingMethodId) &&
			isEqual(poi.totalCost, poiOriginal.totalCost) &&
			isEqual(poi.trackingNumbers, poiOriginal.trackingNumbers) &&
			isEqual(poi.type, poiOriginal.type) &&
			isEqual(poi.unitOfMeasure, poiOriginal.unitOfMeasure) &&
			isEqual(poi.vendorId, poiOriginal.vendorId) &&
			isEqual(poi.warrantyInfo, poiOriginal.warrantyInfo));
	};

	var isPoPristine = function () {
		return ($scope.purchaseOrder.id > 0 && $scope.purchaseOrderOriginal &&
			isEqual($scope.purchaseOrder.additionalCharges, $scope.purchaseOrderOriginal.additionalCharges) &&
			isEqual($scope.purchaseOrder.attn, $scope.purchaseOrderOriginal.attn) &&
			isEqual($scope.purchaseOrder.city, $scope.purchaseOrderOriginal.city) &&
			isEqual($scope.purchaseOrder.country, $scope.purchaseOrderOriginal.country) &&
			isEqual($scope.purchaseOrder.discount, $scope.purchaseOrderOriginal.discount) &&
			isDatesEqual($scope.purchaseOrder.expectedAt, $scope.purchaseOrderOriginal.expectedAt) &&
			isEqual($scope.purchaseOrder.locationId, $scope.purchaseOrderOriginal.locationId) &&
			isEqual($scope.purchaseOrder.manualStatusSelection, $scope.purchaseOrderOriginal.manualStatusSelection) &&
			isEqual($scope.purchaseOrder.note, $scope.purchaseOrderOriginal.note) &&
			isDatesEqual($scope.purchaseOrder.orderedAt, $scope.purchaseOrderOriginal.orderedAt) &&
			isEqual($scope.purchaseOrder.orderedById, $scope.purchaseOrderOriginal.orderedById) &&
			isEqual($scope.purchaseOrder.phoneNumber, $scope.purchaseOrderOriginal.phoneNumber) &&
			isEqual($scope.purchaseOrder.poom, $scope.purchaseOrderOriginal.poom) &&
			isEqual($scope.purchaseOrder.referenceNumber, $scope.purchaseOrderOriginal.referenceNumber) &&
			isEqual($scope.purchaseOrder.salesTax, $scope.purchaseOrderOriginal.salesTax) &&
			isEqual($scope.purchaseOrder.shippingCharges, $scope.purchaseOrderOriginal.shippingCharges) &&
			isEqual($scope.purchaseOrder.state, $scope.purchaseOrderOriginal.state) &&
			isEqual($scope.purchaseOrder.status, $scope.purchaseOrderOriginal.status) &&
			isEqual($scope.purchaseOrder.streetAddress, $scope.purchaseOrderOriginal.streetAddress) &&
			isEqual($scope.purchaseOrder.streetAddress2, $scope.purchaseOrderOriginal.streetAddress2) &&
			isEqual($scope.purchaseOrder.subTotal, $scope.purchaseOrderOriginal.subTotal) &&
			isEqual($scope.purchaseOrder.thirdPartyLocationId, $scope.purchaseOrderOriginal.thirdPartyLocationId) &&
			isEqual($scope.purchaseOrder.thirdPartyShippingMethodId, $scope.purchaseOrderOriginal.thirdPartyShippingMethodId) &&
			isEqual($scope.purchaseOrder.totalCost, $scope.purchaseOrderOriginal.totalCost) &&
			isEqual($scope.purchaseOrder.vendorId, $scope.purchaseOrderOriginal.vendorId) &&
			isEqual($scope.purchaseOrder.webReference, $scope.purchaseOrderOriginal.webReference) &&
			isEqual($scope.purchaseOrder.zipcode, $scope.purchaseOrderOriginal.zipcode));
	};

	var loadInvoices = function () {
		$scope.invoicesByPoiIdMap.clear();
		PurchaseOrderInvoiceFactory.findByPurchaseOrderId({purchaseOrderId: $scope.purchaseOrderId})
			.$promise.then(function (response) {
			if (response && response.length) {
				$scope.invoicesOriginal = angular.copy(response);
				response.forEach(function (invoice) {
					invoice.invoiceDate = invoice.invoiceDate ? $moment(invoice.invoiceDate).toDate() : null;
					var poiId = invoice.purchaseOrderItemId ? invoice.purchaseOrderItemId : 0;
					if (!$scope.invoicesByPoiIdMap.get(poiId)) { $scope.invoicesByPoiIdMap.set(poiId, []); }
					$scope.invoicesByPoiIdMap.get(poiId).push(invoice);
				});
			} else {
				$scope.invoicesOriginal = [];
			}
		});
	};

	$scope.openPurchaseOrderInvoice = function (isPackingSlip = false) {
		var deliveryBranchId = '';
		if ($scope.delivery_location && $scope.delivery_location != 'other') {
			var objDeliveryLocation = JSON.parse($scope.delivery_location);
			if (objDeliveryLocation.id) {
				deliveryBranchId = objDeliveryLocation.id;
			}
		}

		if (isPackingSlip) {
			UtilService.openPrintScreen('purchase_order_invoice?purchaseOrderId=' + $scope.purchaseOrderId + '&deliveryBranchId=' + deliveryBranchId + '&isPackingSlip=true');
		} else {
			UtilService.openPrintScreen('purchase_order_invoice?purchaseOrderId=' + $scope.purchaseOrderId + '&deliveryBranchId=' + deliveryBranchId);
		}
	};

	// REGION: Purchase Order level Actions
	$scope.poBranchChanged = function () {
		if ($scope.purchaseOrder.branchId) {
			$scope.purchaseOrder.branch = $scope.branches.find(b => b.id == $scope.purchaseOrder.branchId);
		} else {
			$scope.purchaseOrder.branch = null;
		}
		// set all POI branches to PO-level branch
		if ($scope.purchaseOrder.singleBranch) {
			angular.forEach($scope.purchaseOrderItems, function (poi) {
				poi.branchId = $scope.purchaseOrder.branchId;
				poi.branch = $scope.purchaseOrder.branch;
			});
		}
	};

	$scope.poPatientChanged = function () {
		$scope.purchaseOrder.patientId = $scope.purchaseOrder.patient ? $scope.purchaseOrder.patient.id : null;
		$scope.purchaseOrder.prescription = null;
		$scope.purchaseOrder.prescriptionId = null;
		$scope.purchaseOrder.prescriptionLCode = null;
		$scope.purchaseOrder.prescriptionLCodeId = null;
		$scope.purchaseOrder.prescriptions = [];
		// get patient's prescriptions for the "Prescription" dropdown
		if ($scope.purchaseOrder.patient) {
			PrescriptionFactory.findByPatientIdActiveIsTrue({patientId: $scope.purchaseOrder.patientId}).$promise.then(function (prescriptions) {
				$scope.purchaseOrder.prescriptions = prescriptions;
				if ($scope.purchaseOrder.singlePatient) {
					angular.forEach($scope.purchaseOrderItems, function (poi) {
						poi.patient = $scope.purchaseOrder.patient;
						poi.patientId = $scope.purchaseOrder.patientId;
						poi.prescription = null;
						poi.prescriptionId = null;
						poi.prescriptionLCode = null;
						poi.prescriptionLCodeId = null;
						poi.prescriptions = $scope.purchaseOrder.prescriptions;
					});
				}
			});
		}
	};

	$scope.poPrescriptionChanged = function () {
		// I am clearing these first because filling in PLCs will be done asynchronously
		$scope.purchaseOrder.prescriptionLCode = null;
		$scope.purchaseOrder.prescriptionLCodeId = null;
		if ($scope.purchaseOrder.singlePatient) {
			angular.forEach($scope.purchaseOrderItems, function (poi) {
				poi.prescription = null;
				poi.prescriptionId = null;
				poi.prescriptionLCode = null;
				poi.prescriptionLCodeId = null;
				poi.practitioner = null;
				poi.practitionerId = null;
			});
		}

		if ($scope.purchaseOrder.prescriptionId) {
			$scope.purchaseOrder.prescription = $scope.purchaseOrder.prescriptions.find(p => p.id == $scope.purchaseOrder.prescriptionId);
			PrescriptionLCodeFactory.findByPrescriptionId({prescriptionId: $scope.purchaseOrder.prescriptionId, stripDown: true}).$promise.then(function (prescriptionLCodes) {
				$scope.purchaseOrder.prescription.prescriptionLCodes = prescriptionLCodes;
				if ($scope.purchaseOrder.singlePatient) {
					angular.forEach($scope.purchaseOrderItems, function (poi) {
						poi.prescription = $scope.purchaseOrder.prescription; // this should include prescriptionLCodes
						poi.prescriptionId = $scope.purchaseOrder.prescriptionId;
						poi.practitioner = $scope.purchaseOrder.prescription.treatingPractitioner;
						poi.practitionerId = $scope.purchaseOrder.prescription.treatingPractitionerId;
					});
				}
			});
		}
	};

	$scope.poPrescriptionLCodeChanged = function () {
		if ($scope.purchaseOrder.singlePatient) {
			angular.forEach($scope.purchaseOrderItems, function (poi) {
				poi.prescriptionLCode = $scope.purchaseOrder.prescriptionLCode;
				poi.prescriptionLCodeId = $scope.purchaseOrder.prescriptionLCodeId;
			});
		}
		if ($scope.purchaseOrder.prescriptionLCodeId) {
			$scope.purchaseOrder.prescriptionLCode = $scope.purchaseOrder.prescription.prescriptionLCodes
				.find(lc => lc.id == $scope.purchaseOrder.prescriptionLCodeId);
			if ($scope.purchaseOrder.singlePatient) {
				angular.forEach($scope.purchaseOrderItems, function (poi) {
					poi.prescriptionLCode = $scope.purchaseOrder.prescriptionLCode;
					poi.prescriptionLCodeId = $scope.purchaseOrder.prescriptionLCodeId;
				});
			}
		}
	};

	$scope.poPractitionerChanged = function () {
		if ($scope.purchaseOrder.singlePractitioner) {
			angular.forEach($scope.purchaseOrderItems, function (poi) {
				poi.practitioner = $scope.purchaseOrder.practitioner;
				poi.practitionerId = $scope.purchaseOrder.practitionerId;
			});
		}
	};

	/**
	 * Changing PO status will trigger change of POI statuses according to the rule
	 * encoded in $scope.poStatusesThatUpdate and $scope.poiStatusesThatGetUpdated
	 * Actual update of POI statuses is a very simple operation, except for the Inventory POs
	 * that require checking of available inventory levels.
	 * @param newPoStatus
	 * @param oldPoStatus
	 */
	$scope.poStatusChanged = function (newPoStatus, oldPoStatus) {
		if ($scope.poStatusesThatUpdate.includes(newPoStatus)) {
			$scope.purchaseOrderItems.forEach(function (poi, index) {
				if ($scope.poiStatusesThatGetUpdated.includes(poi.status)) {
					var oldPoiStatus = poi.status;
					if ($scope.inventoryStatuses.includes(newPoStatus)) {
						// Inventory order - update only if not already inventory
						if (!$scope.inventoryStatuses.includes(poi.status)) {
							poi.status = newPoStatus;
						}
 					} else if ($scope.purchaseOrder.voidStatuses.includes(newPoStatus)) {
						// Voided POI - update only if not already voided
						if (!$scope.purchaseOrder.voidStatuses.includes(poi.status)) {
							poi.status = newPoStatus;
						}
					} else if (poi.status != newPoStatus) {
						poi.status = newPoStatus;
					}

					if (oldPoiStatus != poi.status) {
						$scope.poiStatusChanged(index, newPoStatus, oldPoiStatus, true);
					}
				}
			});
		}
	};

	$scope.poVendorChanged = function () {
		if ($scope.purchaseOrder.vendor) {
			$scope.purchaseOrder.vendorId = $scope.purchaseOrder.vendor.id;
			if ($scope.singleVendor) {
				angular.forEach($scope.purchaseOrderItems, function (poi) {
					if ($scope.purchaseOrder.vendorId != poi.vendorId || !poi.item || $scope.purchaseOrder.vendorId != poi.item.vendorId) {
						poi.item = null;
						poi.itemId = null;
						poi.vendor = $scope.purchaseOrder.vendor;
						poi.vendorId = $scope.purchaseOrder.vendorId;
					}
				});
			}
		} else {
			$scope.purchaseOrder.vendorId = null;
		}
	};
	// ENDREGION: Purchase Order level Actions

	// REGION: Purchase Order Item level Actions
	$scope.poiBranchChanged = function (i) {
		if ($scope.purchaseOrderItems[i].branchId) {
			$scope.purchaseOrderItems[i].branch = $scope.branches.find(b => b.id == $scope.purchaseOrderItems[i].branchId);
		} else {
			$scope.purchaseOrderItems[i].branch = null;
		}
	};

	$scope.poiDisablePatient = function (i) {
		return $scope.purchaseOrder.singlePatient ||
			!$scope.userCanEditPurchaseOrder($scope.purchaseOrderItems[i]) ||
			($scope.purchaseOrderItems[i] != null && $scope.purchaseOrderItems[i].is_for_stock == 'true' && $scope.purchaseOrderItems[i].vendor != null && $scope.purchaseOrderItems[i].vendor.name == 'Empire') ||
			(($scope.purchaseOrderItems[i] == null || $scope.purchaseOrderItems[i].vendor == null || $scope.purchaseOrderItems[i].vendor.name == null || $scope.purchaseOrderItems[i].vendor.name != 'Empire') && ['inventory','ordered'].includes($scope.purchaseOrder.status));
	};

	$scope.poiDisablePrescription = function (i) {
		return ($scope.purchaseOrder.singlePatient && $scope.purchaseOrder.prescriptionId) ||
			!$scope.purchaseOrderItems[i].patientId ||
			!$scope.userCanEditPurchaseOrder($scope.purchaseOrderItems[i]) ||
			['inventory','ordered'].includes($scope.purchaseOrder.status);
	};

	$scope.poiDisablePrescriptionLCode = function (i) {
		return ($scope.purchaseOrder.singlePatient && $scope.purchaseOrder.prescriptionLCodeId) ||
			!$scope.purchaseOrderItems[i].prescriptionId ||
			!$scope.userCanEditPurchaseOrder($scope.purchaseOrderItems[i]) ||
			['inventory','ordered'].includes($scope.purchaseOrder.status);
	};

	/**
	 * In the case of "Single Vendor", all Items are from the same Vendor, so no need to propagate the "vendor change"
	 * @param index
	 */
	$scope.poiItemChanged = function (index) {
		$scope.purchaseOrderItems[index].cog = null;
		$scope.purchaseOrderItems[index].totalCost = null;
		$scope.purchaseOrderItems[index].unitOfMeasure = null;
		if ($scope.purchaseOrderItems[index].item) {
			// Leading spaces in the item name cause a bug whereby all fields get blanked out
			if ($scope.purchaseOrderItems[index].item.name.startsWith(' ')) {
				$scope.purchaseOrderItems[index].item.name = $scope.purchaseOrderItems[index].item.name.trim();
			}
			$scope.purchaseOrderItems[index].itemCost = Number($scope.purchaseOrderItems[index].item.price);
			$scope.purchaseOrderItems[index].itemId = $scope.purchaseOrderItems[index].item.id;
			$scope.purchaseOrderItems[index].patientSalePrice = $scope.purchaseOrderItems[index].item.patientSalePrice;
			$scope.purchaseOrderItems[index].quantity = 1;
			$scope.purchaseOrderItems[index].unitOfMeasure = $scope.purchaseOrderItems[index].item.unitOfMeasureQualifier == 'EA' ? 'Each' : null;
			$scope.purchaseOrderItems[index].vendor = $scope.purchaseOrderItems[index].item.vendor;
			$scope.purchaseOrderItems[index].vendorId = $scope.purchaseOrderItems[index].item.vendorId;
			/*$scope.purchaseOrderItems[index].item.LCodeCategoryId = $scope.purchaseOrderItems[index].item.categoryId;
			$scope.purchaseOrderItems[index].item.LCodeCategory = {
				id: $scope.purchaseOrderItems[index].item.categoryId,
				category: $scope.purchaseOrderItems[index].item.category
			};*/
		} else {
			$scope.purchaseOrderItems[index].itemCost = null;
			$scope.purchaseOrderItems[index].itemId = null;
			$scope.purchaseOrderItems[index].patientSalePrice = null;
			$scope.purchaseOrderItems[index].quantity = null;
			$scope.purchaseOrderItems[index].vendor = null;
			$scope.purchaseOrderItems[index].vendorId = null;
		}
		$scope.calculateTotals();
	};

	$scope.poiPatientChanged = function (index) {
		$scope.purchaseOrderItems[index].prescription = null;
		$scope.purchaseOrderItems[index].prescriptionId = null;
		$scope.purchaseOrderItems[index].prescriptionLCode = null;
		$scope.purchaseOrderItems[index].prescriptionLCodeId = null;
		// get patient's prescriptions for the "Prescription" dropdown
		if ($scope.purchaseOrderItems[index].patient) {
			$scope.purchaseOrderItems[index].patientId = $scope.purchaseOrderItems[index].patient.id;
			PrescriptionFactory.findByPatientIdActiveIsTrue({patientId: $scope.purchaseOrderItems[index].patientId}).$promise.then(function (prescriptions) {
				$scope.purchaseOrderItems[index].prescriptions = prescriptions;
			});
		} else {
			$scope.purchaseOrderItems[index].patientId = null;
			$scope.purchaseOrderItems[index].prescriptions = [];
		}
	};

	$scope.poiPrescriptionChanged = function (index) {
		if ($scope.purchaseOrderItems[index].prescriptionId) {
			$scope.purchaseOrderItems[index].prescription = $scope.purchaseOrderItems[index].prescriptions.find(p => p.id == $scope.purchaseOrderItems[index].prescriptionId);
			$scope.purchaseOrderItems[index].practitioner = $scope.purchaseOrderItems[index].prescription.treatingPractitioner;
			$scope.purchaseOrderItems[index].practitionerId = $scope.purchaseOrderItems[index].prescription.treatingPractitionerId;
			if ($scope.purchaseOrderItems[index].prescription) {
				PrescriptionLCodeFactory.findByPrescriptionId({prescriptionId: $scope.purchaseOrderItems[index].prescriptionId, stripDown: true}).$promise.then(function (prescriptionLCodes) {
					$scope.purchaseOrderItems[index].prescription.prescriptionLCodes = prescriptionLCodes;
				});
			}
		} else {
			$scope.purchaseOrderItems[index].prescription = null;
			$scope.purchaseOrderItems[index].practitioner = null;
			$scope.purchaseOrderItems[index].practitionerId = null;
		}
	};

	$scope.poiPrescriptionLCodeChanged = function (index) {
		if ($scope.purchaseOrderItems[index].prescriptionLCodeId) {
			$scope.purchaseOrderItems[index].prescriptionLCode = $scope.purchaseOrderItems[index].prescription.prescriptionLCodes
				.find(lc => lc.id == $scope.purchaseOrderItems[index].prescriptionLCodeId);
		}
	};

	$scope.poiQuantityChanged = function (index, newQuantity, oldQuantity) {
		if ($scope.purchaseOrderItems[index].quantity <= 1 && $scope.purchaseOrderItems[index].splitPurchaseOrderItems) {
			$scope.purchaseOrderItems[index].splitPurchaseOrderItems = false;
		}
		validatePoiChange(index, null, oldQuantity, false);
	};

	$scope.poiResetStatusAndQuantity = function (error, index, oldPoiStatus, oldQuantity, triggeredByPo) {
		$scope.purchaseOrderItems[index].quantity = Number(oldQuantity);
		$scope.purchaseOrderItems[index].status = oldPoiStatus;
		if (triggeredByPo) {
			$scope.purchaseOrder.status = 'purchase_order_error'
		}
		toastr.clear();
		toastr.error(error, 'Problem Setting Inventory Item', {
			timeOut: 5000,
			closeButton: true,
			closeHtml: '<i class="fa fa-times-circle"></i>'});
	}

	/**
	 * Check if the status has changed from valid to invalid or vice versa
	 * @param newPoiStatus
	 * @param oldPoiStatus
	 * @param index
	 */
	$scope.poiStatusChanged = function (index, newPoiStatus, oldPoiStatus, triggeredByPo) {
		validatePoiChange(index, oldPoiStatus, null, triggeredByPo);
		/* This code deletes invoices if the POI is cancelled
		if ($scope.purchaseOrder.voidStatuses.includes(newPoiStatus) && $scope.invoicesByPoiIdMap[index] && $scope.invoicesByPoiIdMap[index].length) {
			if ($scope.poInvoicesToDelete == null) {
				$scope.poInvoicesToDelete = [];
			}
			$scope.invoicesByPoiIdMap[index].forEach(function (invoice) {
				if (invoice.id > 0) {
					$scope.poInvoicesToDelete.push(invoice.id);
				}
			});
			$scope.invoicesByPoiIdMap[index] = [];
		} */
	};

	/**
	 * In the case of "Single Vendor", all Items are from the same Vendor, so no need to propagate the "change"
	 * @param index
	 */
	$scope.poiVendorChanged = function (index) {
		if ($scope.purchaseOrderItems[index].item.vendor) {
			if ($scope.purchaseOrderItems[index].item.vendor.id != $scope.purchaseOrderItems[index].vendorId) {
				$scope.purchaseOrderItems[index].vendorId = $scope.purchaseOrderItems[index].item.vendor.id;
			}
			$scope.purchaseOrderItems[index].vendor = $scope.purchaseOrderItems[index].item.vendor;
			$scope.purchaseOrderItems[index].vendorId = $scope.purchaseOrderItems[index].item.vendorId;
		} else {
			$scope.purchaseOrderItems[index].vendor = null;
			$scope.purchaseOrderItems[index].vendorId = null;
		}
	};
	// ENDREGION: Purchase Order Item level Actions

	$scope.refreshLCodes = function (input) {
		return LCodeFactory.search({q: input, c: null, active: true}).$promise.then(function (response) {
			return response;
		});
	};

	$scope.roundMoney = function (moneyFloat) {
		return Math.round(moneyFloat * 100) / 100;
	};

	$scope.save = function (form) {
		$scope.submitted = true;
		if (isAllPristine()) {
			toastr.clear();
			toastr.error('No changes detected', 'Purchase Order Not Saved', {
				timeOut: 5000,
				closeButton: true,
				closeHtml: '<i class="fa fa-times-circle"></i>'
			});
			$scope.submitted = false;
			return;
		}
		if (!$scope.validatePO(form)) {
			return;
		}

		$scope.loading = true;
		// need this for after save
		var isNewOrder = !$scope.purchaseOrderId;

		trimPurchaseOrder();
		//
		var purchaseOrderInvoices = [];
		// When retrieved from invoicesByPoiIdMap as its key, poiId is either poi.id or 0 in case of PO-level invoices
		$scope.invoicesByPoiIdMap.forEach((poiInvoices, poiId) => {
			var poiModifiedInvoices = poiInvoices.filter(invoice => !isInvoicePristine(invoice));
			if (poiModifiedInvoices && poiModifiedInvoices.length) {
				poiModifiedInvoices.forEach(function (poiInvoice) {
					purchaseOrderInvoices.push(poiInvoice);
				});
			}
		});
		if (purchaseOrderInvoices.length) {
			$scope.purchaseOrder.purchaseOrderInvoices = purchaseOrderInvoices;
		} else {
			delete $scope.purchaseOrder.purchaseOrderInvoices;
		}
		//
		$scope.purchaseOrder.purchaseOrderItems = trimPurchaseOrderItems();
		var params = {
			invoiceIdsToDelete: $scope.poInvoicesToDelete,
			originalPoItemIds: $scope.purchaseOrderItemsOriginal.map(poi => poi.id),
			poItemIdsToDelete: $scope.poItemsToDelete
		};
		PurchaseOrderFactory.savePurchaseOrder(params, $scope.purchaseOrder).$promise.then(function (savedPO) {
			if (isNewOrder) $stateParams.purchaseOrderId = savedPO.id;
			$scope.poInvoicesToDelete = [];
			$scope.poItemsToDelete = [];
			PurchaseOrderItemFactory.findByPurchaseOrderId({purchaseOrderId: $stateParams.purchaseOrderId}).$promise.then(function (purchaseOrderItems) {
				$scope.purchaseOrderItemsOriginal = angular.copy(purchaseOrderItems);
				$scope.purchaseOrderItems = purchaseOrderItems;
				$scope.originalPurchaseOrderItems = angular.copy($scope.purchaseOrderItems);
				toastr.clear();
				var messageHeader = (isNewOrder ? 'Created new' : 'Updated existing') + ' Purchase Order #' + savedPO.id;
				toastr.success(savedPO.message, messageHeader, {
					timeOut: 5000,
					closeButton: true,
					closeHtml: '<i class="fa fa-times-circle"></i>'
				});
				$scope.init();
			});
		}, function (error) {
			console.error(JSON.stringify(error));
			UtilService.displayAlert('danger', '<p>There has been an error saving the purchase order</p>', '#purchase-order-alert-container');
			$scope.loading = false;
			$scope.submitted = false;
		});
	};

	var setEmpireShippingAddress = function (empireShippingAddress) {
		var empireShippingAddressArray = $stateParams.shipping_address.split(',');
		$scope.purchaseOrder.attn = empireShippingAddressArray[0].split(':')[1];
		$scope.purchaseOrder.city = empireShippingAddressArray[1].split(':')[1];
		$scope.purchaseOrder.state = empireShippingAddressArray[2].split(':')[1].toUpperCase();
		var key;
		for (key in $scope.states) {
			if ($scope.states[key] === $scope.purchaseOrder.state) {
				$scope.purchaseOrder.state = key;
				break;
			}
		}
		$scope.purchaseOrder.zipcode = empireShippingAddressArray[3].split(':')[1];
		$scope.purchaseOrder.streetAddress = empireShippingAddressArray[4].split(':')[1];
		$scope.purchaseOrder.streetAddress2 = null;
		$scope.purchaseOrder.country = 'US';
	};

	var setPurchaseOrderVendorId = function (purchaseOrderItems, vendorId) {
		var setVendorId = true;
		angular.forEach(purchaseOrderItems, function (poi) {
			if (poi.vendorId !== undefined && poi.vendorId !== vendorId) {
				setVendorId = false;
			}
		});
		return setVendorId ? vendorId : null;
	};

	$scope.showPOOM = function () {
		$scope.showXML = !$scope.showXML;
	};

	// REGION: Purchase Order level checkboxes
	$scope.singleBranchChanged = function () {
		if ($scope.purchaseOrder.singleBranch) {
			if ($scope.purchaseOrderItems[0].branchId) {
				$scope.purchaseOrder.branchId = $scope.purchaseOrderItems[0].branchId;
				$scope.poBranchChanged();
			} else {
				$scope.purchaseOrder.branch = null;
				$scope.purchaseOrder.branchId = null;
			}
		}
	};

	$scope.singlePatientChanged = function () {
		$scope.purchaseOrder.prescriptions = [];
		if ($scope.purchaseOrder.singlePatient) {
			if ($scope.purchaseOrderItems[0].patient) { // if patient is selected
				$scope.purchaseOrder.patient = $scope.purchaseOrderItems[0].patient;
				$scope.purchaseOrder.patientId = $scope.purchaseOrderItems[0].patientId;
				var prescriptionIds = [...new Set($scope.purchaseOrderItems.map(poi => poi.prescriptionId))];
				if (prescriptionIds.length == 1 && prescriptionIds[0]) {
					$scope.purchaseOrder.prescription = $scope.purchaseOrderItems[0].prescription;
					$scope.purchaseOrder.prescriptionId = $scope.purchaseOrderItems[0].prescriptionId;
					var prescriptionLCodeIds = [...new Set($scope.purchaseOrderItems.map(poi => poi.prescriptionLCodeId))];
					if (prescriptionLCodeIds.length == 1 && prescriptionLCodeIds[0]) {
						$scope.purchaseOrder.prescriptionLCode = $scope.purchaseOrderItems[0].prescriptionLCode;
						$scope.purchaseOrder.prescriptionLCodeId = $scope.purchaseOrderItems[0].prescriptionLCodeId;
					}
				} else {
					$scope.purchaseOrder.prescription = null;
					$scope.purchaseOrder.prescriptionId = null;
					$scope.purchaseOrder.prescriptionLCode = null;
					$scope.purchaseOrder.prescriptionLCodeId = null;
				}
				$scope.purchaseOrder.prescriptions = $scope.purchaseOrderItems[0].prescriptions;
				angular.forEach($scope.purchaseOrderItems, function (poi) {
					if (poi.patientId != $scope.purchaseOrder.patientId) {
						poi.patient = $scope.purchaseOrder.patient;
						poi.patientId = $scope.purchaseOrder.patientId;
						poi.prescription = null;
						poi.prescriptionId = null;
						poi.prescriptionLCode = null;
						poi.prescriptionLCodeId = null;
						poi.prescriptions = $scope.purchaseOrder.prescriptions;
					}
				});
			}
		} else {
			$scope.purchaseOrder.patient = null;
			$scope.purchaseOrder.patientId = null;
			$scope.purchaseOrder.prescription = null;
			$scope.purchaseOrder.prescriptionId = null;
			$scope.purchaseOrder.prescriptionLCode = null;
			$scope.purchaseOrder.prescriptions = [];
		}
	};

	$scope.singlePractitionerChanged = function () {
		if ($scope.purchaseOrder.singlePractitioner) {
			if ($scope.purchaseOrderItems[0].practitionerId) {
				$scope.purchaseOrder.practitioner = $scope.purchaseOrderItems[0].practitioner;
				$scope.purchaseOrder.practitionerId = $scope.purchaseOrderItems[0].practitionerId;
			} else {
				$scope.purchaseOrder.practitioner = null;
				$scope.purchaseOrder.practitionerId = null;
			}
			$scope.poPractitionerChanged();
		} else {
			$scope.purchaseOrder.practitioner = null;
			$scope.purchaseOrder.practitionerId = null;
		}
	};

	$scope.singleVendorChanged = function () {
		if ($scope.purchaseOrder.singleVendor) {
			if ($scope.purchaseOrderItems[0].vendor) {
				$scope.purchaseOrder.vendor = $scope.purchaseOrderItems[0].vendor;
				$scope.purchaseOrder.vendorId = $scope.purchaseOrderItems[0].vendor.id;
				$scope.poVendorChanged();
			}
		} else {
			$scope.purchaseOrder.vendor = null;
			$scope.purchaseOrder.vendorId = null;
		}
	};
	// ENDREGION: Purchase Order level checkboxes

	/**
	 * The API is very finicky about the size and depth of the JSON object,
	 * so I am removing all of its fields that are null or nested objects.
	 */
	var trimPurchaseOrder = function () {
		if (!$scope.purchaseOrder.additionalCharges) delete $scope.purchaseOrder.additionalCharges;
		if (!$scope.purchaseOrder.attn) delete $scope.purchaseOrder.attn;
		delete $scope.purchaseOrder.cog;
		if (!$scope.purchaseOrder.city) delete $scope.purchaseOrder.city;
		if (!$scope.purchaseOrder.country) delete $scope.purchaseOrder.country;
		if (!$scope.purchaseOrder.createdAt) delete $scope.purchaseOrder.createdAt;
		if (!$scope.purchaseOrder.discount) delete $scope.purchaseOrder.discount;
		if (!$scope.purchaseOrder.expectedAt) delete $scope.purchaseOrder.expectedAt;
		if (!$scope.purchaseOrder.locationId) delete $scope.purchaseOrder.locationId;
		delete $scope.purchaseOrder.message;
		if (!$scope.purchaseOrder.note) delete $scope.purchaseOrder.note;
		if (!$scope.purchaseOrder.orderedAt) delete $scope.purchaseOrder.orderedAt;
		delete $scope.purchaseOrder.orderedBy;
		if (!$scope.purchaseOrder.orderedById) delete $scope.purchaseOrder.orderedById;
		delete $scope.purchaseOrder.patient;
		delete $scope.purchaseOrder.patientId;
		if (!$scope.purchaseOrder.phoneNumber) delete $scope.purchaseOrder.phoneNumber;
		if (!$scope.purchaseOrder.poom) delete $scope.purchaseOrder.poom;
		delete $scope.purchaseOrder.prescription;
		delete $scope.purchaseOrder.prescriptionId;
		if (!$scope.purchaseOrder.referenceNumber) delete $scope.purchaseOrder.referenceNumber;
		if (!$scope.purchaseOrder.salesTax) delete $scope.purchaseOrder.salesTax;
		if (!$scope.purchaseOrder.shippingCharges) delete $scope.purchaseOrder.shippingCharges;
		delete $scope.purchaseOrder.singleBranch;
		delete $scope.purchaseOrder.singlePatient;
		delete $scope.purchaseOrder.singlePractitioner;
		delete $scope.purchaseOrder.singleVendor;
		if (!$scope.purchaseOrder.state) delete $scope.purchaseOrder.state;
		if (!$scope.purchaseOrder.streetAddress) delete $scope.purchaseOrder.streetAddress;
		if (!$scope.purchaseOrder.streetAddress2) delete $scope.purchaseOrder.streetAddress2;
		if (!$scope.purchaseOrder.thirdPartyLocationId) delete $scope.purchaseOrder.thirdPartyLocationId;
		delete $scope.purchaseOrder.thirdPartyShippingMethod;
		if (!$scope.purchaseOrder.thirdPartyShippingMethodId) delete $scope.purchaseOrder.thirdPartyShippingMethodId;
		if (!$scope.purchaseOrder.vendorId) delete $scope.purchaseOrder.vendorId;
		delete $scope.purchaseOrder.vendor;
		delete $scope.purchaseOrder.voidStatuses;
		if (!$scope.purchaseOrder.webReference) delete $scope.purchaseOrder.webReference;
		if (!$scope.purchaseOrder.zipcode) delete $scope.purchaseOrder.zipcode;
	}

	/**
	 * The API is very finicky about the size and depth of the JSON object,
	 * so I am removing from every POI all fields that are null or nested objects.
	 */
	var trimPurchaseOrderItems = function () {
		var poItemsToSave = [];
		$scope.purchaseOrderItems.forEach(function (poi) {
			if (!isPoItemPristine(poi)) {
				delete poi.$additionalCharges;
				delete poi.$discount;
				delete poi.$salesTax;
				delete poi.$shippingCharges;
				if (!poi.additionalComments) delete poi.additionalComments;
				if (!poi.approved) delete poi.approved;
				delete poi.branch;
				delete poi.cog;
				if (!poi.dateExpected) delete poi.dateExpected;
				if (!poi.dateNeeded) delete poi.dateNeeded;
				if (!poi.description) delete poi.description;
				if (!poi.discount) delete poi.discount;
				if (!poi.discountedPrice) delete poi.discountedPrice;
				delete poi.item;
				if (!poi.name) delete poi.name;
				if (!poi.parentId) delete poi.parentId;
				if (!poi.partNumber) delete poi.partNumber;
				delete poi.patient;
				if (!poi.patientId) delete poi.patientId;
				if (!poi.patientSalePrice) delete poi.patientSalePrice;
				delete poi.practitioner;
				if (!poi.practitionerId) delete poi.practitionerId;
				delete poi.prescription;
				if (!poi.prescriptionId) delete poi.prescriptionId;
				delete poi.prescriptions;
				delete poi.prescriptionLCode;
				delete poi.purchaseOrder;
				// TODO: maybe I need to reconsider this
				delete poi.purchaseOrderInvoices;
				if (!poi.prescriptionLCodeId) delete poi.prescriptionLCodeId;
				if (!poi.rma) delete poi.rma;
				if (!poi.serialNumber) delete poi.serialNumber;
				if (!poi.showOnDelivery) delete poi.showOnDelivery;
				if (!poi.sku) delete poi.sku;
				if (!poi.splitPurchaseOrderItems) delete poi.splitPurchaseOrderItems;
				if (!poi.stocked) delete poi.stocked;
				if (!poi.thirdPartyLocationId) delete poi.thirdPartyLocationId;
				delete poi.thirdPartyShippingMethod;
				if (!poi.thirdPartyShippingMethodId) delete poi.thirdPartyShippingMethodId;
				if (!poi.trackingNumbers) delete poi.trackingNumbers;
				if (!poi.type) delete poi.type;
				if (!poi.unitOfMeasure) delete poi.unitOfMeasure;
				if (!poi.vendorId) delete poi.vendorId;
				if (!poi.warrantyInfo) delete poi.warrantyInfo;
				if (!poi.trackingNumbers) delete poi.trackingNumbers;
				if (!poi.trackingNumbers) delete poi.trackingNumbers;
				delete poi.vendor;
				poItemsToSave.push(poi);
			}
		});
		return poItemsToSave;
	}

	$scope.updateDeliveryLocation = function (isCascade, selected) {
		$scope.delivery_location = selected;
		$scope.purchaseOrder.thirdPartyLocationId = undefined;
		if (isCascade && selected) {
			$scope.purchaseOrder.attn = "";
			$scope.purchaseOrder.thirdPartyLocationId = selected.id;
			$scope.purchaseOrder.attn = selected.name;
			$scope.purchaseOrder.streetAddress = selected.shippingAddress1;
			$scope.purchaseOrder.streetAddress2 = selected.shippingAddress2;
			$scope.purchaseOrder.city = selected.shippingCity;
			$scope.purchaseOrder.state = selected.shippingState;
			$scope.purchaseOrder.zipcode = selected.shippingPostalCode;
			$scope.purchaseOrder.country = selected.shippingCountry;
			$scope.purchaseOrder.phoneNumber = selected.phone;
			$scope.purchaseOrder.locationId = selected.preferredLocationId;
		} else if ((selected === 'primary_branch' && $scope.purchaseOrder.patient) ||
			(selected === 'prescription_branch' && !$scope.purchaseOrder.prescriptionId)) {
			var primaryBranch = $scope.purchaseOrder.patient.primaryBranch;
			$scope.purchaseOrder.attn = "";
			$scope.purchaseOrder.streetAddress = primaryBranch.streetAddress;
			$scope.purchaseOrder.streetAddress2 = null;
			$scope.purchaseOrder.city = primaryBranch.city;
			$scope.purchaseOrder.state = primaryBranch.state;
			$scope.purchaseOrder.zipcode = primaryBranch.zipcode;
			$scope.purchaseOrder.country = 'US';
		} else if (selected === 'prescription_branch' && $scope.purchaseOrder.prescriptionId && $scope.purchaseOrder.prescriptions.length > 0) {
			var prescription = $scope.purchaseOrder.prescriptions[0];
			var prescriptionBranch = prescription.branch;
			$scope.purchaseOrder.attn = "";
			$scope.purchaseOrder.streetAddress = prescriptionBranch.streetAddress;
			$scope.purchaseOrder.streetAddress2 = null;
			$scope.purchaseOrder.city = prescriptionBranch.city;
			$scope.purchaseOrder.state = prescriptionBranch.state;
			$scope.purchaseOrder.zipcode = prescriptionBranch.zipcode;
			$scope.purchaseOrder.country = 'US';
		} else if (selected === 'patient_address' && $scope.purchaseOrder.patient) {
			$scope.purchaseOrder.attn = UtilService.formatName($scope.purchaseOrder.patient, 'FL');
			$scope.purchaseOrder.streetAddress = $scope.purchaseOrder.patient.streetAddress;
			$scope.purchaseOrder.streetAddress2 = $scope.purchaseOrder.patient.streetAddress_line2;
			$scope.purchaseOrder.city = $scope.purchaseOrder.patient.city;
			$scope.purchaseOrder.state = $scope.purchaseOrder.patient.state;
			$scope.purchaseOrder.zipcode = $scope.purchaseOrder.patient.zipcode;
			$scope.purchaseOrder.country = 'US';
		} else if (selected === 'patient_alternate_address' && $scope.purchaseOrder.patient.streetAddress2) {
			$scope.purchaseOrder.attn = UtilService.formatName($scope.purchaseOrder.patient, 'FL');
			$scope.purchaseOrder.streetAddress = $scope.purchaseOrder.patient.streetAddress2;
			$scope.purchaseOrder.streetAddress2 = $scope.purchaseOrder.patient.streetAddress2_line2;
			$scope.purchaseOrder.city = $scope.purchaseOrder.patient.city2;
			$scope.purchaseOrder.state = $scope.purchaseOrder.patient.state2;
			$scope.purchaseOrder.zipcode = $scope.purchaseOrder.patient.zipcode2;
			$scope.purchaseOrder.country = 'US';
		} else if (selected === 'other' || selected === 'shipping_branch') {
			$scope.purchaseOrder.attn = "";
			$scope.purchaseOrder.streetAddress = "";
			$scope.purchaseOrder.city = "";
			$scope.purchaseOrder.state = "";
			$scope.purchaseOrder.zipcode = "";
			$scope.purchaseOrder.country = 'US';
		} else {
			var b = JSON.parse(selected);
			$scope.purchaseOrder.attn = "";
			$scope.purchaseOrder.streetAddress = b.streetAddress;
			if (b.streetAddress2) {
				$scope.purchaseOrder.streetAddress2 = b.streetAddress2;
			} else {
				$scope.purchaseOrder.streetAddress2 = null;
			}
			$scope.purchaseOrder.city = b.city;
			$scope.purchaseOrder.state = b.state;
			$scope.purchaseOrder.zipcode = b.zipcode;
			$scope.purchaseOrder.country = 'US';
		}
	};

	/* REGION ItemPhysical management */
	/**
	 * Function validatePoiChange() retrieves physical items from the database
	 * @param index - index of $scope.purchaseOrderItems, there is no need to validate if it is within bounds
	 * @param validateQuantity - during init(), this function may execute before ItemService.findItemPhysicalByPurchaseOrderId()
	 * thereby giving an incomplete count, I call this function with validateQuantity = false from init()
	 */
	var validatePoiChange = function (index, oldPoiStatus, oldQuantity, triggeredByPo) {
		var changeSummary = null;
		// these two parameters are reciprocal, if one is changed, then the other is not
		if (!oldPoiStatus) {
			changeSummary = ' quantity to ' + $scope.purchaseOrderItems[index].quantity;
			oldPoiStatus = $scope.purchaseOrderItems[index].status;
		}
		if (!oldQuantity) {
			changeSummary = ' status to "' + $scope.purchaseOrderItems[index].status + '"';
			oldQuantity = $scope.purchaseOrderItems[index].quantity;
		}

		var error = null;
		var poi = $scope.purchaseOrderItems[index];
		if (poi == null) {
			error = 'PO Item ' + (index + 1) + ' does not exist';
		} else if (poi.status == 'partially_received') {
			error = 'Status "Partially Received" is not yet implemented at the PO Item level';
		} else if (!$scope.inventoryStatuses.includes(poi.status)
			&& $scope.inventoryStatuses.includes(oldPoiStatus)
			&& !$scope.purchaseOrder.voidStatuses.includes(poi.status)) {
			// changing from inventory to non-inventory is not allowed
			error = 'Cannot convert inventory PO Item ' + (index + 1) + ' to non-inventory';
		} else if ($scope.inventoryStatuses.includes(poi.status)
			&& !$scope.inventoryStatuses.includes(oldPoiStatus)) {
			var prefix = 'Cannot set PO Item ' + (index + 1) + ' status to "' + poi.status + ' without a';
			if ('received' == poi.status) {
				error = 'Cannot convert received PO Item ' + (index + 1) + ' to inventory';
			} else if (!poi.branchId) {
				error = prefix + ' Branch';
			} else if (!poi.item) {
				error = prefix + 'n Item';
			} else if (!poi.prescriptionId) {
				error = prefix + ' Prescription';
			}
		}

		if (error) {
			$scope.poiResetStatusAndQuantity(error, index, oldPoiStatus, oldQuantity, triggeredByPo);
		} else {
			var originalQuantity = Number($scope.physicalItemCountsByPoiId[poi.id]);
			if (originalQuantity == null || isNaN(originalQuantity)) originalQuantity = 0;
			if ($scope.inventoryStatuses.includes(poi.status) && originalQuantity < poi.quantity) {
				$scope.searching = true;
				// make sure that there are enough physical items to fulfill the purchase order item
				ItemService.findItemPhysicalByBranchIdAndItemId(poi.branchId, poi.itemId, poi.quantity, 'inventory').then(function (response) {
					var additionalQuantity = response ? response.length : 0;
					var newQuantity = originalQuantity + additionalQuantity;
					if (newQuantity < poi.quantity) {
						error = 'Cannot set PO Item ' + (index + 1) + changeSummary +
							' because there are not enough Physical Items of "' + poi.item.name + '" at "' + poi.branch.name +
							'" (requested ' + poi.quantity + ', available ' + newQuantity + ')';
						$scope.poiResetStatusAndQuantity(error, index, oldPoiStatus, oldQuantity, triggeredByPo);
					} else {
						$scope.physicalItemCountsByPoiId[poi.id] = newQuantity;
						$scope.calculateTotals();
					}
					$scope.searching = false;
				});
			} else {
				$scope.physicalItemCountsByPoiId[poi.id] = poi.quantity;
				$scope.calculateTotals();
			}
		}
	};

	/**
	 * I no longer load actual physical items into PO, these are only counts of physical items in "inventory" POIs
	 */
	var loadPhysicalItems = function () {
		ItemService.getItemPhysicalCountsByPoiId($scope.purchaseOrderId).then(function (response) {
			$scope.physicalItemCountsByPoiId = response;
		});
	};
	/* ENDREGION ItemPhysical management */

	$scope.updateStatusForRMA = function (purchaseOrderItem) {
		if (purchaseOrderItem.rma && purchaseOrderItem.rma != "") {
			purchaseOrderItem.status = 'returned';
		}
	};

	$scope.userCanEditPurchaseOrder = function (purchaseOrderItem) {
		return ($scope.hasPermission('purchase_order_add') && $scope.purchaseOrder != null && $scope.purchaseOrder.id == null) ||
			($scope.hasPermission('purchase_order_add') && purchaseOrderItem != null && purchaseOrderItem.id == null) ||
			($scope.hasPermission('purchase_order_edit') && $scope.purchaseOrder != null && $scope.purchaseOrder.id != null);
	};

	$scope.validatePO = function (form) {
		if (form.$invalid) {
			$scope.loading = false;
			UtilService.displayAlert('danger', '<p>There are missing values in required fields.</p>', '#header-alert-container');
			window.scrollTo(0, 0);
			return false;
		}

		$scope.purchaseOrder.orderedAt = $scope.purchaseOrder.orderedAt ? $moment($scope.purchaseOrder.orderedAt, 'MM/DD/YYYY').format('YYYY-MM-DD') : null;
		if ($scope.purchaseOrder.expectedAt === '' || $scope.purchaseOrder.expectedAt === 'Invalid date' || !$scope.purchaseOrder.expectedAt) {
			$scope.purchaseOrder.expectedAt = null;
		} else {
			$scope.purchaseOrder.expectedAt = $moment($scope.purchaseOrder.expectedAt, 'MM/DD/YYYY').format('YYYY-MM-DD');
		}
		var vendorId = null;
		if ($scope.purchaseOrderItems.length > 0 && $scope.purchaseOrderItems[0] !== null && $scope.purchaseOrderItems[0].vendorId != null) {
			vendorId = $scope.purchaseOrderItems[0].vendorId;
		}
		if (vendorId !== null && !$scope.purchaseOrder.singleVendor) {
			var commonVendorId = setPurchaseOrderVendorId($scope.purchaseOrderItems, vendorId);
			if (commonVendorId && !$scope.purchaseOrder.vendorId && confirm("The Items selected are all from the same vendor.  Select \"OK\" to save as a single vendor PO, or select \"Cancel\" to save as a multi-vendor PO.")) {
				$scope.purchaseOrder.vendorId = commonVendorId;
			}
		}
		if ($scope.purchaseOrder.address && !$scope.purchaseOrder.address.country) {
			$scope.purchaseOrder.address.country = "US";
		}
		$scope.purchaseOrder.status = getPoStatus();
		return true;
	};

	$rootScope.$on('item-added', function (event, args) {
		if (args && args.item) {
			var index = $scope.newItemIndex >= 0 ? $scope.newItemIndex : $scope.purchaseOrderItems.length - 1;
			$scope.purchaseOrderItems[index].item = args.item;
			$scope.poiItemChanged(index);
		}
		$scope.newItemIndex = null;
	});

	$scope.$on('item-searched', function (event, args) {
		var index = args.position;
		if ($scope.purchaseOrderItems[index] === null) {
			$scope.purchaseOrderItems[index] = {};
		}
		$scope.purchaseOrderItems[index].item = args.item;
		$scope.purchaseOrderItems[index].itemId = args.item.id;
		$scope.purchaseOrderItems[index].vendorId = args.item.vendorId;
		$scope.purchaseOrderItems[index].itemCost = Number(args.item.price);
		$scope.purchaseOrderItems[index].quantity = 1;
		$scope.purchaseOrderItems[index].totalCost = undefined;
		$scope.purchaseOrderItems[index].cog = undefined;
		$scope.purchaseOrderItems[index].patientSalePrice = args.item.patientSalePrice;
		if (args.item.vendor && !$scope.purchaseOrder.singleVendor) {
			$scope.selectVendor(args.item.vendor, undefined, undefined, index);
		}
		$scope.calculateTotals();
	});

	$rootScope.$on('setCurrentBranchSessionService', function () {
		$scope.currentBranch = SessionService.getCurrentBranch();
		if ($scope.purchaseOrder && $scope.purchaseOrder.id === undefined) {
			$scope.purchaseOrder.streetAddress = $scope.currentBranch.streetAddress;
			if ($scope.currentBranch.streetAddress2) {
				$scope.purchaseOrder.streetAddress2 = $scope.currentBranch.streetAddress2;
			} else {
				$scope.purchaseOrder.streetAddress2 = null;
			}
			$scope.purchaseOrder.city = $scope.currentBranch.city;
			$scope.purchaseOrder.state = $scope.currentBranch.state;
			$scope.purchaseOrder.zipcode = $scope.currentBranch.zipcode;
			$scope.purchaseOrder.country = $scope.currentBranch.country;
		}
	});

	$scope.$watch('shipping.branch', function (newValue, oldValue) {
		if (newValue) {
			$scope.purchaseOrder.streetAddress = newValue.streetAddress;
			if (newValue.streetAddress2) {
				$scope.purchaseOrder.streetAddress2 = newValue.streetAddress2;
			} else {
				$scope.purchaseOrder.streetAddress2 = null;
			}
			$scope.purchaseOrder.city = newValue.city;
			$scope.purchaseOrder.state = newValue.state;
			$scope.purchaseOrder.zipcode = newValue.zipcode;
			$scope.purchaseOrder.country = 'US';
		}
	});

	$scope.$on('vendor-added', function (event, args) {
		VendorFactory.search().$promise.then(function (vendors) {
			$scope.vendors = vendors;
		});
	});

	$scope.init();

	$timeout(function () {
		if ($scope.purchaseOrder && $scope.purchaseOrder.status && $scope.purchaseOrder.status === 'ordered') {
			$('#cascade_location').prop('disabled', true).trigger("chosen:updated");
			$('#shipping_method').prop('disabled', true).trigger("chosen:updated");
			$('.chosen-select-prescription').prop('disabled', true).trigger("chosen:updated");
			$('.chosen-select-branch').prop('disabled', true).trigger("chosen:updated");
		}
	}, 1000);
}
