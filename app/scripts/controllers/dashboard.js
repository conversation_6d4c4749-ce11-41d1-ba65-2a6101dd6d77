'use strict';
app.controller('DashboardCtrl', DashboardCtrl);
DashboardCtrl.$inject = ['$rootScope', '$scope', '$stateParams', '$http', '$cookies', '$uibModal', '$moment', 'DateService', 'AppointmentFactory', 'AppointmentStatusFactory', 'AppointmentTypeFactory', 'PrescriptionFactory', 'PrescriptionSectionFactory', 'VisitTypesFactory', 'UserGroupsFactory', 'TaskFactory', 'UserService', 'ClaimFactory', 'UserBranchFactory', 'BranchService',  'ReportFactory', 'SessionService', 'SystemSettingFactory'];

function DashboardCtrl($rootScope, $scope, $stateParams, $http, $cookies, $uibModal, $moment, DateService, AppointmentFactory, AppointmentStatusFactory, AppointmentTypeFactory, PrescriptionFactory, PrescriptionSectionFactory, VisitTypesFactory, UserGroupsFactory, TaskFactory, UserService, ClaimFactory, UserBranchFactory, BranchService,  ReportFactory, SessionService, SystemSettingFactory) {
  $rootScope.page = {
    title: 'Dashboard',
    subtitle: ''
  };

  $scope.branchService = BranchService;
  $scope.userBranchIds = [];
	$scope.userBranches = [];

  $scope.moment = $moment;
  $scope.userService = UserService;
	$scope.dateService = DateService;
	//$scope.reportService = ReportService;

	$scope.appointmentTypes = AppointmentTypeFactory.query();
	$scope.currentUserId = UserService.getCurrentUserId();

	$scope.patientCount = 0;
	$scope.appointmentCount = 0;
	$scope.currentUserWidgets = [];
	$scope.upcomingAppointments = [];
	$scope.missingNotes = [];
	$scope.wips = [];
	$scope.showWip = false;
	$scope.tasks = [];
  $scope.projectedVsBilled = [];
  $scope.projectedVsBilledBranchId = null;
  $scope.estimatedMonthlyDeliveryLoading = false;
	$scope.tasksLoading = false;
	$scope.noActivityOnClaims = [];
	$scope.noActivityOnClaimsBranchId = "";
	$scope.releaseNoteShown = false;

  $scope.getPatientCount = function () {
    var url = 'api/dashboard/patient/count/branch/' + $rootScope.branchId;
		$http.get(url).then(function (response) {
			$scope.patientCount = response.data;
		});
	};

	$scope.getAppointmentCount = function () {
		var startOfTodayISOString = $moment().startOf('day').toISOString();
		var url = 'api/dashboard/appointment/count/branch/' + $rootScope.branchId + '/startDateTime/' + startOfTodayISOString;
		$http.get(url).then(function (response) {
			$scope.appointmentCount = response.data;
		});
	};

	// ---- Widgets ----

	$scope.getUpcomingAppointments = function () {
		$scope.upcomingAppointments = [];
		AppointmentFactory.getUpcomingAppointmentsDto({userId: $scope.currentUserId}).$promise.then(function (response) {
			$scope.upcomingAppointments = response;
		});
	};

	$scope.getMissingNotes = function () {
		$scope.notesLoading = true;
		AppointmentFactory.findDashboardMissingNotesDto({userId: $scope.currentUserId,}).$promise.then(function (response) {
			$scope.missingNotes = response;
			$scope.notesLoading = false;
		});
	};

  $scope.getNoActivityOnClaims = function (branchId) {
  	$scope.noActivityOnClaims = [];
    ClaimFactory.findClaimsWithNoActivityDashboard({branchId: branchId ? branchId : ""}).$promise.then(function (response) {
      $scope.noActivityOnClaims = response;
    });
  };

	$scope.getWips = function (range) {
		$scope.wips = [];
		$scope.wipsLoading = true;
		PrescriptionFactory.getDashboardPatientSummaryDto({
			userId: $scope.currentUserId,
			range: range
		}).$promise.then(function (response) {
			$scope.wips = response;
			$scope.wipsLoading = false;
		});
		$scope.showWip = true;
	};

	$scope.getTasks = function () {
		$scope.tasksLoading = true;
		$scope.tasks = [];
		var startDate = $moment($moment().subtract(3, "month").startOf("week").format("MMMM D, YYYY"), "MMM DD, YYYY").format("YYYY-MM-DD");
		var endDate = $moment($moment().endOf("month").format("MMMM D, YYYY"), "MMM DD, YYYY").format("YYYY-MM-DD");

		TaskFactory.sort_dto({
			userId: $scope.currentUserId,
			startDate: startDate,
			endDate: endDate
		}).$promise.then(function (response) {
			$scope.tasks = response;
			$scope.tasksLoading = false;
		});
	};

	$scope.getProjectedVsBilled = function (type) {
		$scope.projectedVsBilled = [];
		if ($scope.userBranchIds.length > 0) {
			$scope.projectedVsBilledLoading = true;
			ClaimFactory.getProjectedVsBilled(
				{
					branchIds: $scope.userBranchIds,
					isPatient: type === "patient" ? true : false,
					isPrescription: type === "prescription" ? true : false

				}).$promise.then(function (response) {
				$scope.projectedVsBilled = response;
				$scope.projectedVsBilledLoading = false;
			});
		}
	};

	$scope.getEstimatedMonthlyDelivery = function (config) {
		$scope.estimatedMonthlyDeliveryDTO = {};
		if ($scope.userBranchIds.length > 0) {
			if (!config) config = 'month'; // default to monthly
			$scope.estimatedMonthlyDeliveryLoading = true;
			PrescriptionFactory.getEstimatedMonthlyDeliveryDto({
				range: config,
				today: $moment().format('YYYY-MM-DD').toString(),
				branchIds: $scope.userBranchIds
			}).$promise.then(function (response) {
				$scope.estimatedMonthlyDeliveryDTO = response;
				$scope.estimatedMonthlyDeliveryLoading = false;
			});
		}
	};

	$scope.createPatientIntakeLink = function () {
		// "k=" + TenantContext.getCurrentTenant() + "&p=" + patientId + "&d=" + DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.ENGLISH).format(expirationDate);
		var expirationDate = $moment($moment().add(1, 'days')).format('YYYY-MM-DD');
		var encodedStr = btoa('k=' + SessionService.getCurrentCompany().key + '&d=' + expirationDate);
		var hostname = window.location.hostname;
		if ('localhost' == window.location.hostname) {
			hostname = hostname + ':8080';
		}
		alert(window.location.protocol + '//' + hostname + '/patient_intake?t=' + encodedStr);
	};

	$scope.getDailySalesOutstanding = function () {
		$scope.dailySalesOutstandingDTOs = [];
		// SCRUM-4433 (later...more disabled)
		// if ($scope.userBranchIds.length > 0) {
		// 	$scope.dailySalesOutstandingLoading = true;
		// 	ReportFactory.dailySalesOutstandingWidget({
		// 		startDate: $moment().startOf('year').format('YYYY-MM-DD'),
		// 		branchIds: $scope.userBranchIds
		// 	}).$promise.then(function (response) {
		// 		$scope.dailySalesOutstandingDTOs = response;
		// 		$scope.dailySalesOutstandingLoading = false;
		// 	});
		// }
	};

	$scope.openViewReleaseNoteModal = function (releaseNote) {
		var modalInstance = $uibModal.open({
			templateUrl: "viewReleaseNoteModal.html",
			controller: 'ViewReleaseNoteCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'lg',
			resolve: {
				releaseNote: function () {
					return releaseNote;
				}
			}
		});
	};

	function init() {
		if ($stateParams.releaseNote !== null && !$scope.releaseNoteShown) {
			$scope.releaseNoteShown = true;
			$scope.openViewReleaseNoteModal($stateParams.releaseNote);
		}
		if ($rootScope.branchId) {
			$scope.userBranchIds = [];
			UserBranchFactory.findUserBranches({userId: SessionService.getCurrentUserId()}).$promise.then(function (response) {
				angular.forEach(response, function (branch) {
					if (!branch.parentId && branch.active) {
						$scope.userBranchIds.push(branch.id);
						$scope.userBranches.push(branch);
					}
				});

				$scope.currentUser = SessionService.getCurrentUser();
				if($scope.currentUser) {
					angular.forEach($scope.currentUser.widgets, function (widget) {
						$scope.currentUserWidgets.push(widget.name);
						if (widget.name === "upcoming_appointments") $scope.getUpcomingAppointments();
						else if (widget.name === "missing_notes") $scope.getMissingNotes();
						else if (widget.name === "outstanding_claims_with_no_activity") $scope.getNoActivityOnClaims();
						else if (widget.name === "prescription_summary") $scope.getWips(0);
						else if (widget.name === "tasks") $scope.getTasks();
						else if (widget.name === "projected_vs_billed") $scope.getProjectedVsBilled('patient');
						else if (widget.name === 'estimated_monthly_delivery') $scope.getEstimatedMonthlyDelivery();
						//else if (widget.name === 'daily_sales_outstanding') $scope.getDailySalesOutstanding();
						// else if (widget.name === "outstanding_claim_ar") $scope.reportService.arAgingReport($rootScope.branchId);
					});
				} else {
					console.log("Could not fetch current user's widgets");
				}
				$scope.getPatientCount();
				$scope.getAppointmentCount();
			});
		}
	}

	$scope.$on('localStorageObjectLoadComplete', function () {
		init();
	});

	$scope.aiNotesEnabled = false;

	SystemSettingFactory.findBySectionAndField({
		section: "general",
		field: "ai_notes_on"
	}).$promise.then(function (response) {
		$scope.aiNotesEnabled = response.value === 'Y';
	});
}
