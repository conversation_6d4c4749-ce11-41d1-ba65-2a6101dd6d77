'use strict';
app.controller('PatientMergeModalCtrl', PatientMergeModalCtrl);
PatientMergeModalCtrl.$inject = ['$scope', '$uibModalInstance', 'patientIdFromProfile', 'PatientService', 'UtilService'];

function PatientMergeModalCtrl($scope, $uibModalInstance, patientIdFromProfile, PatientService, UtilService) {

  $scope.patientService = PatientService;
  $scope.utilService = UtilService;
  $scope.sourcePatientId = patientIdFromProfile; 
  $scope.mergeTargetPatient = null;

  $scope.confirmMergeAction = function() {
    if ($scope.mergeTargetPatient) {
      $uibModalInstance.close({ 
        sourcePatientId: $scope.sourcePatientId,
        targetPatientId: $scope.mergeTargetPatient.id 
      });
    }
  };

  $scope.cancel = function() {
    $uibModalInstance.dismiss('cancel');
  };

  $scope.selectPatient = function (item) {
    if (item && Number(item.id) !== Number($scope.sourcePatientId)) {
	  if (item.active) {
        $scope.mergeTargetPatient = item; // merge target patient must be active
	  } else {
        delete $scope.mergeTargetPatient; // if not active, remove
	  }
    } else if (Number(item.id) === Number($scope.sourcePatientId)) {
	  delete $scope.mergeTargetPatient;	// merge target patient cannot be the same as source
	}
  };

  $scope.deselectPatient = function () {
    if ($scope.mergeTargetPatient.id) {
      delete $scope.mergeTargetPatient;
    }
  };

}
