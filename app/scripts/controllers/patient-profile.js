app.controller('PatientProfileCtrl', PatientProfileCtrl);
PatientProfileCtrl.$inject = ['$rootScope', '$cookies', '$location', '$scope', "$uibModal",'$state', '$stateParams', '$moment', '$timeout',
	'DateService', 'UtilService', 'StatesFactory', 'UserFactory', 'PaymentTypesFactory', 'AppointmentStatusFactory',
	'FormTypesFactory', 'BranchFactory', 'DeliveryLocationFactory', 'ChecklistTemplateFactory', 'LivingSituationFactory',
	'FileService', 'HippaFormService', 'ClaimService', 'UserNotificationService', 'PhysicianService', 'TabService', 'UserService',
	'NoteService', 'CustomFormService', 'EligService', 'PatientService', 'UploadService', 'AppointmentService', 'PrescriptionService',
	'OutcomeMeasureService', 'InsuranceService', 'FinancialResponsibilityService', 'ChecklistService', 'PurchasingService',
	'ForbinService', 'ClaimAdjustmentService', 'PrescriptionFileService', 'PurchaseOrderItemStatusesFactory', 'PatientFactory',
	'TaskService', 'SignatureService', 'ShoppingCartService', 'PaymentService', 'NotificationFactory', 'CacheFactory', 'PrescriptionFactory',
	'EvaluationFormService', 'SystemSettingFactory', 'BranchService', 'FileFactory', 'toastr', 'MeasurementService', 'TemplateFactory',
	'ItemService', 'InventoryItemFactory'];

function PatientProfileCtrl($rootScope, $cookies, $location, $scope, $uibModal,$state, $stateParams, $moment, $timeout, DateService,
                            UtilService, StatesFactory, UserFactory, PaymentTypesFactory, AppointmentStatusFactory,
                            FormTypesFactory, BranchFactory, DeliveryLocationFactory, ChecklistTemplateFactory,
                            LivingSituationFactory, FileService, HippaFormService, ClaimService, UserNotificationService,
                            PhysicianService, TabService, UserService, NoteService, CustomFormService, EligService,
                            PatientService, UploadService, AppointmentService, PrescriptionService, OutcomeMeasureService,
                            InsuranceService, FinancialResponsibilityService, ChecklistService, PurchasingService, ForbinService,
                            ClaimAdjustmentService, PrescriptionFileService, PurchaseOrderItemStatusesFactory, PatientFactory,
                            TaskService, SignatureService, ShoppingCartService, PaymentService, NotificationFactory, CacheFactory,
                            PrescriptionFactory, EvaluationFormService, SystemSettingFactory, BranchService, FileFactory, toastr,
                            MeasurementService, TemplateFactory, ItemService, InventoryItemFactory) {

	$scope.itemService = ItemService;
	$scope.measurementService = MeasurementService;
	$scope.measurementService.clear();

	$rootScope.page = {
		title: 'Patients',
		subtitle: 'Patient #' + $stateParams.patientId.toString()
	};
	$scope.tab = $location.search().tab;
	if ($stateParams.tab != null) {
		$scope.tab = $stateParams.tab;
	}
	$scope.prescriptionId = $location.search().prescriptionId;
	$scope.section = $location.search().section;
	if ($scope.prescriptionId != null) {
		TabService.activeTab = [];
	}

	if ($scope.tab == null) {
		$scope.tab = "patient_summary";
	}

	$scope.tabName = $scope.tab;

	var prescriptionWatch;
	var timedSave = {};

	NoteService.initializeNoteCache();

	//$scope.$on("localStorageObjectLoadComplete", function () {
	// if(prescriptionWatch){
	// 	prescriptionWatch();
	// }

	$scope.patientId = $stateParams.patientId;
	$scope.displayExpanded = false;

	$scope.openTab = function (tab) {
		PrescriptionService.openTab(tab, $scope.patientId);
		$scope.tabName = tab;

		if (tab === 'measurements') {
			$scope.measurementService.init($scope.patientId);
		}
	};

	$scope.$on('openTab', function (event, tab) {
		$scope.openTab(tab);
	});

	$scope.isActiveTab = function (tab) {
		return (tab === $scope.tabName);
	};

	if ($scope.tab === 'work-in-progress') {
		PrescriptionService.openTab('work_in_progress', $scope.patientId);
		$scope.tabName = 'work_in_progress';
	} else {
		PrescriptionService.openTab($scope.tab, $scope.patientId);
		$scope.openTab($scope.tab);
	}

	$scope.moment = $moment;
	$scope.tabService = TabService;
	$scope.userService = UserService;
	$scope.hasPermission = UserService.hasPermission;
	$scope.dateService = DateService;
	$scope.hippaFormService = HippaFormService;
	$scope.customFormService = CustomFormService;
	$scope.eligService = EligService;
	$scope.patientService = PatientService;
	$scope.uploadService = UploadService;
	$scope.userNotificationService = UserNotificationService;
	$scope.utilService = UtilService;
	$scope.prescriptionService = PrescriptionService;
	$scope.claimService = ClaimService;
	$scope.appointmentService = AppointmentService;
	$scope.insuranceService = InsuranceService;
	$scope.noteService = NoteService;
	$scope.fileService = FileService;
	$scope.physicianService = PhysicianService;
	$scope.financialResponsibilityService = FinancialResponsibilityService;
	$scope.checklistService = ChecklistService;
	$scope.purchasingService = PurchasingService;
	$scope.forbinService = ForbinService;
	$scope.outcomeMeasureService = OutcomeMeasureService;
	$scope.claimAdjustmentService = ClaimAdjustmentService;
	$scope.prescriptionFileService = PrescriptionFileService;
	$scope.taskService = TaskService;
	$scope.signatureService = SignatureService;
	$scope.shoppingCartService = ShoppingCartService;
	$scope.evaluationFormService = EvaluationFormService;
	$scope.branchService = BranchService;
	$scope.paymentService = PaymentService;
	$scope.vendor = undefined;
	$scope.searching = false;

	/* Preload data for dropdowns */
	$scope.states = StatesFactory.get(); //Patient, Insurance, Contacts
	$scope.paymentTypes = PaymentTypesFactory.get();  //Transaction History
	$scope.appointmentStatus = AppointmentStatusFactory.get(); //Patient Appointments
	$scope.fabricationTemplates = ChecklistTemplateFactory.getChecklistTemplatesByType({type: 'Fabrication'});
	$scope.checklistTemplates = ChecklistTemplateFactory.getChecklistTemplatesByType({type: 'Prescription'});
	$scope.livingSituations = LivingSituationFactory.get();
	$scope.formTypes = FormTypesFactory.get();
	$scope.poiStatuses = PurchaseOrderItemStatusesFactory.get();
	$scope.ruralOptions = {'Rural': true, 'Non-Rural': false};

	// PERF: DTO this.  Only used on _update_forms.html
	$scope.primaryPractitioners = UserFactory.getPractitionerUsers({companyId: UserService.getCompanyId()});

	$scope.calendar = {
		opened: {},
		dateOptions: {
			formatYear: 'yy',
			maxDate: new Date(),
			minDate: new Date().setFullYear(new Date().getFullYear() - 130),
			startingDay: 1
		},
		open: function ($event, which) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.calendar.opened[which] = true;
		}
	};

	// 	$scope.init();
	// 	if(!PatientService.patient.primaryBranchId){
	// 		$scope.openPickPatientPrimaryBranch(PatientService.patient.id);
	// 	}
	// });

	$scope.summernoteOptions = {
		focus: false,
		airMode: false,
		tabDisable: true,
		toolbar: [
			['edit', ['undo', 'redo']],
			['headline', ['style']],
			['style', ['bold', 'italic', 'underline', 'superscript', 'subscript', 'strikethrough', 'clear']],
			['fontface', ['fontname']],
			['textsize', ['fontsize']],
			['fontclr', ['color']],
			['alignment', ['ul', 'ol', 'paragraph', 'lineheight']],
			['height', ['height']],
			['table', ['table']],
			['insert', ['link', 'picture', 'video', 'hr']],
			['insert', ['link', 'hr']]
			// ['view', ['fullscreen', 'codeview']],
			// ['help', ['help']]
		],
		insertTableMaxSize: {
			col: 20,
			row: 20
		},
		dialogsInBody: true,
		callbacks: {
			onPaste: function (e) {
				var updatePastedText = function () {
					var replacedText = $scope.utilService.stripUnsupportedHTMLTagsSummerNote(e.currentTarget.innerHTML);
					if (replacedText) {
						e.currentTarget.innerHTML = replacedText;
					}
				};
				setTimeout(function () {
					updatePastedText();
				}, 100);
			},
			onChange: function (contents, event) {
				var id = event[0].parentElement.parentElement.parentElement.children[0].id;
				var rxId = 0;
				if (id) {
					rxId = id.split('_')[1];
				}
				if (contents != '<p><br></p>') {
					CacheFactory.get('noteCache').put('/patient/' + $scope.patientId + '/prescription/note/' + rxId, {
						content: contents
					});
					var saveDraft = function (rxId) {
						PrescriptionFactory.get({id: rxId}).$promise.then(function (prescription) {
							NoteService.syncSaveNote(prescription, 'draft').then(function (note) {
								clearTimedSave(rxId);
								NoteService.viewNote(note);
							}).catch(function (error) {
								//console.error(error);
							});
						});
					};

					let clearTimedSave = function (rxId) {
						if (rxId in timedSave) {
							//alert("clearing for "+ rxId);
							clearTimeout(timedSave[rxId]);
						}
					};

					clearTimedSave(rxId);
					timedSave[rxId] = setTimeout(saveDraft, 60000, rxId);
				}
			}
		}
	};

	/**
	 * Uploads image to files and docs.
	 * Returns image node for summernote to insert.
	 * @param file
	 * @returns void
	 */
	$scope.upload = function (files) {
		const file = files[0];
		toastr.info('Uploading image...');
		const fileReader = new FileReader();
		fileReader.readAsArrayBuffer(file);
		return (prescriptionId) => {
			fileReader.onload = function (fileIO) {
				const fileFormData = new FormData();
				fileFormData.append('file', new Blob([fileIO.target.result]), file.name);
				FileFactory.uploadImage({
					prescriptionId: prescriptionId | 0,
					patientId: $scope.patientId
				}, fileFormData).$promise.then((uniqueFileName)=>{
					let stringFileName = '';
					for(const charIndex in uniqueFileName) {
						// if charindex is a number then add it to the string
						if(!isNaN(parseInt(charIndex))) {
							stringFileName += uniqueFileName[charIndex];
						}
					}
					FileFactory.save({type: prescriptionId ? 'prescription' : 'general'}, {
						patientId: $scope.patientId,
						prescriptionId: prescriptionId | 0,
						fileName: stringFileName,
						description: 'Note Image Upload: ' + file.name,
					}).$promise.then(function (response) {
						toastr.success('Successfully uploaded ' + file.name);
						// insert image into summernote via image link insert
						const baseUrl = $('#baseUrl').attr('value');
						const imageLink =  `${baseUrl}/api/file/contents/${response.id}`;
						const editorScope = angular.element(`#note_${prescriptionId ? prescriptionId:0}`);
						editorScope.summernote('insertImage', imageLink);
					}).catch(function (error) {
						toastr.error('Error uploading '+ file.name );
						console.error(error);
					});
				});
			};
			return fileReader;
		};
	};

	$scope.changeTemplate = function (prescriptionId, patientId, noteTemplateId) {
		prescriptionId = prescriptionId || 0;
		var templateId = NoteService.noteTemplateId[prescriptionId];

		if (templateId !== "") {
			var timezone = $moment.tz.guess(true);

			TemplateFactory.loadByIdPatientPrescription({
				templateId: templateId,
				patientId: patientId,
				prescriptionId: prescriptionId,
				userTimeZone: timezone
			}).$promise.then(function (response) {

				const editorScope = angular.element(`#note_${prescriptionId ? prescriptionId:0}`);

					
				var node = document.createElement('div');
				node.innerHTML = response.data;
				editorScope.summernote('editor.saveRange');
				editorScope.summernote('editor.restoreRange');
				editorScope.summernote('editor.focus');

				editorScope.summernote('insertNode', node);
			
				editorScope.summernote('editor.saveRange');
				editorScope.summernote('editor.restoreRange');
			
			});
		}
	};

	SystemSettingFactory.findBySectionAndField({
		section: "billing",
		field: "rural_non_rural_on"
	}).$promise.then(function (response) {
		$scope.ruralFeesEnabled = response.value === 'Y';
	});

	SystemSettingFactory.findBySectionAndField({
		section: "claim",
		field: "use_rental_auto_bill"
	}).$promise.then(function (response) {
		$scope.useRentalAutoBill = response.value === 'Y';
	});

	SystemSettingFactory.findBySectionAndField({
		section: "general",
		field: "hide_device_type"
	}).$promise.then(function (response) {
		$scope.hideDeviceType = response.value === 'Y';
	});

	SystemSettingFactory.findBySectionAndField({
		section: "general",
		field: "enable_loaners"
	}).$promise.then(function (response) {
		$scope.enableLoaners = response.value === 'Y';
	});

	SystemSettingFactory.findBySectionAndField({
		section: "purchasing",
		field: "hcpcs_item_link"
	}).$promise.then(function (response) {
		$scope.hcpcsItemLink = response.value === '1';
	});

	$scope.$watch("patientService.patientMedicalHistory.referringPhysician", function (newValue, oldValue) {
		if (newValue === undefined) return;
		if (typeof (newValue) === 'object' && typeof (oldValue) !== 'object') {
			PatientService.patientMedicalHistory.referringPhysicianId = newValue.id;
		} else if (typeof (newValue) === 'string' && typeof (oldValue) === 'object') {
			PatientService.patientMedicalHistory.referringPhysician = undefined;
			PatientService.patientMedicalHistory.referringPhysicianId = undefined;
		} else {
			if (PatientService.patientMedicalHistory) PatientService.patientMedicalHistory.referringPhysicianId = undefined;
		}
	});

	$scope.toggleDisplay = function () {
		$scope.displayExpanded = !$scope.displayExpanded;
	};

	$scope.formSelected = function (selected) {
		$scope.utilService.openPrintScreen(selected + '?prescriptionId=' + PrescriptionService.prescription.id + '?patientId=' + $scope.patientId);
	};

	$scope.init = function () {
		/* Load Data */
		if (prescriptionWatch)
			prescriptionWatch();
		prescriptionWatch = $scope.$watch("prescriptionService.prescriptions", function (newValue, oldValue) {

			if (PrescriptionService.loading || newValue === null) {
				//2781 console.log("???????? BYPASS! Patient Profile init for " + $scope.patientId);
				return;
			}
			//2781 console.log("????????Patient Profile init for " + $scope.patientId);

			NoteService.profileLoad($scope.patientId); //Depends on Prescriptions and Appointments, hence triggered here when prescriptions change
			FileService.profileLoad($scope.patientId); // Also depends on Prescriptions and Appointments.
			HippaFormService.findByPatientId($scope.patientId);
			ChecklistService.profileLoad($scope.patientId, PrescriptionService.prescriptions);
			OutcomeMeasureService.profileLoad($scope.patientId, PrescriptionService.prescriptions);


			// moved to prescriptionService.setActivePrescription
			// if(PrescriptionService.prescription && ClaimService.prescriptionClaim[PrescriptionService.prescription.id] && ClaimService.prescriptionClaim[PrescriptionService.prescription.id].prescriptionClaimSubmissions) {
			// 	$scope.hasSubmissions = ClaimService.prescriptionClaim[PrescriptionService.prescription.id].prescriptionClaimSubmissions.length;
			// } else {
			// 	$scope.hasSubmissions = false;
			// }

		});
		PatientService.init();
		AppointmentService.init();
		// UserNotificationService.init();
		ClaimService.init($scope.patientId);
		InsuranceService.init();
		PrescriptionService.init();
		NoteService.init();
		FileService.init();
		ChecklistService.init();
		//OutcomeMeasureService.init();
		ForbinService.init();  // does not have a profileLoad (??)

		$timeout(function () {
			PatientService.profileLoad($scope.patientId).then(() => {
					// After loading the patient,
					// if the patient ID is not valid redirect to All Patients.
					if (!PatientService.patient.id) {
						$state.transitionTo("app.patient.all");
						toastr.warning("Error accessing patient, if you believe this is an error contact support")
					}
				}
			);
			ClaimService.profileLoad($scope.patientId, PrescriptionService.showArchivedItems);
			InsuranceService.profileLoad($scope.patientId);
			PrescriptionService.profileLoad($scope.patientId); //Depends on InsuranceVerification
			PurchasingService.profileLoad($scope.patientId, PrescriptionService.showArchivedItems);
			AppointmentService.profileLoad($scope.patientId);

			// Everything else is loaded in the PrescriptionService.Prescriptions $watch above
		});
	};

	$scope.init();

	$scope.$watch('patientService.patient.id', function () {
		if (PatientService.patient && PatientService.patient.id && $scope.prescriptionId) {
			PrescriptionService.closePrescription($scope.prescriptionId);
			$scope.openDefaultSection($scope.section);
		}
		DeliveryLocationFactory.query().$promise.then(function (response) {
			$scope.deliveryLocations = response;
			$scope.deliveryLocations.push({
				id: "primary_branch",
				name: "Primary Branch",
				sortOrder: 1
			});
			$scope.deliveryLocations.push({
				id: "prescription_branch",
				name: "Prescription Branch",
				sortOrder: 1
			});
			$scope.deliveryLocations.push({
				id: "patient_address",
				name: (PatientService.patient.addressLabel ?
					PatientService.patient.addressLabel + ' - ' : "") + "Patient Address",
				sortOrder: 2
			});
			$scope.deliveryLocations.push({
				id: "other",
				name: "Other"
			});
			$scope.deliveryLocations.push({
				id: "patient_alternate_address",
				name: (PatientService.patient.addressLabel2 ? PatientService.patient.addressLabel2 + ' - ' : "") + "Patient Alternate Address"
			});
		});

		if (!PatientService.patient && !PatientService.patient.primaryBranchId) {
			PatientService.openPickPatientPrimaryBranch(PatientService.patient.id);
		}
	});

	$scope.$watch('insuranceService.patientInsurancesVerification', function (newValue, oldValue) {
		if (!$scope.$$phase) {
			$scope.$apply();
		}
	});

	$scope.activeTab = function (tab) {
		if (tab === $scope.tab) {
			return "active";
		} else {
			return "notactive";
		}
	};

	$scope.activeTabPane = function (tab) {
		if (tab === $scope.tab) {
			return "tab-pane active";
		} else {
			return "tab-pane";
		}
	};

	$scope.quickOrder = function (patientId) {
		UtilService.openV2Link("patients/patient/", patientId + "?prescriptionId=new");
	};

	$scope.openTaskModal = function (task) {
		var modalInstance = $uibModal.open({
			templateUrl: 'views/tmpl/tasks/_task_modal.html',
			controller: 'TaskCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				task: function () {
					return task;
				},
				fromAlert: function () {
					return false;
				},
				patientFromProfile: function () {
					return $scope.patientId;
				}
			}
		});
		modalInstance.result.then(function (payload) {
			// Don't immediately mark task notification read if task was just created--SCRUM-3097
			if (payload.task.completed && payload.alreadyExists) {
				NotificationFactory.markFollowUpNotificationRead(payload.task).$promise.then(function (response) {
					UserNotificationService.loadNotifications();
				});
			}
			PatientService.getPatientTasks($scope.patientId);
		});
	};

	$scope.openMergePatientModal = function() {
	    var modalInstance = $uibModal.open({
	        templateUrl: 'mergePatientModal.html',
	        controller: 'PatientMergeModalCtrl',
			backdrop: 'static',
			keyboard: false,
			size: 'md',
			resolve: {
				patientIdFromProfile: function () {
					return $scope.patientId;
				}
			}
	    });	
	    modalInstance.result.then(function(result) {
		    PatientFactory.mergePatient({
				fromPatientId: result.sourcePatientId, 
				toPatientId: result.targetPatientId
		    }, {}).$promise
		    .then(function(response) {
				$state.go("app.patient.profile", {"patientId": result.targetPatientId});
				toastr.success(`Patient #${result.sourcePatientId} was merged into patient #${result.targetPatientId}. Patient #${result.sourcePatientId} was deactivated.`);
		    })
		    .catch(function(error) {
        	// console.error('Merge failed', error);
		    });		    
	    }, function(reason) {
	         // console.log('Merge canceled: ' + reason);
	    });
	};

	$scope.openDefaultSection = function (section) {
		switch (section) {
			case 'prescription_details':
				TabService.wipSection.prescription_details_panel.open = true;
				break;
			case 'insurance_verification':
				TabService.wipSection.insurance_verification_panel.open = true;
				break;
			case 'pre-authorization':
				TabService.wipSection.pre_authorization_panel.open = true;
				break;
			case 'l_code_selection':
				TabService.wipSection.l_code_selection_panel.open = true;
				break;
			case 'l_code_justification':
				TabService.wipSection.l_code_justification_panel.open = true;
				break;
			case 'standard_written_order':
				TabService.wipSection.standard_written_order_panel.open = true;
				break;
			case 'covered_codes':
				TabService.wipSection.covered_codes_panel.open = true;
				break;
			case 'service_estimate':
				TabService.wipSection.service_estimate_panel.open = true;
				break;
			case 'financial_responsibility':
				TabService.wipSection.financial_responsibility_panel.open = true;
				break;
			case 'proof_of_delivery':
				TabService.wipSection.proof_of_delivery_panel.open = true;
				break;
			case 'required_documentation':
				TabService.wipSection.required_documentation.open = true;
				break;
			case 'physician_documentation':
				TabService.wipSection.physician_documentation.open = true;
				break;
			default:
				break;
		}
	};

	$scope.updatePrescriptionHCPCFees = function (bSavePrescription) {
		if (PrescriptionService.prescription) {

			if (!ClaimService.prescriptionClaim && ClaimService.prescriptionClaim[PrescriptionService.prescription.id].prescriptionClaimSubmissions.length) {
				return; // absolute safety
			}
			if (!ClaimService.prescriptionClaim && ClaimService.prescriptionClaim[PrescriptionService.prescription.id]) {
				if (!confirm("Clicking this button will change the Total Claim Amount on the claim, are you sure you want to reset these HCPCs to their default values for these fee schedules?")) {
					return;
				}
			} else if (!confirm("Are you sure you want to reset these HCPCs to their default values for these fee schedules?")) {
				return;
			}

			$("#update-prescription-hcpcs-fees").button("loading");
			PrescriptionFactory.updatePrescriptionHCPCFees({
				prescriptionId: PrescriptionService.prescription.id,
				isRural: PrescriptionService.prescription.isRural !== undefined ? PrescriptionService.prescription.isRural : false,
				willSaveRx: bSavePrescription
			}).$promise.then(function (response) {
				UtilService.displayAlert("success", "<p>Fees successfully updated.</p>", "#hcpcs-update-alert-container");
				$("#update-prescription-hcpcs-fees").button("reset");
				InsuranceService.loadPatientInsurancesVerification(PrescriptionService.prescription.id);
			}, function (e) {
				$("#update-prescription-hcpcs-fees").button("reset");
				UtilService.displayAlert("danger", "<p>e</p>", "#hcpcs-update-alert-container");
			});
		}
	};

	$scope.selectedCartItems = {
		checkAll: false,
		checked: []
	};

	$scope.toggleAllChecked = function () {
		if ($scope.selectedCartItems.checkAll) {
			$(".generate-cart-item").each(function (index, item) {
				if ($(item).data().cartItem.status === "Open" || $(item).data().cartItem.status === "Approved") {
					if (!$(item).prop("disabled")) {
						$(item).prop("checked", true);
					}
				}
			});

			if ($(".generate-cart-item:checked").length) {
				$("#add-items").removeAttr("disabled");
				$("#delete-items").removeAttr("disabled");
			}
		} else {
			$("#add-items").prop("disabled", true);
			$("#delete-items").prop("disabled", true);
			$(".generate-cart-item").each(function (index, item) {
				$(item).prop("checked", false);
			});
		}
	};

	$scope.toggleChecked = function () {
		if ($("#check-all").is(":checked")) {
			if (!$(".generate-cart-item:checked").length) {
				$("#check-all").prop("checked", false);
				$("#add-items").prop("disabled", true);
				$("#delete-items").prop("disabled", true);
			} else {
				$("#add-items").removeAttr("disabled");
				$("#delete-items").removeAttr("disabled");
			}
		} else {
			if (!$(".generate-cart-item:checked").length) {
				$("#add-items").prop("disabled", true);
				$("#delete-items").prop("disabled", true);
			} else {
				$("#add-items").removeAttr("disabled");
				$("#delete-items").removeAttr("disabled");
			}
		}
	};

	$scope.addSelectedCartItemsToPurchaseOrder = function () {
		$scope.selectedCartItems.checked = [];
		$(".generate-cart-item").each(function (index, item) {
			if ($(this).is(":checked")) {
				$scope.selectedCartItems.checked.push($(item).data("cart-item"));
			}
		});
		if ($scope.selectedCartItems.checked.length) {
			var allPrescriptionIdsSet = true;
			angular.forEach($scope.selectedCartItems.checked, function (cart, index) {
				if (!validateCartItem(cart)) {
					return;
				}
				if($scope.forcePrescriptionOnPOItems && $scope.selectedCartItems.checked[index].patientId && !$scope.selectedCartItems.checked[index].prescriptionId){
					allPrescriptionIdsSet = false;
				}
				cart.neededOn = $moment(cart.neededOn, "YYYY-MM-DD");
			});
			if(!allPrescriptionIdsSet) {
				UtilService.displayAlert("danger", "<p>Only cart items with valid presciptions can be edited to the PO.  Please select a prescription for the items marked with the red X in the prescription field.</p>", "#header-alert-container");
			} else {
				ShoppingCartService.addItemsToPurchaseOrder($scope.selectedCartItems.checked).then(function (response) {
					$scope.selectedCartItems.checkAll = false;
					ShoppingCartService.findByPatientId($scope.patientId);
					UtilService.displayAlert("success", "<p>Cart Items were successfully added to Purchase Order.</p>", "#header-alert-container");
				}, function (e) {
					console.log(e);
				});
			}
		}
	};

	var validateCartItem = function(cartItem) {
		if (['from_inventory', 'inventory'].includes(cartItem.status.toLowerCase())) {
			if (!cartItem.prescriptionId) {
				confirm('ShoppingCart #' + cartItem.id + ': Item "' + cartItem.item.name + '" requires a prescription.');
				return false;
			}
			ItemService.findItemPhysicalByBranchIdAndItemId(cartItem.branchId, cartItem.itemId, cartItem.quantity, 'inventory').then(function (response) {
				if (response.length < cartItem.quantity) {
					confirm('ShoppingCart #' + cartItem.id + ': not enough inventory of Item "' + cartItem.item.name + '" is available for branch "' + cartItem.branch.name +
						'" (requested: ' + cartItem.quantity + ', available: ' + response.length + ')');
					return false;
				} else {
					return true;
				}
			}, function (error) {
				console.error(JSON.stringify(error));
				confirm(error.data.message);
				return false;
			});
		} else {
			return true;
		}
	}

	$scope.updateRentalStatus = function (plc) {
		var newStatus = plc.rentalStatus === 'IN' ? 'OUT' : 'IN';
		var itemStatus = newStatus == 'IN' ? 'cleaning' : 'rented';
		if (confirm("Change `Rental Status` to " + newStatus + " ?")) {
			var codedReasons = $('#codedReasons').attr('value').split(',');
			ItemService.updateItemPhysicalStatus(plc.prescriptionId, plc.id, itemStatus, 'l_code_selection', codedReasons).then(function (statusChanged) {
				if (statusChanged) PatientService.getPatientLoaners(plc.prescription.patientId);
			});
			plc.rentalStatus = newStatus;
			PrescriptionService.updatePlc(plc);
		}
	};

	$scope.$on('$stateChangeStart', function () {

		if (prescriptionWatch) {
			prescriptionWatch();
		}
	});

	$scope.$watch('userService.reloadAbnSectionInWIP', function (newVal, oldVal) {
		if (PrescriptionService.prescription && newVal !== oldVal) {
			PrescriptionService.closePrescription(PrescriptionService.prescription.id);
		}
	});

	$scope.$watch('userService.reloadEFormSectionInNotes', function (newVal, oldVal) {
		if ($scope.patientId && newVal !== oldVal) {
			NoteService.profileLoad($scope.patientId);
		}
	});

	$scope.$watch('userService.reloadPatientFiles', function (newVal, oldVal) {
		//console.log("reloadPatientFiles watch entered");
		if (PrescriptionService.prescription && newVal !== oldVal) {
			//console.log("Run reloadPatientFiles methods");
			FileService.getPatientFiles(PrescriptionService.prescription.patientId);
			PrescriptionService.closePrescription(PrescriptionService.prescription.id);
		}
	});

	$rootScope.$on('itemPhysicalModalClosed', function () {
		PatientService.getPatientLoaners($scope.patientId);
	});

	$rootScope.$on('lCodeSelectionModalClosed', function () {
		PatientService.getPatientLoaners($scope.patientId);
	});
}
