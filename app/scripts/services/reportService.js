app.service('ReportService', ReportService);
ReportService.$inject = ['$rootScope', '$filter', '$moment', '$http', 'AdjustmentFactory', 'AppointmentStatusFactory', 'ReportsListFactory', 'ReportFactory', 'BranchService', 'PhysicianFactory', 'PatientFactory', 'ServiceEstimateFactory', 'UtilService', 'UserFactory', 'ClaimSubmissionFactory', 'InsuranceCompanyFactory', 'UserService', 'SurveyFactory', 'ClaimStatusJsonFactory', 'PaymentTypesFactory', 'PrescriptionLCodeFactory', 'SystemSettingFactory', 'NoteTypesFactory', 'AppointmentTypeFactory'];

function ReportService($rootScope, $filter, $moment, $http, AdjustmentFactory, AppointmentStatusFactory, ReportsListFactory, ReportFactory, BranchService, PhysicianFactory, PatientFactory, ServiceEstimateFactory, UtilService, UserFactory, ClaimSubmissionFactory, InsuranceCompanyFactory, UserService, SurveyFactory, ClaimStatusJsonFactory, PaymentTypesFactory, PrescriptionLCodeFactory, SystemSettingFactory, NoteTypesFactory, AppointmentTypeFactory) {
  var _this = this;
  this.appointmentTypes = AppointmentTypeFactory.findActiveAppointmentTypes();
  this.noteTypes = NoteTypesFactory.get();
  this.noteType = null;
  this.categoryId = "";
  this.orthoticOrProsthetic = "";
  this.branchId = $rootScope.branchId;
  this.useSubmissionDate = true;
  this.hideNegativeBalance = false;
  this.insuranceCompanyId = null;
  this.status = null;
  this.completeStatus = "all";
  this.codeSet = "icd-10";
  this.dobMonth = "January";
  this.monthlyBillingsMonths = "3";       // default to past three months
  this.monthlyNewPatientsMonths = "3";    // default to past three months
  this.percentCollectionsMonths = "3";
  this.billingsByPractitionerMonths = "3";
  this.billingsByViewUserMonths = "3";
  this.billingReferralMonths = "3";
	//SCRUM-5266: BranchService.loadUserBranches();
  this.startDate = new Date();
  this.endDate = new Date();
  this.startSurveySentDate = $moment().format('YYYY-MM-DD');
  this.endSurveySentDate = $moment().format('YYYY-MM-DD');
  this.startConfirmationRequestSentDate = $moment().format('YYYY-MM-DD');
  this.endConfirmationRequestSentDate = $moment().format('YYYY-MM-DD');
  this.surveySent = "notsent";
  this.yearlyBillingsCollectedStartYear = new Date().getFullYear();
  this.yearlyBillingsCollectedEndYear = new Date().getFullYear();
  this.payeeType = "all";
  this.balanceType = true;
  this.dateOption = "payment";
  this.branches = [];
  this.paymentTypes = [];
  this.report = null;
  this.prescriptions = [];
  this.days = 0;
	this.sortColumn = '';
	this.reverseOrder = false;
	this.firstSort = true;
	this.appointmentStatus = AppointmentStatusFactory.get();
	this.users = UserFactory.getBillingUsers({companyId: UserService.getCompanyId()});
	this.userBatchSummaryUsers = UserFactory.search({companyId: UserService.getCompanyId()});
	this.allUsers = UserFactory.search({companyId: UserService.getCompanyId()});
	this.insuranceCompanies = InsuranceCompanyFactory.search({active: true});
	this.claimStatus = ClaimStatusJsonFactory.get();
	this.paymentTypes = PaymentTypesFactory.get();
	this.adjustmentTypes = AdjustmentFactory.search({q: null});
	this.insuranceCompaniesWithOutstandingBalance = null;
	this.insuranceBalances = [];
	this.isExpand = false;
	this.isOpen = false;
  this.checked = [];
  this.utilService = UtilService;
  this.dailyCloseReportDto = null;
  this.deviceType = "";
  this.dateType = "submission";
  this.selectedAdjustments = [];
  this.currentDate = $moment().format("MMM D, YYYY");
  this.lCodeDisplay = "lCodes";
  this.usePatientBranch = false;
  this.billingsByPractitionerTotalBilled = 0;
  this.billingsByPractitionerTotalAllowable = 0;
  this.billingsByViewUserTotalBilled = 0;
  this.billingsByViewUserTotalAllowable = 0;
  this.showExportLimitMessage = !(UserService.getCompanyId() === 91
	  || UserService.getCompanyId() === 119);

  this.answerTwoCount = {one: 0, two: 0, three: 0, four: 0, five: 0};
  this.answerThreeCount = {one: 0, two: 0, three: 0, four: 0, five: 0};

  this.qsdashboardLink = "";

	this.classname = null;
	this.tableListings = ReportFactory.tableListings();
	this.classFields = [];
	this.classKeys = [];
	this.includeFields = [];
	this.includeKeys = [];
	this.quickSightloading = false;
	this.surveyLinkType = "device_type";
	this.userId = null;
	this.patientId = null;

	this.deprecatedOrLegacyWarning = "WARNING: This report can be used for high level financial information but when exact figures " +
		"matter, we advise using the general ledger. This report is not guaranteed to reconcile to any other legacy report";

	var monthAbbrs = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
	this.months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

	var loading = false;
	var reportsList = ReportsListFactory.get();
	var yearlyBillingsCollectedDataset = [];
	var yearlyBillingsCollectedOptions = {};
	var monthlyBillingsCollectedDataset = [];
	var monthlyBillingsCollectedOptions = {};
	var monthlyNewPrescriptionsDataset = [];
	var monthlyNewPrescriptionsOptions = {};
	var monthlyPercentCollectionsDataset = [];
	var monthlyPercentCollectionsOptions = {};
	var arReportDataset = [];
	var arReportOptions = {};
	var arAgingChartDataSet = []; // unused
	var arAgingChartOptions = {}; // unused
	var arAgingResultSet = {};    // unused


	var results;
	var totalBilledDto = {
		claimCount: null,
		totalAllowable: null,
		totalBillable: null,
		totalPatientResponsibility: null,
	};

	var adjustment = {
		charges: null,
		claimsFiled: null,
		paymentsTotal: null,
		totalAllowable: null,
		initialUnallowed: null,
		totalInsurancePayment: null,
		totalAppliedInsurance: null,
		totalUnappliedInsurance: null,
		totalPatientPayment: null,
		totalAppliedPatient: null,
		totalUnappliedPatient: null,
		paymentsUnappliedPatientChecks: null,
		paymentsUnappliedPatientCash: null,
		paymentsUnappliedPatientCreditCard: null,
		paymentsAppliedPatientChecks: null,
		paymentsAppliedPatientCash: null,
		paymentsAppliedPatientCreditCard: null,
		totalPatientChecks: null,
		totalPatientCash: null,
		totalPatientCreditCard: null,
		adjustmentsTotal: null,
		lineAdjustmentsTotal: null,
		totalPatientBalance: null,
		totalInsuranceBalance: null,
		ar: null,
		arPatientBalance: null,
		arInsuranceBalance: null,
		primaryClaimsFiled: null,
		secondaryClaimsFiled: null,
		tertiaryClaimsFiled: null,
		quaternaryClaimsFiled: 0,
		quinaryClaimsFiled: 0,
		senaryClaimsFiled: 0,
		septenaryClaimsFiled: 0,
		octonaryClaimsFiled: 0,
		nonaryClaimsFiled: 0,
		denaryClaimsFiled: 0,
		otherClaimsFiled: null,
		orthoticSalesSummary: null,
		prostheticSalesSummary: null,
		pedorthicSalesSummary: null,
		mastectomySalesSummary: null,
		miscSalesSummary: null,
		allDeviceTypeSalesSummary: null
	};

  var salesDetailResults = {
	  orthoticSalesDetail: null,
	  prostheticSalesDetail: null,
	  pedorthicSalesDetail: null,
	  mastectomySalesDetail: null,
	  miscSalesDetail: null
  };

  var billingsByDateOfServiceResults = {
    withoutFirstClaimSubmissionDate: null,
    withFirstClaimSubmissionDate: null,
    totalAllowableWithout: 0,
    totalBillableWithout: 0,
    amountPaidWithout: 0,
    totalAllowableWith: 0,
    totalBillableWith: 0,
    amountPaidWith: 0
  };

  var defaultChartOptions = {
    colors: [
      "#61C8B8",
      "#27659A"
    ],
    series: {
      stack: 0,
      shadowSize: 0,
      lines: {show: false, steps: false},
			bars: {show: true, barWidth: 0.9, align: 'center'}
		},
		xaxis: {
			ticks: [],
			font: {
				color: "#FFF",
				size: 10
			}
		},
		yaxis: {
			font: {
				color: "#FFF",
				size: 10
			}
		},
		legend: {
			backgroundOpacity: 0,
			margin: 5,
			position: "ne",
			noColumns: 1
		},
		grid: {
			borderWidth: {
				top: 0,
				right: 0,
				bottom: 1,
				left: 1
			},
			borderColor: "rgba(255, 255, 255, 0.3)",
			margin: 0,
			minBorderMargin: 0,
			labelMargin: 20,
			hoverable: true,
			clickable: true,
			mouseActiveRadius: 6
		}
	};

	//GETTERS for outside calls
	this.reportsList = function () {
		return reportsList;
	};

	this.resetResults = function () {
		results = undefined;
	};

	this.reportResults = function () {
		return results;
	};

	this.loadingData = function () {
		return loading;
	};

	this.getAdjustment = function () {
		return adjustment;
	};

	this.getTotalBilledDto = function () {
		return totalBilledDto;
	};

	this.getYearlyBillingsCollectedDataset = function () {
		return yearlyBillingsCollectedDataset;
	};

	this.getYearlyBillingsCollectedOptions = function () {
		return yearlyBillingsCollectedOptions;
	};

	this.getMonthlyBillingsCollectedDataset = function () {
		return monthlyBillingsCollectedDataset;
	};

	this.getMonthlyBillingsCollectedOptions = function () {
		return monthlyBillingsCollectedOptions;
	};

	this.getMonthlyNewPrescriptionsOptions = function () {
		return monthlyNewPrescriptionsOptions;
	};

	this.getMonthlyNewPrescriptionsDataset = function () {
		return monthlyNewPrescriptionsDataset;
	};

	this.getMonthlyPercentCollectionsOptions = function () {
		return monthlyPercentCollectionsOptions;
	};

	this.getMonthlyPercentCollectionsDataset = function () {
		return monthlyPercentCollectionsDataset;
	};
	this.getArReportOptions = function () {
		return arReportOptions;
	};

	this.getArReportDataset = function () {
		return arReportDataset;
  };

  this.getArAgingChartDataSet = function () {
    return arAgingChartDataSet;
  };

	this.getArAgingChartOptions = function () {
		return arAgingChartOptions;
  };

  this.getArAgingResultSet = function () {
    return arAgingResultSet;
  };

  this.getSalesDetailResults = function () {
    return salesDetailResults;
  };

  this.getBillingsByDateOfServiceResults = function () {
    return billingsByDateOfServiceResults;
  };

  var loadResponse = function (button, response) {
    $('#generate-report').button(button);
    loading = button === 'loading';
    results = response;
  };

  this.sort = function (columnName) {
    _this.sortColumn = columnName;
    if (_this.firstSort) {
      _this.firstSort = false;
    } else {
      _this.reverseOrder = !_this.reverseOrder;
    }
  };

  this.back = function() {
  	_this.report = null;
  	_this.phoneNumber = "";
  };

	/**
	 * Beginning of definitions for callback
	 *
	 * If you look at app/scripts/jsons/reportsList.json, the method definitions are the "id" property in the json object.
	 *
	 * This allows an injectable call, e.g app/views/tmpl/reports/report.html -> ng-click="reportService[reportService.report.id]()"
	 *
	 */

	/** Begin Accounting Reports **/
	//TO-DO: factory url returns 404
	this.arAgingReport = function (branchId) {
		// loadResponse('loading', undefined);
		// ReportFactory.arAgingReport({branchId: branchId}).$promise.then(function (response) {
		// 	loadResponse('reset', response);
		// 	var totals = response;
		// 	var agingArray = [[0, "<30 Days"], [1, "<60 Days"], [2, "<90 Days"], [3, "<120 Days"], [4, "120+ Days"]];
		// 	var finalArray = [];
		//
		// 	finalArray.push([0, totals.thirty]);
		// 	finalArray.push([1, totals.sixty]);
		// 	finalArray.push([2, totals.ninety]);
		// 	finalArray.push([3, totals.oneTwenty]);
		// 	finalArray.push([4, totals.oneTwentyPlus]);
		//
		//
		// 	var barWidth = (0.25);
		// 	var barOptions = {
		// 		show: true,
		// 		varWidth: barWidth,
		// 		lineWidth: 0,
		// 		align: "center",
		// 		fillColor: {colors: [{opacity: 0.3}, {opacity: 0.8}]}
		// 	};
		//
		// 	arReportDataset = [
		// 		{
		// 			data: finalArray,
		// 			label: "Total Outstanding ",
		// 			bars: barOptions
		// 		}
		// 	];
		//
		// 	var yAxisOptions = {
		// 		yaxis: {
		// 			tickFormatter: function (v, axis) {
		// 				return $filter('currency')(v);
		// 			},
		// 			font: {
		// 				color: "#FFF",
		// 				size: 10
		// 			}
		// 		}
		// 	};
		//
		// 	var xAxisOptions = {
		// 		xaxis: {
		// 			ticks: agingArray,
		// 			font: {
		// 				color: "#FFF",
		// 				size: 10
		// 			}
		// 		},
		// 		tooltip: true,
		// 		tooltipOpts: {
		// 			content: "%s: %y",
		// 			defaultTheme: false,
		// 			shifts: {
		// 				x: 0,
		// 				y: 20
		// 			}
		// 		}
		// 	};
		//
		// 	var tmpX = Object.assign(defaultChartOptions, xAxisOptions);
		// 	var tmpY = Object.assign(defaultChartOptions, yAxisOptions);
		// 	arReportOptions = Object.assign(tmpX, tmpY);
		// });
	};

	this.arAgingChart = function () {
		// loadResponse('loading', undefined);
		// ReportFactory.arAgingChart({
		// 	startDate: this.startDate,
		// 	branchId: this.branchId,
		// 	dateOption: this.dateOption
		// }).$promise.then(function (response) {
		// 	//Each response Object[barid][datapoint]
		// 	arAgingResultSet = {
		// 		arDataList: response.arDataList,
		// 		totalBillable: response.totalBillable,
    //     totalContractualAdjustment: response.totalContractualAdjustment,
    //     totalAllowable: response.totalAllowable,
    //     totalContractualDifference: response.totalContractualDifferences,
		// totalCoOrthotic: response.totalCoOrthotic,
		// totalCoProsthetic: response.totalCoProsthetic,
		// totalCoPedorthic: response.totalCoPedorthic,
		// totalCoMisc: response.totalCoMisc,
    //     totalArAdjustments: response.totalArAdjustments,
    //     totalLineAdjustments: response.totalLineAdjustments,
    //     totalPayments: response.totalPayments,
    //     totalDepositTotal: response.totalDepositTotal,
    //     totalOutstandingBalance: response.totalAccountsReceivables,
    //     totalDepositedUnappliedPaymentTotal: response.totalDepositedUnappliedPaymentTotal,
    //     totalAppliedAdjustmentsTotals: response.totalAppliedAdjustmentsTotals,
    //     totalInvalidSubmission: response.totalInvalidSubmission,
    //     errorMessages: response.errorMessages
    //   };
		// 	// var totalBillables = response.totalBillables;
		// 	// var totalAllowables = response.totalAllowables;
		// 	// var totalContractualAdjustments = response.totalContractualAdjustments;
		// 	// var arAdjustments = response.arAdjustments;
		// 	// var lineAdjustments = response.lineAdjustments;
		// 	// var outstandingBalance = response.outstandingBalances;
		// 	arAgingChartDataSet = [
		// 		{label: 'Outstanding Balance', color: '#493d55', data: response.accountsReceivablesMap},
		// 	];
		// 	arAgingChartOptions = {
		// 		series: {
		// 			stack: 1,
		// 			lines: {show: false, steps: false},
		// 			bars: {show: true, barWidth: 0.9, align: 'center', fill: 1}},
		// 		yaxis: {
		// 			tickFormatter: function (v, axis) {
		// 						return $filter('currency')(v);
		// 			},
		// 		},
		// 		xaxis: {
		// 			ticks: [[0, '0 - 30 Days'], [1, '31 - 60 Days'], [2, '61 - 90 Days'], [3, '91 - 120 Days'], [4, '120+ Days'], [5, 'No Submission']],
		// 		},
		// 		 tooltip: true,
		// 		 tooltipOpts: {
		// 			 content: "%s: %y",
		// 			 defaultTheme: false,
		// 			 shifts: {
		// 				 x: 0,
		// 				 y: 20
		// 			 },
		// 		 },
		// 		 grid: {
		// 			 borderWidth: {
		// 				 top: 0,
		// 				 right: 0,
		// 				 bottom: 1,
		// 				 left: 1
		// 			 },
		// 			 borderColor: "rgba(255, 255, 255, 0.3)",
		// 			 margin: 0,
		// 			 minBorderMargin: 0,
		// 			 labelMargin: 20,
		// 			 hoverable: true,
		// 			 clickable: true,
		// 			 mouseActiveRadius: 6
		// 		 }
		// 	};
		// 	loadResponse('reset', undefined);
		// });
	};

	this.cashSummary = function () {
		loadResponse('loading', undefined);
		ReportFactory.cashSummary
		({
			branchId: this.branchId,
			startDate: this.startDate,
			endDate: this.endDate,
			deviceType: this.deviceType,
			dateOption: this.dateOption
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			adjustment = response;
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.paymentsByPayer = function () {
		loadResponse('loading', undefined);
		ReportFactory.paymentsByPayer
		({
			branchId: this.branchId,
			startDate: this.startDate,
			endDate: this.endDate,
			dateOption: this.dateOption
		}).$promise.then(function (response) {
			angular.forEach(response, function (dto, index) {
				response[index].$open = false;
				response[index].$position = index;
			});
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.salesTaxReport = function () {
		loadResponse('loading', undefined);
		ReportFactory.salesTaxReport({
			startDate: this.startDate,
			endDate: this.endDate
		}).$promise.then(function (response) {
			loadResponse('reset', response);
		});
	};

	/** End Accounting Reports **/

	/** Begin Financial Reports **/
	this.averageDaysToDeliverByDeviceType = function () {
		loadResponse('loading', undefined);
		ReportFactory.averageDaysToDeliverByDeviceType({
			startDate: this.startDate,
			endDate: this.endDate,
			branchId: this.branchId
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
			return response;
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	}

	this.billingSummary = function () {
		loadResponse('loading', undefined);
		ReportFactory.billingSummary({
			branchId: this.branchId,
			startDate: this.startDate,
			endDate: this.endDate,
			deviceType: this.deviceType
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			adjustment = response;
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.fabricationsPastDueDate = function () {
		loadResponse('loading', undefined)
		ReportFactory.fabricationsPastDueDate({
			branchId: this.branchId,
			startDate: this.startDate
		}).$promise.then(function (response) {
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
			loadResponse('reset', response);
			return response;
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.salesSummary = function () {
		loadResponse('loading', undefined);
		ReportFactory.salesSummary({
			branchId: this.branchId,
			startDate: this.startDate,
			endDate: this.endDate,
			dateOption: this.dateOption,
			usePatientBranch: this.usePatientBranch
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			adjustment.orthoticSalesSummary = response.orthotic;
			adjustment.prostheticSalesSummary = response.prosthetic;
			adjustment.pedorthicSalesSummary = response.pedorthic;
			adjustment.mastectomySalesSummary = response.mastectomy;
			adjustment.miscSalesSummary = response.misc;
			adjustment.allDeviceTypeSalesSummary = response.allDeviceType;
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.salesDetail = function () {
		loadResponse('loading', undefined);
		salesDetailResults.orthoticSalesDetail = 0;
		salesDetailResults.prostheticSalesDetail = 0;
		salesDetailResults.pedorthicSalesDetail = 0;
		salesDetailResults.mastectomySalesDetail = 0;
		salesDetailResults.miscSalesDetail = 0;
		ReportFactory.salesDetail({
			branchId: this.branchId,
			startDate: this.startDate,
			endDate: this.endDate,
			usePatientBranch: this.usePatientBranch
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			salesDetailResults.orthoticSalesDetail = response.orthotic;
			salesDetailResults.prostheticSalesDetail = response.prosthetic;
			salesDetailResults.pedorthicSalesDetail = response.pedorthic;
			salesDetailResults.mastectomySalesDetail = response.mastectomy;
			salesDetailResults.miscSalesDetail = response.misc;
			salesDetailResults.totalSalesDetail = response.total;
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.uncollected = function () {
		loadResponse('loading', undefined);
		ReportFactory.uncollected({
			branchId: this.branchId,
      startDate: this.startDate,
      endDate: this.endDate
    }).$promise.then(function (response) {
    	console.log(response)
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

	this.uncollectedExport = function () {

		loadResponse('loading', undefined);
		ReportFactory.uncollected({
			branchId: this.branchId,
			startDate: this.startDate,
			endDate: this.endDate
		}).$promise.then(function (response) {
			delete response.$promise;
			delete response.$resolved;

			function buildHeader (){

				return [
					'Claim Id',
					'Patient Id',
					'Patient Name',
					'SSN',
					'Date of Birth',
					'Street Address',
					'City',
					'State',
					'Zip Code',
					'Home Phone',
					'Work Phone',
					'Cell Phone',
					'Email',
					'Total Claim Amount',
					'Total Patient Payment',
					'Amount sent to collections',
					'Date Balance Pushed to Patient',
					'Date of Service',
					'Insurance Company',
					'Last Applied Payment Date',
					'Statement Sent Date',
					'Statement Comment',
					'Referring Physician',
					'Treating Practitioner',
					'Guarantor First Name',
					'Guarantor Last Name',
					'Guarantor Middle Initial',
					'Guarantor Address',
					'Guarantor City',
					'Guarantor State',
					'Guarantor Zipcode',
					'Guarantor Phone',
					'HCPC Codes',
					'HCPC Descriptions'
				].join(delimiter) + '\n'
			}

			var buildRow = function(item){
				var newRow = [];
				var patient = item.claim.prescription.patient;
				var prescription = item.claim.prescription;
				var claim = item.claim;
				var referringPhysician = item.claim.prescription.referringPhysician;
				var treatingPractioner = prescription.treatingPractitioner;
				var patientInsurance = claim.patientInsurance;
				var patientGuarantor = patient.patientGuarantor ? patient.patientGuarantor.contact : undefined;

				newRow.push(item.claimId);
				newRow.push(patient.id);
				newRow.push(UtilService.formatName(patient,'FL')),
				newRow.push(patient.ssn);
				newRow.push(moment(patient.dob).format('L'));
				newRow.push(patient.streetAddress);
				newRow.push(patient.city);
				newRow.push(patient.state);
				newRow.push(patient.zipcode);
				newRow.push(patient.homePhone);
				newRow.push(patient.workPhone);
				newRow.push(patient.cellPhone);
				newRow.push(patient.email);
				newRow.push(claim.totalClaimAmount);
				newRow.push(claim.totalPtResponsibilityPaid);
				newRow.push(claim.uncollected);

				newRow.push(claim.ptBalancePushedDate);
				newRow.push(claim.dateOfService);
				newRow.push(patientInsurance.insuranceCompany.name);
				newRow.push(moment(item.lastPaymentDate).format('L'))
				newRow.push(moment(item.sentDate).format('L'))
				newRow.push(item.comment);
				referringPhysician ? newRow.push(referringPhysician.firstName + ' ' + referringPhysician.lastName): newRow.push('');
				treatingPractioner ? newRow.push(treatingPractioner.firstName + ' ' + treatingPractioner.lastName): newRow.push('');

				patientGuarantor ? newRow.push(patientGuarantor.firstName) : newRow.push('');
				patientGuarantor ? newRow.push(patientGuarantor.lastName) : newRow.push('');
				patientGuarantor ? newRow.push(patientGuarantor.middleName) : newRow.push('');
				patientGuarantor ? newRow.push(patientGuarantor.streetAddress) : newRow.push('');
				patientGuarantor ? newRow.push(patientGuarantor.city) : newRow.push('');
				patientGuarantor ? newRow.push(patientGuarantor.state) : newRow.push('');
				patientGuarantor ? newRow.push(patientGuarantor.zipcode) : newRow.push('');
				patientGuarantor ? newRow.push(patientGuarantor.phoneNumber) : newRow.push('');

				var l_code_names = '';
				var l_code_desc = '';
				angular.forEach(item.l_codes, function (l_code) {
					l_code_names = l_code_names + l_code.lCode.name + '-';
					l_code_desc = l_code_desc + l_code.lCode.description + ' - ';
				});
				newRow.push(l_code_names);
				newRow.push(l_code_desc);

				var formatted = _.map(newRow, function(value){
					return value === null ? '' : JSON.stringify(value);
				});

				return formatted;
			}

			var csvExport = '';
			var delimiter = ',';
			var branchName = "All";
			var branchIndex = _.findIndex(BranchService.userBranches, function(o) { return o.id == _this.branchId; });

			if(branchIndex<0){
				branchName = BranchService.userBranches[branchIndex];
			}
			var fileName = "claims_sent_to_collections_" + [branchName, _this.startDate, _this.endDate].join('_') +  ".csv";

			csvExport += buildHeader();

			angular.forEach(response, function (row) {
				csvExport += buildRow(row).join(delimiter)+"\n";
			});

			var element = document.createElement('a');
			element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(csvExport));
			element.setAttribute('download', fileName);

			element.style.display = 'none';
			document.body.appendChild(element);
			element.click();
			document.body.removeChild(element);

			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');

		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

  this.billingsByDateOfService = function () {
    loadResponse('loading', undefined);
    billingsByDateOfServiceResults.withoutFirstClaimSubmissionDate = null;
    billingsByDateOfServiceResults.withFirstClaimSubmissionDate = null;
    billingsByDateOfServiceResults.totalAllowableWithout = 0;
    billingsByDateOfServiceResults.totalBillableWithout = 0;
    billingsByDateOfServiceResults.AmountPaidWithout = 0;
    billingsByDateOfServiceResults.totalAllowableWith = 0;
    billingsByDateOfServiceResults.totalBillableWith = 0;
    billingsByDateOfServiceResults.AmountPaidWith = 0;
    ReportFactory.billingsByDateOfService({
      branchId: this.branchId,
      startDate: this.startDate,
      endDate: this.endDate,
	    usePatientBranch: this.usePatientBranch
    }).$promise.then(function (response) {
      loadResponse('reset', response);
      billingsByDateOfServiceResults.withoutFirstClaimSubmissionDate = response.withoutFirstClaimSubmissionDate;
      angular.forEach(response.withoutFirstClaimSubmissionDate, function (item) {
        billingsByDateOfServiceResults.totalAllowableWithout += item.totalAllowable;
        billingsByDateOfServiceResults.totalBillableWithout += item.totalBillable;
        billingsByDateOfServiceResults.AmountPaidWithout += item.amountPaid;
      });
      billingsByDateOfServiceResults.withFirstClaimSubmissionDate = response.withFirstClaimSubmissionDate;
      angular.forEach(response.withFirstClaimSubmissionDate, function (item) {
        billingsByDateOfServiceResults.totalAllowableWith += item.totalAllowable;
        billingsByDateOfServiceResults.totalBillableWith += item.totalBillable;
        billingsByDateOfServiceResults.AmountPaidWith += item.amountPaid;
      });
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };


  this.generalBatchSummaryList = function () {
    loadResponse('loading', undefined);
    ReportFactory.generalBatchSummaryList({
      startDate: this.startDate,
      endDate: this.endDate
    }).$promise.then(function (response) {
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.claimsSummary = function () {
		loadResponse('loading', undefined);
		ReportFactory.claimsSummary({
			startDate: this.startDate,
			endDate: this.endDate,
			branchId: this.branchId,
			dateType: this.dateType
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.claimsSummaryPivotTableExport = function () {
		loadResponse('loading', undefined);
		ReportFactory.claimsSummaryPivotTableExport({
			startDate: this.startDate,
			endDate: this.endDate,
			branchId: this.branchId
		}).$promise.then(function (response) {
			var element = document.createElement('a');
			element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(response.data));
			element.setAttribute('download', 'claimsSummaryPivotTableExport.csv');

			element.style.display = 'none';
			document.body.appendChild(element);
			element.click();
			document.body.removeChild(element);
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.insuranceCompaniesBilledList = function () {
		loadResponse('loading', undefined);
		ReportFactory.insuranceCompaniesBilled({
			branchId: this.branchId,
			startDate: this.startDate,
			endDate: this.endDate
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.outstandingInsuranceBalances = function () {
		// loadResponse('loading', undefined);
		// ReportFactory.outstandingInsuranceBalances({
		// 	branchId: this.branchId,
		// 	useSubmissionDate: this.useSubmissionDate,
		// 	hideNegativeBalance: this.hideNegativeBalance,
		// 	useBillable: this.balanceType
		// }).$promise.then(function (response) {
		// 	// _this.insuranceBalances = _this.insuranceCompaniesWithOutstandingBalance.entityList;
		// 	angular.forEach(response.entityList, function (item, index) {
		// 		item.$open = false;
		// 		item.$position = index;
		// 	});
		// 	loadResponse('reset', response);
		// 	UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		// }, function (err) {
		// 	UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		// });
	};

	this.expandCollapse = function () {
		_this.isExpand = !_this.isExpand;
		for (var i = 0; i < _this.reportResults().entityList.length; i++) {
			if (_this.reportResults().entityList != null) {
				_this.reportResults().entityList[i].$open = _this.isExpand;
			}
		}
	};

	this.expandCollapseSalesDetail = function () {
		_this.isExpand = !_this.isExpand;

		var results = _this.getSalesDetailResults();
		if(results){
			var i;
			if(results.orthoticSalesDetail && results.orthoticSalesDetail.claimsSummaryDTOList) {
				for (i = 0; i < results.orthoticSalesDetail.claimsSummaryDTOList.length; i++) {
					results.orthoticSalesDetail.claimsSummaryDTOList[i].$open = _this.isExpand;
				}
			}

			if(results.prostheticSalesDetail && results.prostheticSalesDetail.claimsSummaryDTOList) {
				for (i = 0; i < results.prostheticSalesDetail.claimsSummaryDTOList.length; i++) {
					results.prostheticSalesDetail.claimsSummaryDTOList[i].$open = _this.isExpand;
				}
			}

			if(results.pedorthicSalesDetail && results.pedorthicSalesDetail.claimsSummaryDTOList) {
				for (i = 0; i < results.pedorthicSalesDetail.claimsSummaryDTOList.length; i++) {
					results.pedorthicSalesDetail.claimsSummaryDTOList[i].$open = _this.isExpand;
				}
			}

			if(results.miscSalesDetail && results.miscSalesDetail.claimsSummaryDTOList) {
				for (i = 0; i < results.miscSalesDetail.claimsSummaryDTOList.length; i++) {
					results.miscSalesDetail.claimsSummaryDTOList[i].$open = _this.isExpand;
				}
			}

		}
	};

	this.monthlyBillingsCollected = function () {
		loadResponse('loading', undefined);
		ReportFactory.monthlyBillingsCollected({
			branchId: this.branchId,
			numberMonths: this.monthlyBillingsMonths
		}).$promise.then(function (response) {
			loadResponse('reset', undefined);
			var billings = response.billings;
			var collected = response.collected;
			var months = response.months;
			var positions = response.positions;
			var monthsArray = [];
			var billingsArray = [];
			var collectionsArray = [];
			var percentageArray = [];

			angular.forEach(months, function (month, index) {
				if (month < months[index - 1]) {
					months[index] = months[index] + 12;
				}
			});

			// use the positions array with index to fill the months array
			positions.forEach(function (value, index) {
				var m = months[index];
				if (m > 11) {
					m -= 12;
				}
				monthsArray.push([months[value], monthAbbrs[m]]);
			});

			billings.forEach(function (billing, index) {
				billingsArray.push([months[index], billing]);
			});

			collected.forEach(function (c, index) {
				collectionsArray.push([months[index], c]);
				if (billingsArray[index][1] != 0) {
					var percentage = Math.round(c / billingsArray[index][1] * 100);
				} else {
					percentage = 0;
				}
				percentageArray.push([months[index], percentage]);
			});

			var barWidth = (0.6 * months.length / 10);
			var barOptions = {
				show: true,
				barWidth: barWidth,
				lineWidth: 0,
				align: "center",
				fillColor: {colors: [{opacity: 0.3}, {opacity: 0.8}]}
			};

			monthlyBillingsCollectedDataset = [
				{
					data: billingsArray,
					label: "Billings",
					bars: barOptions
				},
				{
					data: collectionsArray,
					label: "Collected",
					bars: barOptions
				}
			];

			var yAxisOptions = {
				yaxis: {
					tickFormatter: function (v, axis) {
						return $filter('currency')(v);
					},
					font: {
						color: "#FFF",
						size: 10
					}
				}
			};

			var xAxisOptions = {
				xaxis: {
					ticks: monthsArray,
					font: {
						color: "#FFF",
						size: 10
					}
				},
				tooltip: true,
				tooltipOpts: {
					content: function (label, xval, yval, flotItem) {
						var percentMonth = 0;
						for (var i = 0; i < monthsArray.length; i++) {
							if (percentageArray[i][0] == xval) {
								percentMonth = i;
							}
						}
						return "%s: %y </br>Percentage Collected: " + percentageArray[percentMonth][1] + "%";
					},
					defaultTheme: false,
					shifts: {
						x: 0,
						y: 20
					}
				}
			};

			var tmpX = Object.assign(defaultChartOptions, xAxisOptions);
			var tmpY = Object.assign(defaultChartOptions, yAxisOptions);
			monthlyBillingsCollectedOptions = Object.assign(tmpX, tmpY);

		});
	};

	this.outstandingPatientResponsibilityBalances = function () {
		loadResponse('loading', undefined);
		ReportFactory.outstandingPatientResponsibilityBalances({branchId: this.branchId}).$promise.then(function (response) {
			angular.forEach(response, function (claim, index) {
				ClaimSubmissionFactory.findByClaimId({claimId: claim.id}).$promise.then(function (claimSubmission) {
					response[index].$claimSubmission = claimSubmission[claimSubmission.length - 1];
					if (response[index].$claimSubmission && response[index].$claimSubmission.submissionDate) {
						response[index].$days = moment().diff(response[index].$claimSubmission.submissionDate, 'days');
					} else
						response[index].$days = '';
				});
			});
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.patientRefunds = function () {
		loadResponse('loading', undefined);
		ReportFactory.patientRefunds({branchId: this.branchId}).$promise.then(function (response) {
			angular.forEach(response, function (claim, index) {
				UserFactory.get({id: claim.prescription.treatingPractitionerId}).$promise.then(function (practitioner) {
					response[index].$treatingPractitioner = practitioner;
				});
				response[index].$days = moment().utc().diff(response[index].prescription.createdAt, 'days');
			});
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		})
	};

	this.totalBilled = function () {
		loadResponse('loading', undefined);
		ReportFactory.totalBilled({
			startDate: this.startDate,
			endDate: this.endDate
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			totalBilledDto = response;
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.userBatchSummaryList = function () {
		loadResponse('loading', undefined);
		ReportFactory.userBatchSummaryList({
			userId: this.userId,
			startDate: this.startDate,
			endDate: this.endDate,
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.unClaimedPrescriptions = function () {
		loadResponse('loading', undefined);
		ReportFactory.unclaimedPrescriptions({
			branchId: this.branchId,
			completeStatus: this.completeStatus,
			startDate: this.startDate
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.wipBillingsCollected = function () {
		loadResponse('loading', undefined);
		ReportFactory.wipBillingsCollected({
			branchId: this.branchId
		}).$promise.then(function (prescriptions) {
			loadResponse('reset', prescriptions);
		}, function (error) {
			console.log(error);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.yearlyBillingsCollected = function () {
		loadResponse('loading', undefined);
		ReportFactory.yearlyBillingsCollected({
			branchId: this.branchId,
			startYear: this.yearlyBillingsCollectedStartYear,
			endYear: this.yearlyBillingsCollectedEndYear
		}).$promise.then(function (response) {
			loadResponse('reset', undefined);
			var billings = response.billings;
			var collected = response.collected;
			var years = response.years;
			var yearsArray = [];
			var billingsArray = [];
			var collectionsArray = [];

			years.forEach(function (year) {
				yearsArray.push([year, Math.abs(year).toString()]);
			});

			billings.forEach(function (billing, index) {
				billingsArray.push([years[index], billing]);
			});

			collected.forEach(function (c, index) {
				collectionsArray.push([years[index], c])
			});

			var barWidth = (0.6 * years.length / 10);
			var barOptions = {
				show: true,
				barWidth: barWidth,
				lineWidth: 0,
				align: "center",
				fillColor: {colors: [{opacity: 0.3}, {opacity: 0.8}]}
			};

			yearlyBillingsCollectedDataset = [
				{
					data: billingsArray,
					label: "Billings",
					bars: barOptions
				},
				{
					data: collectionsArray,
					label: "Collected",
					bars: barOptions
				}
			];

			var yAxisOptions = {
				yaxis: {
					tickFormatter: function (v, axis) {
						return $filter('currency')(v);
					},
					font: {
						color: "#FFF",
						size: 10
					}
				}
			};

			var xAxisOptions = {
				xaxis: {
					ticks: yearsArray,
					font: {
						color: "#FFF",
						size: 10
					}
				},
				tooltip: true,
				tooltipOpts: {
					content: "%s: %y",
					defaultTheme: false,
					shifts: {
						x: 0,
						y: 20
					}
				}
			};

			var tmpX = Object.assign(defaultChartOptions, xAxisOptions);
			var tmpY = Object.assign(defaultChartOptions, yAxisOptions);
			yearlyBillingsCollectedOptions = Object.assign(tmpX, tmpY);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.dailyClose = function () {
		loadResponse('loading', undefined);
		ReportFactory.dailyClose({
			payeeType: this.payeeType,
			dateOption: this.dateOption,
			startDate: this.startDate,
			endDate: this.endDate,
			branchId: this.branchId
		}).$promise.then(function (response) {
			response.$open = false;
			_this.dailyCloseReportDto = response;
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.getAdjustmentTypeLabel = function(adjustmentTypeId) {
		// true for exact match (Important)
		return $filter('filter')(this.adjustmentTypes, {id: adjustmentTypeId}, true)[0].name;
	};

	this.salesDetailExport = function () {
		$("#sales-detail-export").button('loading')
		ReportFactory.salesDetailExport().$promise.then(function (response) {
			$("#sales-detail-export").button('reset')
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');

			var element = document.createElement('a');
			element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(response.data));
			element.setAttribute('download', 'sales-detail-export.csv');

      element.style.display = 'none';
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.practitionerCommissions = function () {
    loadResponse('loading', undefined);
    ReportFactory.practitionerCommissions({
      startDate: this.startDate,
      endDate: this.endDate,
      branchId: this.branchId,
      dateOption: this.dateOption,
      deviceType: this.deviceType,
    }).$promise.then(function (response) {
      loadResponse('reset', response);
    });
  };

  this.representativeCommissions = function () {
    loadResponse('loading', undefined);
    ReportFactory.representativeCommissions({
      startDate: this.startDate,
      endDate: this.endDate,
      branchId: this.branchId,
      dateOption: this.dateOption,
      deviceType: this.deviceType,
    }).$promise.then(function (response) {
      loadResponse('reset', response);
    });
  };

  /** End Financial Reports **/

  /** Begin Office Reports **/
  this.appointmentList = function () {
	  var startDateTime = $moment(this.startDate).startOf('day').toISOString();
	  var endDateTime = $moment(this.endDate).endOf('day').toISOString();
	  loadResponse('loading', undefined);
	  ReportFactory.appointmentList({
		  branchId: this.branchId,
		  appointmentTypeId: this.appointmentTypeId,
		  startDateTime,
		  endDateTime
	  }).$promise.then(function (response) {
		  loadResponse('reset', response);
		  UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
	  }, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.appointmentListCanceled = function () {
		var startDateTime = $moment(this.startDate).startOf('day').toISOString();
		var endDateTime = $moment(this.endDate).endOf('day').toISOString();
		loadResponse('loading', undefined);
		ReportFactory.appointmentsByStatusWithNoReschedule({
			status: "cancelled",
			startDateTime,
			endDateTime,
			branchId: this.branchId
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.appointmentListNoShow = function () {
		var startDateTime = $moment(this.startDate).startOf('day').toISOString();
		var endDateTime = $moment(this.endDate).endOf('day').toISOString();
		loadResponse('loading', undefined);
		ReportFactory.appointmentsByStatusWithNoReschedule({
			status: "no_show",
			startDateTime,
			endDateTime,
			branchId: this.branchId
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.surveyResponse = function () {
		loadResponse('loading', undefined);
		this.answerTwoCount = {one: 0, two: 0, three: 0, four: 0, five: 0};
		this.answerThreeCount = {one: 0, two: 0, three: 0, four: 0, five: 0};
		var exclusiveEndDate = $moment(_this.endDate).add(1, 'days').format('YYYY-MM-DD');
		ReportFactory.surveyResponses({
			branchId: this.branchId,
			startDate: this.startDate,
			endDate: exclusiveEndDate
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			angular.forEach(response, function (survey) {
				switch (survey.answerTwo) {
					case 1:
						_this.answerTwoCount.one = _this.answerTwoCount.one + 1;
						break;
					case 2:
						_this.answerTwoCount.two = _this.answerTwoCount.two + 1;
						break;
					case 3:
						_this.answerTwoCount.three = _this.answerTwoCount.three + 1;
						break;
					case 4:
						_this.answerTwoCount.four = _this.answerTwoCount.four + 1;
						break;
					case 5:
						_this.answerTwoCount.five = _this.answerTwoCount.five + 1;
						break;
				}
				switch (survey.answerThree) {
					case 1:
						_this.answerThreeCount.one = _this.answerThreeCount.one + 1;
						break;
					case 2:
						_this.answerThreeCount.two = _this.answerThreeCount.two + 1;
						break;
					case 3:
						_this.answerThreeCount.three = _this.answerThreeCount.three + 1;
						break;
					case 4:
            _this.answerThreeCount.four = _this.answerThreeCount.four + 1;
            break;
          case 5:
            _this.answerThreeCount.five = _this.answerThreeCount.five + 1;
            break;
        }
      })
    });
  };

  this.prescriptionsWithoutSurveyText = function () {
    loadResponse('loading', undefined);
	  _this.checked = [];
    ReportFactory.prescriptionsWithoutSurveyText({
      branchId: this.branchId,
      startDate: this.startDate,
      endDate: this.endDate
    }).$promise.then(function (response) {
      angular.forEach(response, function (dto) {
        if (_this.checked[dto.prescriptionId] === undefined || dto.surveyCompleted === false) _this.checked[dto.prescriptionId] = true;
        if (dto.surveyCompleted === true) _this.checked[dto.prescriptionId] = false;
      });
      SystemSettingFactory.findBySectionAndField({
        section: "general",
        field: "survey_link_type"
      }).$promise.then(function (setting) {
        _this.surveyLinkType = setting.value;
        loadResponse('reset', response);
        UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
      });
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.prescriptionsOnHold = function () {
    loadResponse('loading', undefined);
    ReportFactory.prescriptionsOnHold({
      startDate: this.startDate,
      endDate: this.endDate
    }).$promise.then(function (response) {
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.surveyTextMessagesForAppointmentTypes = function () {
	  var startDateTime = $moment(this.startDate).startOf('day').toISOString();
	  var endDateTime = $moment(this.endDate).endOf('day').toISOString();
	  loadResponse('loading', undefined);
	  _this.checked = [];
	  ReportFactory.surveyTextMessagesForAppointmentTypes({
		  branchId: this.branchId,
		  startDateTime: startDateTime,
		  endDateTime: endDateTime,
		  appointmentTypeId: this.appointmentTypeId,
		  appointmentStatusName: this.appointmentStatusName,
		  surveySent: this.surveySent,
		  startSurveySentDate: this.startSurveySentDate,
		  endSurveySentDate: this.endSurveySentDate
	  }).$promise.then(function (response) {
      angular.forEach(response, function (dto) {
        if (_this.checked[dto.appointmentId] === undefined || dto.surveySent === false) {
          _this.checked[dto.appointmentId] = true;
        }
        if (dto.surveySent === true) {
          _this.checked[dto.appointmentId] = false;
        }
      });
      // SystemSettingFactory.findBySectionAndField({
      //   section: "general",
      //   field: "survey_link_type"
      // }).$promise.then(function (setting) {
      //   _this.surveyLinkType = setting.value;
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
      // });
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.checkSmsComplianceUser = function () {
    loadResponse('loading', undefined);
    ReportFactory.checkSmsComplianceUser({
      userId: this.userId,
      phoneNumber: this.phoneNumber
    }).$promise.then(function (response) {
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
      // });
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.checkSmsCompliancePatient = function () {
    loadResponse('loading', undefined);
    ReportFactory.checkSmsCompliancePatient({
      branchId: this.branchId,
      patientId: this.patientId,
      phoneNumber: this.phoneNumber
    }).$promise.then(function (response) {
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
      // });
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.sentTextMessageDetails = function () {
    loadResponse('loading', undefined);
    ReportFactory.sentTextMessageDetails({
      branchId: this.branchId,
      appointmentTypeId: this.appointmentTypeId,
      patientId: this.patientId,
      phoneNumber: this.phoneNumber,
      startConfirmationRequestSentDate: this.startConfirmationRequestSentDate,
      endConfirmationRequestSentDate: this.endConfirmationRequestSentDate
    }).$promise.then(function (response) {
      angular.forEach(response, function (dto) {
        if (_this.checked[dto.appointmentId] === undefined || dto.surveySent === false) {
          _this.checked[dto.appointmentId] = true;
        }
        if (dto.surveySent === true) {
          _this.checked[dto.appointmentId] = false;
        }
      });
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
      // });
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.patientsByBranch = function (branchId) {
    this.patients = PatientFactory.searchBy({
      active: null,
      branchId: branchId
    });
  };

  this.prescriptionsOnHold = function () {
    loadResponse('loading', undefined);
    ReportFactory.prescriptionsOnHold({
      startDate: this.startDate,
      endDate: this.endDate
    }).$promise.then(function (response) {
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };


  /** End Office Reports **/


  /** Begin Practice Reports **/
  this.billingsByPractitioner = function () {
    loadResponse('loading', undefined);
    _this.billingsByPractitionerTotalBilled = 0;
    _this.billingsByPractitionerTotalAllowable = 0;
		ReportFactory.billingsByPractitioner({
			branchId: this.branchId,
			startDate: this.startDate,
			endDate: this.endDate,
			lCodeDisplay: this.lCodeDisplay,
			usePatientBranch: this.usePatientBranch
		}).$promise.then(function (response) {
			angular.forEach(response, function (practitionerBilling, index) {
				response[index].$open = false;
        response[index].$position = index;
        _this.billingsByPractitionerTotalBilled += response[index].overallBillingTotal;
        _this.billingsByPractitionerTotalAllowable += response[index].overallAllowableTotal;
      });
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.billingsByRepresentative = function () {
    loadResponse('loading', undefined);
    _this.billingsByViewUserTotalBilled = 0;
    _this.billingsByViewUserTotalAllowable = 0;
    ReportFactory.billingsByRepresentative({
      branchId: this.branchId,
      startDate: this.startDate,
      endDate: this.endDate,
      lCodeDisplay: this.lCodeDisplay,
	    usePatientBranch: this.usePatientBranch
    }).$promise.then(function (response) {
      angular.forEach(response, function (viewUserBilling, index) {
        response[index].$open = false;
        response[index].$position = index;
        _this.billingsByViewUserTotalBilled += response[index].overallBillingTotal;
        _this.billingsByViewUserTotalAllowable += response[index].overallAllowableTotal;
      });
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.practitionerBillingsByCategory = function () {
    loadResponse('loading', undefined);
    ReportFactory.practitionerBillingsByCategory({
      userId: this.userId,
      startDate: this.startDate,
      endDate: this.endDate
    })
      .$promise
      .then(function (response) {
				loadResponse('reset', response);
				UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
			}, function (err) {
				UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
			});
	};

	this.lCodeBilledCharges = function () {
		loadResponse('loading', undefined);
		ReportFactory.lCodeBilledCharges({
			startDate: this.startDate,
			endDate: this.endDate
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.monthlyPhysicianVisitCount = function () {
		var startDateTime = $moment(this.startDate).startOf('day').toISOString();
		loadResponse('loading', undefined);
		ReportFactory.monthlyPhysicianVisitCount({
			startDateTime: startDateTime
		}).$promise
			.then(function (response) {
				loadResponse('reset', response);
				UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
			}, function (err) {
				UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
			});
	};

	this.unapprovedNotes = function () {
		loadResponse('loading', undefined);
		ReportFactory.unapprovedNotes({
			userId: this.userId,
			startDate: this.startDate,
			endDate: this.endDate,
			branchId: this.branchId
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};
	/** End Practice Reports **/

	/** Begin Market Reports **/
	this.billingsByReferringPhysician = function () {
		loadResponse('loading', undefined);
		ReportFactory.billingsByReferringPhysician({
			startDate: this.startDate,
			endDate: this.endDate,
			branchId: this.branchId,
			usePatientBranch: this.usePatientBranch
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.newPrescriptionsByMonth = function () {
		loadResponse('loading', undefined);
		ReportFactory.newPrescriptionsByMonth({
			branchId: this.branchId,
			numberMonths: this.monthlyNewPatientsMonths
		}).$promise.then(function (response) {
			loadResponse('reset', undefined);

			var newPrescriptions = response.newPrescriptions;
			var months = response.months;
			var positions = response.positions;

			var monthsArray = [];
			var newPrescriptionsArray = [];

			// use the positions array with index to fill the months array
			positions.forEach(function (value, index) {
				var m = months[index];
				monthsArray.push([value, monthAbbrs[m]]);
			});

			newPrescriptions.forEach(function (rx, index) {
				newPrescriptionsArray.push([months[index], rx]);
			});

			var barWidth = (0.6 * months.length / 10);

			monthlyNewPrescriptionsDataset = [
				{
					data: newPrescriptionsArray,
					label: "# RX",
					bars: {
						show: true,
						barWidth: barWidth,
						lineWidth: 0,
						align: "center",
						fillColor: {colors: [{opacity: 0.3}, {opacity: 0.8}]}
					}
				}
			];

			var yAxisOptions = {
				yaxis: {
					tickFormatter: function (v, axis) {
						return $filter('number')(v);
					},
					font: {
						color: "#FFF",
						size: 10
					}
				}
			};

			var xAxisOptions = {
				xaxis: {
					ticks: monthsArray,
					font: {
						color: "#FFF",
						size: 10
					}
				},
				tooltip: true,
				tooltipOpts: {
					content: "New Patient Rx: %y",
					defaultTheme: false,
					shifts: {
						x: 0,
						y: 20
					}
				}
			};
			var tmpX = Object.assign(defaultChartOptions, xAxisOptions);
			var tmpY = Object.assign(defaultChartOptions, yAxisOptions);
			monthlyNewPrescriptionsOptions = Object.assign(tmpX, tmpY);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};
	/** End Market Reports **/

	/** Begin Misc. Reports **/
	this.claimsWithAdditionalComment = function () {
		loadResponse('loading', undefined);
		ReportFactory.claimsWithAdditionalComment({branchId: this.branchId}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.deliveredPrescriptionsList = function () {
		loadResponse('loading', undefined);
		ReportFactory.deliveredPrescriptionList({
			branchId: this.branchId,
			startDate: this.startDate,
			endDate: this.endDate
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.diagnosisCodeList = function () {
		loadResponse('loading', undefined);
		ReportFactory.diagnosisCodeList({
			codeSet: this.codeSet,
			active: this.status
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.lCodeAlertsList = function () {
		loadResponse('loading', undefined);
		ReportFactory.lCodeAlertsList({
			insuranceCompanyId: this.insuranceCompanyId
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.lowInventoryStockList = function () {
    loadResponse('loading', undefined);
    ReportFactory.lowInventoryStockList({
      branchId: this.branchId
    }).$promise.then(function (response) {
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.noteList = function () {
    loadResponse('loading', undefined);
    ReportFactory.noteList({
      userId: this.userId,
      startDate: this.startDate,
      endDate: this.endDate,
      branchId: this.branchId,
      noteType: this.noteType
    }).$promise.then(function (response) {
      loadResponse('reset', response);
      UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
    }, function (err) {
      UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.patientsByCategoryList = function () {
    loadResponse('loading', undefined);
    var params = {
      branchId: this.branchId,
      category: this.orthoticOrProsthetic,
      startDate: this.startDate,
      endDate: this.endDate
    };
    ReportFactory.patientsByCategoryList(params).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.patientBirthdayList = function () {
		loadResponse('loading', undefined);
		ReportFactory.patientBirthdayList({
			branchId: this.branchId,
			dobMonth: this.dobMonth
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.patientList = function () {
		loadResponse('loading', undefined);
		PatientFactory.searchBy({
			active: this.status,
			branchId: this.branchId
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.patientsWithCriticalMessageList = function () {
		loadResponse('loading', undefined);
		ReportFactory.patientsWithCriticalMessage({
			startDate: this.startDate,
			endDate: this.endDate,
			branchId: this.branchId
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.patientByReferralSource = function () {
		loadResponse('loading', undefined);
		ReportFactory.patientByReferralSource({
			startDate: this.startDate,
			endDate: this.endDate,
			branchId: this.branchId
		}).$promise.then(function (response) {
			loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.dashBoardsByQuickSight = function () {
    _this.quickSightloading = true;
    ReportFactory.dashBoardsByQuickSight().$promise.then(function (response) {
      _this.quickSightloading = false;
      _this.qsdashboardLink = response;
    }, function (err) {
    });
  };

  this.physicianList = function () {
    loadResponse('loading', undefined);
    PhysicianFactory.search({q: "", active: this.status, page: 0, size: 10000}).$promise.then(function (response) {
	    loadResponse('reset', response);
	    UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
    }, function (err) {
	    UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
    });
  };

  this.sendAppointmentSurvey = function () {
    var surveyAppointmentList = [];
    angular.forEach(_this.checked, function (checked, index) {
      if (checked === true)
        surveyAppointmentList.push(index);
    });
	  if (confirm("Are you sure you want to text a survey to the selected patients?")) {
		  SurveyFactory.sendAppointmentSurvey(surveyAppointmentList).$promise.then(function (response) {
			  UtilService.displayAlert('success', '<p>Report generated and surveys sent successfully.</p>', '#header-alert-container');
		  }, function (err) {
			  UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		  });
	  }
  };

  this.sendSurvey = function () {
    var surveyList = [];
    angular.forEach(_this.checked, function (checked, index) {
      if (checked === true)
        surveyList.push(index);
    });
	  if (confirm("Are you sure you want to text a survey to the selected patients?")) {
		  SurveyFactory.sendSurvey(surveyList).$promise.then(function (response) {
			  UtilService.displayAlert('success', '<p>Report generated and surveys sent successfully.</p>', '#header-alert-container');
		  }, function (err) {
			  UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		  });
	  }
  };

  this.authExpiration = function () {
    loadResponse('loading', undefined);
    var params = {
      branchId: this.branchId,
      startDate: this.startDate,
      endDate: this.endDate
    };
    ReportFactory.expiringPreAuths(params).$promise.then(function (response) {
      loadResponse('reset', response);
			UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.clericalProductivity = function () {
		loadResponse('loading', undefined);
		var params = {
			branchId: _this.branchId,
			startDate: _this.startDate,
			endDate: _this.endDate
		};
		ReportFactory.clericalProductivity(params).$promise.then(function (response) {
			loadResponse('reset', response);
		});
	};

	var _thisExport = this;
	this.exportAll = function () {
			loadResponse('loading', undefined);
			$http({
				url: 'api/report/ad-hoc/export/' + _this.classname,
				transformRequest: angular.identity,
				responseType: 'arraybuffer',
				method: 'GET',
				headers: {
					'Content-type': 'application/json'
				},
				params: {
					includeFields: _this.includeFields,
					includeKeys: _this.includeKeys
				}
			}).success(function (data, status, headers, config) {
				var blob = new Blob([data], {
					type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
				});
				var anchor = angular.element('<a/>');
				anchor.css({display: 'none'});
				angular.element(document.body).append(anchor);
				anchor.attr({
					href: (window.URL || window.webkitURL).createObjectURL(blob),
					target: '_blank',
					download: _this.classname + '.xls'
				})[0].click();
				anchor.remove();
				loadResponse('reset');
			}).error(function (data, status, headers, config) {
				console.log("Error getting excel file.")
			});

	};

	this.classMetadata = function () {
		_this.classFields = [];
		_this.classKeys = [];
		_this.includeFields = [];
		_this.includeKeys = [];
		ReportFactory.tableMetadata({className: _this.classname}).$promise.then(function (response) {
			angular.forEach(response, function (clazz, index) {
        if (clazz.name != 'serialVersionUID') {
          if (clazz.join === 'false') {
            _this.classFields.push(clazz);
            _this.includeFields.push(clazz.name);
          } else {
            _this.classKeys.push(clazz);
            _this.includeKeys.push(clazz.type);
          }
        }
      });
			// UtilService.displayAlert('success', '<p>Report generated successfully.</p>', '#header-alert-container');
		}, function (err) {
			UtilService.displayAlert('danger', '<p>There has been an error generating your report.</p>', '#header-alert-container');
		});
	};

	this.updatePreferences = function (temp) {

		ReportFactory.tableFields({className: _this.classname}).$promise.then(function (response) {
			_this.classFields = response;
		});

		ReportFactory.tableForeignKeys({className: _this.classname}).$promise.then(function (response) {
			_this.classForeignKeys = response;
		});
	};

	this.includedFieldsList = function (field) {
		var index = _this.includeFields.indexOf(field);
		if (index > -1) {
			_this.includeFields.splice(index, 1);
		} else {
			_this.includeFields.push(field);
		}
	};

	this.includedKeysList = function (key) {
		var index = _this.includeKeys.indexOf(key)
		if (index > -1) {
			_this.includeKeys.splice(index, 1);
		} else {
			_this.includeKeys.push(key);
		}
	};

	// Function that takes a data set and a key and sums the numerical contents of the associated values
	this.sumByColumn = function (collection, column) {
		var total = 0;
		collection.forEach(function (item) {
			total += (item[column]);
		});
		return total;
	};

	this.openAll = function (collection) {
		collection.forEach(function (dto) {
			if (_this.isOpen == true) {
				dto.$open = false;
			} else {
				dto.$open = true;
			}
		});
		_this.isOpen = !_this.isOpen;
	};

	this.overdueRental = function () {
    loadResponse('loading', undefined);
    var params = {
      branchId: this.branchId,
      startDate: this.startDate
    };
    ReportFactory.overdueRental(params).$promise.then(function (response) {
      loadResponse('reset', response);
    });
  };

  this.syncSurveySentDates = function () {
    if (this.surveySent == 'sent' && this.startSurveySentDate == $moment().format('YYYY-MM-DD') && this.endSurveySentDate == $moment().format('YYYY-MM-DD')) {
      this.startSurveySentDate = this.startDate;
      this.endSurveySentDate = this.endDate;
    }
  }
}
