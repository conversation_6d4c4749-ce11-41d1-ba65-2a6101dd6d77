app.service('UtilService', UtilService);
UtilService.$inject = ['$state', '$filter', '$window', '$cookies', '$sce'];

function UtilService($state, $filter, $window, $cookies, $sce) {

	var _this = this;

	// SCRUM-4887: let the people pick between ATP and Practitioner
	// --still need to look at the role named "practitioner" and things like appointment status "in_room_with_practitioner"
	//    These are punts for now and will probably be tackled in the V2 version of this
	this.practitionerNoun = "Practitioner";
	this.prescriptionNoun = "Prescription";
	this.prescriptionAbbreviation = "Rx";
	this.zipCodeRegEx = /^(\d{5}(-\d{4})?)$/;
	// this.phonePattern = /^(\d{5}(-\d{4})?)$/;

	this.openPrintScreen = function (url) {
		var printWindow = window.open(url, '_blank');
	};

	this.navigate = function (state, params) {
		// $state.go(state, params);
		var url = $state.href(state, params);
		var printWindow = window.open(url, '_blank');
	};

	this.getUserProfilePhoto = function (id, color, lastName) {
		var result = "<img class='img-circle size-30x30' src='images/no-user-image.png'>";
		if (id !== undefined) {
			var url = 'uploads/user_profile_photos/' + id + '.png';
			var img = new Image();
			img.onerror = function () {
				return false;
				/*console.log("User image not found, using default");*/
			};
			img.src = url;
			if (img.height !== 0) {
				result = "<img class=\"img-circle size-30x30\" src='" + url + "'>";
			}
		}
		return result;
	}

	this.getUserProfilePhotoForAppointment = function (appointment, userGroups) {
		var result;
		var url = 'uploads/user_profile_photos/' + appointment.userId + '.png';
		var img = new Image();
		img.onerror = function () {
			console.log("User image not found, using default");
		};
		img.src = url;
		if (img.height !== 0) {
			result = "<img class=\"img-circle\" src='" + url + "'>";
		} else if (appointment.userType === 'Single User') {
			result = "<div class=\"media-object img-circle bg-primary mr-0\">" + appointment.user.lastName.substring(0, 1) + "</div>";
		} else if (appointment.userType === 'User Class') {
			result = "<div class=\"media-object img-circle bg-primary mr-0\">" + userGroups[appointment.userTypeId]['name'][0] + "</div>";
		} else {
			result = "<div class=\"media-object img-circle bg-primary mr-0\">" + appointment.userType + "</div>";
		}
		return result;
	}

	/**
	 *
	 * @param type e.g. warning, success, danger
	 * @param message
	 * @param container e.g. alert-container
	 */
	this.displayAlert = function (type, message, container) {
		container = container || "";
		container = container === "" ? "#alert-container" : container;
		$(container).html(message);
		$(container).attr("class", "alert alert-" + type).show();

		// Don't auto-hide alerts in the change-password-alert-container
		if (container !== '#change-password-alert-container') {
			setTimeout(function () {
				$(container).slideUp('slow');
			}, 10000);
		}
	}

	this.formatName = function (obj, format) {
		if (obj === undefined || obj === null) return "";

		if (!format) format = "LFMC"

		var formats = ["LFM", "LFMC", "LF", "LFC", "FML", "FMLB", "FMLC", "FL", "FLC", "LFMi", "FMiL", "FMiLC", "LFMiC", "FiL"];
		format = formats.indexOf(format) > -1 ? format : "LFM";

		var name = "";
		var firstName = obj.firstName ? obj.firstName : "";
		var lastName = obj.lastName ? obj.lastName : "";
		var middleName = obj.middleName ? obj.middleName : "";
		var credentials = obj.credentials ? obj.credentials.trim() : "";
		var birthday = obj.dob ? obj.dob : "";

		var suffix = obj.suffix ? obj.suffix.trim() : "";
		if (["LFM", "LFMC", "FML", "FMLB", "FMLC", "LFMi", "FMiL", "FMiLC", "LFMiC"].indexOf(format) > -1) {
			if (format === "LFM") {
				name = lastName + ", " + firstName + (middleName === "" ? "" : " " + middleName) + (suffix === "" ? "" : " " + suffix);
			} else if (format === "LFMC") {
				name = lastName + ", " + firstName + (middleName == "" ? "" : " " + middleName) + (suffix === "" ? "" : " " + suffix) + (credentials === "" ? "" : ", " + credentials);
			} else if (format === "FML") {
				name = firstName + (middleName === "" ? "" : " " + middleName) + " " + lastName + (suffix === "" ? "" : " " + suffix);
			} else if (format === "FMLB") {
				name = firstName + (middleName === "" ? "" : " " + middleName) + " " + lastName + (suffix === "" ? "" : " " + suffix) + " " + birthday;
			} else if (format === "FMLC") {
				name = firstName + (middleName === "" ? "" : " " + middleName) + " " + lastName + (suffix === "" ? "" : " " + suffix) + (credentials === "" ? "" : ", " + credentials);
			} else if (format === "LFMi") {
				name = lastName + ", " + firstName + (middleName === "" ? "" : " " + middleName.substring(0, 1) + ". ") + (suffix === "" ? "" : ", " + suffix);
			} else if (format === "FMiL") {
				name = firstName + (middleName === "" ? "" : " " + middleName.substring(0, 1) + ". ") + " " + lastName + (suffix === "" ? "" : " " + suffix);
			} else if (format === "FMiLC") {
				name = firstName + (middleName === "" ? "" : " " + middleName.substring(0, 1) + ". ") + " " + lastName + (suffix === "" ? "" : " " + suffix) + (credentials === "" ? "" : ", " + credentials);
			} else if (format === "LFMiC") {
				name = lastName + ", " + firstName + (middleName === "" ? "" : " " + middleName.substring(0, 1) + ". ") + (suffix === "" ? "" : " " + suffix) + (credentials === "" ? "" : ", " + credentials);
			}
		} else if (["LF", "LFC", "FL", "FLC", "FiL"].indexOf(format) > -1) {
			if (format === "LF") {
				name = lastName + ", " + firstName + (suffix === "" ? "" : " " + suffix);
			} else if (format === "LFC") {
				name = lastName + ", " + firstName + (suffix === "" ? "" : " " + suffix) + (credentials === "" ? "" : ", " + credentials);
			} else if (format === "FL") {
				name = firstName + " " + lastName + (suffix === "" ? "" : " " + suffix);
			} else if (format === "FLC") {
				name = firstName + " " + lastName + (suffix === "" ? "" : " " + suffix) + (credentials === "" ? "" : ", " + credentials);
			} else if (format === "FiL") {
				name = (firstName === "" ? "" : firstName.substring(0, 1) + ". ") + " " + lastName + (suffix === "" ? "" : " " + suffix);
			}
		}

		return name.trim() == "" ? "Unnamed" : name;
	}

	this.formatNameWithDOB = function (obj) {
		var name = "";
		if (obj === undefined || obj === null) {
			return name;
		}
		var firstName = obj.firstName ? obj.firstName : "";
		var lastName = obj.lastName ? obj.lastName : "";
		var middleName = obj.middleName ? obj.middleName : "";
		var suffix = obj.suffix ? obj.suffix.trim() : "";
		var birthday = obj.dob ? obj.dob : "";
		if (birthday !== null) {
			if (typeof birthday === 'object') {
				birthday = birthday.toISOString().split('T')[0];
			}
			var bDayArray = birthday.split('-');
			birthday = bDayArray[1] + '-' + bDayArray[2] + '-' + bDayArray[0];
		}
		name = firstName + (middleName === "" ? "" : " " + middleName) + " " + lastName + (suffix === "" ? "" : " " + suffix) + "   (" + birthday + ")";
		return name;
	};

	this.getUrlParameter = function (param, dummyPath) {
		var sPageURL = dummyPath || window.location.search.substring(1),
			sURLVariables = sPageURL.split(/[&||?]/),
			res;

		for (var i = 0; i < sURLVariables.length; i += 1) {
			var paramName = sURLVariables[i],
				sParameterName = (paramName || '').split('=');

			if (sParameterName[0] === param) {
				res = sParameterName[1];
			}
		}

		return res;
	};

	this.ucFirst = function (string) {
		return string === undefined ? "" : string.charAt(0).toUpperCase() + string.slice(1);
	};

	this.fixCase = function (value) {
		value = value.split("_").join(" ");
		value = this.ucwords(value);
		return value;
	};

	this.ucwords = function (string) {
		return string === undefined ? "" : string.replace(/^(.)|\s+(.)/g, function ($1) {
			return $1.toUpperCase();
		});
	};

	this.getHeightDisplay = function (hw) {
		if (hw.heightIn === undefined) {
			if (hw.heightFt === undefined)
				return "";
			else
				return hw.heightFt + "'";
		} else
			return hw.heightFt + "' " + hw.heightIn + "\"";
	};

	this.getHeightInInches = function (hw) {
		if (hw.heightFt === undefined) return hw.heightIn ? hw.heightIn : 0;
		return (hw.heightFt * 12) + (hw.heightIn ? hw.heightIn : 0);
	};

	this.getParentClassification = function (classification) {
		var parent_type = "";
		if (classification === "sub_type")
			parent_type = "main_type";
		else if (classification === "category")
			parent_type = "sub_type";
		else if (classification === "sub_category")
			parent_type = "category";
		else if (classification === "product")
			parent_type = "sub_category";
		return parent_type;
	};

	this.formatAddress = function (obj) {
		var result = "";
		if (obj === undefined) return result;
		if (obj.streetAddress !== undefined)
			result = result + obj.streetAddress;
		if (obj.city !== undefined)
			result = result !== "" ? result + ", " + obj.city : obj.city;
		if (obj.state !== undefined)
			result = result !== "" ? result + ", " + obj.state : obj.state;
		if (obj.zipcode !== undefined)
			result = result !== "" ? result + " " + obj.zipcode : obj.zipcode;
		if (obj.country !== undefined)
			result = result !== "" ? result + ", " + obj.country : obj.country;
		return result; //obj.streetAddress + ", " + obj.city + ", " + obj.state + " " +  obj.zipcode;
	};

	this.formatPhone = function (value) {
		if (value && value.length === 10) {
			return '(' + value.slice(0, 3) + ') ' + value.slice(3, 6) + '-' + value.slice(6);
		} else if (value > 10) {
			return '(' + value.slice(0, 3) + ') ' + value.slice(3, 6) + '-' + value.slice(6, 10) + ' Ext: ' + value.slice(10);
		} else
			return value;
	};

	this.formatList = function (array) {
		if (array === undefined || array === null || array.length === 0) return "";
		return array.join(", ");
	};

	this.dataURItoBlob = function (dataURI) {
		// convert base64/URLEncoded data component to raw binary data held in a string
		var byteString;
		if (dataURI.split(',')[0].indexOf('base64') >= 0)
			byteString = atob(dataURI.split(',')[1]);
		else
			byteString = unescape(dataURI.split(',')[1]);

		// separate out the mime component
		var mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];

		// write the bytes of the string to a typed array
		var ia = new Uint8Array(byteString.length);
		for (var i = 0; i < byteString.length; i++) {
			ia[i] = byteString.charCodeAt(i);
		}
		return new Blob([ia], {type: mimeString});
	};

	this.openNewWindow = function (html) {
		var newTab = window.open("#", "_blank");
		newTab.document.write(html);
	};


	this.stripUnsupportedHTMLTagsSummerNote = function (input) {
		var inputRegEx = new RegExp('<input.*?>', 'gi');
		var output = input.replace(inputRegEx, "[]");
		if (output === input) {
			return null;
		}
		return output;

		// Save this for other common cases we might want to add...
		// // 1. remove line breaks / Mso classes
		// var stringStripper = /(\n|\r| class=(")?Mso[a-zA-Z]+(")?)/g;
		// var output = input.replace(stringStripper, ' ');
		// // 2. strip Word generated HTML comments
		// var commentSripper = new RegExp('<!--(.*?)-->','g');
		// var output = output.replace(commentSripper, '');
		// var tagStripper = new RegExp('<(/)*(meta|link|span|\\?xml:|st1:|o:|font)(.*?)>','gi');
		// // 3. remove tags leave content if any
		// output = output.replace(tagStripper, '');
		// // 4. Remove everything in between and including tags '<style(.)style(.)>'
		// var badTags = ['style', 'script','applet','embed','noframes','noscript'];
		//
		// for (var i=0; i< badTags.length; i++) {
		// 	tagStripper = new RegExp('<'+badTags[i]+'.*?'+badTags[i]+'(.*?)>', 'gi');
		// 	output = output.replace(tagStripper, '');
		// }
		// // 5. remove attributes ' style="..."'
		// var badAttributes = ['style', 'start'];
		// for (var i=0; i< badAttributes.length; i++) {
		// 	var attributeStripper = new RegExp(' ' + badAttributes[i] + '="(.*?)"','gi');
		// 	output = output.replace(attributeStripper, '');
		// }
	};

	this.cleanHTML = function (value) {
		return value ? value.replace(/<[^>]+>/gm, '') : ''
	};

	this.refSort = function (targetData, refData) {
		if (!refData) return targetData;
		// Create an array of indices [0, 1, 2, ...N].
		var indices = refData.split(",");

		var result = [];
		angular.forEach(indices, function (idx, index) {
			angular.forEach(targetData, function (entry, index2) {
				if (entry.id === +idx) {
					result.push(entry)
				}
			});
		});
		return result;
	};

	this.range = function (start, count) {
		return Array.apply(0, Array(count))
			.map(function (element, index) {
				return index + start;
			});
	};

	this.convertSnakeCaseToTitleCase = function (str) {
		if (!str) return null;
		var arr = str.split("_");
		var tempArr = [];
		for (var i = 0; i < arr.length; i++) {
			var temp = arr[i].charAt(0).toUpperCase() + arr[i].slice(1);
			tempArr.push(temp);
		}
		var results = tempArr.join(" ");
		return results;
	};

	this.isNullOrUndefined = function (input) {
		return input === null || input === undefined;
	};

	this.formatPhysicianNameWithNPIAndAddress = function (physician) {
		var result = "";
		if (physician) {
			var physicianName = _this.formatName(physician, 'LFMC');
			var npi = physician.npi ? physician.npi : "n/a";
			var formattedAddress = _this.formatAddress(physician);
			var address = formattedAddress ? formattedAddress : "n/a";
			result = physicianName + " (NPI: " + npi + ", Address: " + address + ")";
		}
		return result;
	};

	this.capitalizeFirstLetter = function (str) {
		if (str === undefined) return;
		var result = str.charAt(0).toUpperCase() + str.slice(1);
		return result;
	}

	this.toCapitalizedWords = function (name) {
		if (name === null || name === undefined) return;
		var words = name.match(/HCPCS|[A-Za-z0-9][0-9a-z0-9]*/g) || [];
		angular.forEach(words, function (e, i) {
			words[i] = _this.ucwords(words[i]);
		});
		return words.join(" ");
	}

	this.prettifyXML = function (xml) {
		var formatted = '', indent = '';
		var tab = '  ';
		xml.split(/>\s*</).forEach(function (node) {
			if (node.match(/^\/\w/)) indent = indent.substring(tab.length); // decrease indent by one 'tab'
			formatted += indent + '<' + node + '>\r\n';
			if (node.match(/^<?\w[^>]*[^\/]$/)) indent += tab;              // increase indent
		});
		return formatted.substring(1, formatted.length - 3);
	}

	this.getSelectUploadLabel = function (prescription) {
		var result = '';
		if (prescription.id !== 0) {
			if (prescription.zeroBalance) {
				result = 'Zero Balance - ';
			}
			result = result + '(#' + prescription.id + ') ' + prescription.deviceType.name;
			if (prescription.claimIds) {
				result = result + ' | Claim # ' + prescription.claimIds;
			}
			angular.forEach(prescription.$subPrescriptions, function (entry, index) {
				result = result + ' / ';
				if (entry.zeroBalance) {
					result = result + 'Zero Balance - ';
				}
				result = result + '(#' + entry.id + ') '; // + entry.deviceType.name;
				if (entry.claimIds) {
					result = result + ' | Claim # ' + entry.claimIds;
				}
			});
		} else {
			result = prescription.deviceType.name;
		}
		return result;
	}

	this.getSelectEobLabel = function (prescription) {
		var result = ''
		if (prescription.id !== 0) {
			result = ' (#' + prescription.id + ') ' + prescription.deviceType.name;
			angular.forEach(prescription.$subPrescriptions, function (entry, index) {
				result = result + ' (#' + entry.id + ')';
			});
		} else {
			result = prescription.deviceType.name;
		}
		return result;
	}

	this.openNymbl2Link = function (path) {
		_this.openV2Link(path, '');
	}

	this.replaceWithNewLine = function (string) {
		return string.replace(/~/g, '~<br>').trim();
	}

	this.trustPdfUrl = function (base64) {
		return $sce.trustAsResourceUrl('data:application/pdf;base64,' + base64);
	};

	this.formatModifiers = function (plc) {
		var mods = '';
		if (!!plc) {
			if (!!plc.modifier1) {
				mods = mods + plc.modifier1;
			}
			if (!!plc.modifier2) {
				mods = mods + '^' + plc.modifier2;
			}
			if (!!plc.modifier3) {
				mods = mods + '^' + plc.modifier3;
			}
			if (!!plc.modifier4) {
				mods = mods + '^' + plc.modifier4;
			}
			if (!!mods) {
				return plc.lCode.name + ' - ' + plc.quantity + ' (' + mods + ')';
			} else {
				return plc.lCode.name + ' - ' + plc.quantity;
			}
		}
	};

	this.stripFormatting = function (str) {
		if (str == null) return "";
		return str
			// .replace(/[\n\r]+/g, '')
			// .replace(/\s{2,10}/g, " ")
			// .replace(/<p.*?>/ig,"")
			// .replace("</p>", "")
			.replace(/<div.*?>/ig, "")
			.replace("</div>", "")
			.replace(/<input.*?>/ig, "")
			.replace(/<table.*?>(.|\n|\r)*?<\/table>/ig, "");
	}

	/**
	 * Opens a new window to the Nymbl V2 URL with the given path and page with auth token providing current branch.
	 * @param path - The path to open
	 * @param page - The page to open
	 */
	this.openV2Link = (path, page) => {
		_this.openV2Link(path, page, null);
	}
	/**
	 * Opens a new window to the Nymbl V2 URL with the given path, page and query params including auth token providing current branch.
	 * @param path - The path to open
	 * @param page - The page to open
	 * @param queryParams - The query params to open
	 */
	this.openV2Link = (path, page = '', queryParams = null) => {
		var url = $('#nymblV2Url').attr("value");
		// Check if there are backslashes at the end of the page and
		// the beginning of the path if not add one.
		if(path.charAt(path.length-1) !== '/' && page.charAt(0) !== '/')
			page = '/' + page

		$window.open(url + path + page + "?token=" + $cookies.get("X-Auth-Token") + (queryParams ? '&' + queryParams : ""), '_blank');
	}
}
