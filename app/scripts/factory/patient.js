app.factory('PatientFactory', PatientFactory);
PatientFactory.$inject = ['$resource'];

function PatientFactory($resource) {
	return $resource('api/patient/:id', {id: '@_id'}, {
		searchBy: {
			url: 'api/patient/searchBy',
			method: 'GET',
			isArray: true
		},
		search: {
			url: 'api/patient/search',
			method: 'GET',
			isArray: true
		},
    validateUniquePatient: {
      url: 'api/patient/validate-unique-patient',
      method: 'GET',
      isArray: true
    },
    textMedicalHistoryForm: {
      url: 'api/patient/text-medical-history',
	    method: 'GET',
	    transformResponse: function (data) {
		    return {data: data.toString()}
	    }
    },
		sort: {
			url: 'api/patient/sort?page=:pageNumber&size=:pageSize&sort=:columnName,:sortDirection',
			method: 'GET',
      params: {
        pageNumber: '@_pageNumber',
        pageSize: '@_pageSize',
        columnName: '@_columnName',
        sortDirection: '@_sortDirection'
      }
    },
    saveIntakeFromPatient: {
      url: 'api/patient/save-intake',
      method: 'POST'
    },
    savePatient: {
      url: 'api/patient/save-patient',
      method: 'POST'
    },
    mergePatient: {
        url: 'api/patient/merge/:fromPatientId/:toPatientId',
        method: 'POST',
        params: {
            fromPatientId: '@fromPatientId',
            toPatientId: '@toPatientId'
        }
    }
  });
}
