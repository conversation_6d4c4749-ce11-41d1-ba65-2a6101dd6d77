app.factory('ReportFactory', ReportFactory);
ReportFactory.$inject = ['$resource'];

function ReportFactory($resource) {
	return $resource('api/report/ad-hoc', {}, {
		arAgingReport: {
			url: 'api/report/ad-hoc/ar-aging-report/',
			method: 'GET',
			params: {
				branchId: '@_branchId'
			}
		},
		arAgingChart: {
			url: 'api/report/ad-hoc/ar-aging/chart',
			method: 'GET',
			params: {
				startDate: '@_startDate',
				branchId: '@_branchId',
				dateOption: '@_dateOption'
			}
		},
		cashSummary: {
			url: 'api/report/ad-hoc/cash-summary/:startDate/:endDate',
			method: 'GET',
			params: {
				startDate: '@_startDate',
				endDate: '@_endDate',
				branchId: '@_branchId'
			}
		},
		paymentsByPayer: {
			url: 'api/report/ad-hoc/payments-by-payer/:startDate/:endDate',
			method: 'GET',
			params: {
				startDate: '@_startDate',
				endDate: '@_endDate',
				branchId: '@_branchId',
				dateOption: '@_dateOption'
			},
			isArray: true
		},
		billingSummary: {
			url: 'api/report/ad-hoc/billing-summary/:startDate/:endDate',
			method: 'GET',
			params: {
				startDate: '@_startDate',
				endDate: '@_endDate',
				branchId: '@_branchId'
			}
		},
		salesSummary: {
			url: 'api/report/ad-hoc/sales-summary/:startDate/:endDate/:dateOption',
			method: 'GET',
			params: {
				startDate: '@_startDate',
				endDate: '@_endDate',
				dateOption: '@_dateOption',
				branchId: '@_branchId'
			}
		}, salesDetail: {
			url: 'api/report/ad-hoc/sales-detail/:startDate/:endDate',
			method: 'GET',
			params: {
				startDate: '@_startDate',
				endDate: '@_endDate',
				branchId: '@_branchId'
			}
		},
		billingsByDateOfService: {
			url: 'api/report/ad-hoc/billings-by-date-of-service/list',
			method: 'GET',
			params: {
        branchId: '@branchId',
        startDate: '@startDate',
        endDate: '@endDate'
      }
		},
		claimsSummary: {
			url: 'api/report/ad-hoc/claims-summary/list',
			method: 'GET',
			params: {
				startDate: '@startDate',
				endDate: '@endDate',
				branchId: '@branchId'
			}
		},
		claimsSummaryPivotTableExport: {
			url: 'api/report/ad-hoc/claims-summary/pivotTableExport',
			method: 'GET',
			params: {
				startDate: '@startDate',
				endDate: '@endDate',
				branchId: '@branchId'
			},
			transformResponse: function (data) {
				return {data: data.toString()};
			}
		},
		monthlyCollectedPercentage: {
			url: 'api/report/ad-hoc/monthly-collected-percentage/chart',
			method: 'GET'
		},
		generalBatchSummaryList: {
			url: 'api/report/ad-hoc/general-batch-summary/list',
			method: 'GET',
			isArray: true
		},
		insuranceCompaniesBilled: {
			url: 'api/report/ad-hoc/insurance-companies-billed/list',
			method: 'GET',
			isArray: true
		},
		outstandingInsuranceBalances: {
			url: 'api/report/ad-hoc/outstanding-insurance-balances/list',
			method: 'GET'
		},
		monthlyBillingsCollected: {
			url: 'api/report/ad-hoc/monthly-billings-collected/chart',
			method: 'GET'
		},
		outstandingPatientResponsibilityBalances: {
			url: 'api/report/ad-hoc/outstanding-patient-responsibility-balance/list',
			method: 'GET',
			isArray: true
		},
		patientRefunds: {
			url: 'api/report/ad-hoc/patients-owed-refund/list',
			method: 'GET',
			isArray: true
		},
		salesTaxReport: {
			url: 'api/report/ad-hoc/sales-tax-report/:startDate/:endDate',
			method: 'GET',
			params: {
				startDate: '@_startDate',
				endDate: '@_endDate'
			}
		},
		totalBilled: {
			url: 'api/report/ad-hoc/total-billed/list',
			method: 'GET',
			params: {
				startDate: '@_startDate',
				endDate: '@_endDate'
			}
		},
		userBatchSummaryList: {
			url: 'api/report/ad-hoc/user-batch-summary/list',
			method: 'GET',
			isArray: true
		},
		unclaimedPrescriptions: {
			url: 'api/report/ad-hoc/un-claimed-prescriptions/list',
			method: 'GET',
			isArray: true
		},
		wipBillingsCollected: {
			url: 'api/report/ad-hoc/wip-billings-collected/list',
			method: 'GET',
			params: {
				branchId: '@_branchId'
			}
		},
		yearlyBillingsCollected: {
			url: 'api/report/ad-hoc/yearly-billings-collected/chart',
			method: 'GET'
		},
		dailyClose: {
			url: 'api/report/ad-hoc/daily-close/list',
			method: 'GET',
			params: {
				payeeType: '@_payeeType',
				startDate: '@_startDate',
				endDate: '@_endDate',
				branchId: '@_branchId'
			}
		},
		salesDetailExport: {
			url: 'api/report/ad-hoc/sales-detail-export',
			method: 'GET',
			transformResponse: function (data) {
				return {data: data.toString()}
			}
		},
		appointmentList: {
			url: 'api/report/ad-hoc/appointment/appointmentDateRange/list',
			method: 'GET',
			isArray: true
		},
		appointmentsByStatusWithNoReschedule: {
			url: 'api/report/ad-hoc/appointment/:status/list',
			method: 'GET',
			isArray: true,
			params: {
				status: '@_status',
			}
		},
    surveyResponses: {
      url: 'api/report/ad-hoc/survey-response/list',
      method: 'GET',
      isArray: true,
      params: {
        branchId: '@_branchId',
        startDate: '@_startDate',
        endDate: '@_endDate'
      }
    },
    surveyTextMessagesForAppointmentTypes: {
      url: 'api/report/ad-hoc/survey/for-appointment-types',
      method: 'GET',
      isArray: true,
      params: {
	      startDateTime: '@_startDateTime',
	      endDateTime: '@_endDateTime',
      }
    },
    sentTextMessageDetails: {
      url: 'api/report/ad-hoc/survey/sent-text-message-details',
      method: 'GET',
      isArray: true,
      params: {
        startDate: '@_startConfirmationRequestSentDate',
        endDate: '@_endConfirmationRequestSentDate',
      }
    },
    checkSmsComplianceUser: {
      url: 'api/report/ad-hoc/check-sms-compliance-user',
      method: 'GET',
      isArray: true
    },
    checkSmsCompliancePatient: {
      url: 'api/report/ad-hoc/check-sms-compliance-patient',
      method: 'GET',
      isArray: true
    },
    prescriptionsWithoutSurveyText: {
      url: 'api/report/ad-hoc/survey/no-text-list',
      method: 'GET',
      isArray: true,
      params: {
        branchId: '@_branchId'
      }
    },
    prescriptionsOnHold: {
      url: 'api/report/ad-hoc/prescription/on-hold',
      method: 'GET',
      isArray: true
    },
    billingsByPractitioner: {
      url: 'api/report/ad-hoc/monthly-billings-by-practitioner/list',
      method: 'GET',
      isArray: true
    },
    billingsByRepresentative: {
      url: 'api/report/ad-hoc/monthly-billings-by-representative/list',
      method: 'GET',
      isArray: true
    },
    practitionerBillingsByCategory: {
      url: 'api/report/ad-hoc/practitioner-billings-by-category/list',
      method: 'GET',
      isArray: true
    },
    practitionerCommissions: {
      url: 'api/report/ad-hoc/practitioner-commissions/list',
      method: 'GET',
      isArray: true,
      params: {
        branchId: '@_branchId',
        startDate: '@_startDate',
        endDate: '@_endDate',
        dateOption: '@_dateOption',
        deviceType: '@_deviceType'
      }
    },
    representativeCommissions: {
      url: 'api/report/ad-hoc/representative-commissions/list',
      method: 'GET',
      isArray: true,
      params: {
        branchId: '@_branchId',
        startDate: '@_startDate',
        endDate: '@_endDate',
        dateOption: '@_dateOption',
        deviceType: '@_deviceType'
      }
    },
    lCodeBilledCharges: {
      url: 'api/report/ad-hoc/lcode-billings/list',
      method: 'GET',
      isArray: true,
      params: {
        numMonths: '@_numMonths'
      }
    },
    monthlyPhysicianVisitCount: {
      url: 'api/report/ad-hoc/appointment/physician-monthly-count/list',
			method: 'GET',
			isArray: true
		},
		unapprovedNotes: {
			url: 'api/report/ad-hoc/unapproved-notes/list',
			method: 'GET',
			isArray: true,
			params: {
        userId: '@_userId',
        startDate: '@startDate',
        endDate: '@endDate'
      }
		},
		billingsByReferringPhysician: {
			url: 'api/report/ad-hoc/billings-referring-physician/list',
			method: 'GET',
			isArray: true
		},
		newPrescriptionsByMonth: {
			url: 'api/report/ad-hoc/new-prescriptions-by-month/chart',
			method: 'GET'
		},
		claimsWithAdditionalComment: {
			url: 'api/report/ad-hoc/claim/additional-comment/list',
			method: 'GET',
			isArray: true
		},
		deliveredPrescriptionList: {
			url: 'api/report/ad-hoc/delivered-prescriptions/list',
			method: 'GET',
			isArray: true
		},
		diagnosisCodeList: {
			url: 'api/report/ad-hoc/diagnosis-code/list',
			method: 'GET',
			isArray: true
		},
    lCodeAlertsList: {
      url: 'api/report/ad-hoc/l-code-alerts/list',
      method: 'GET',
      isArray: true
    },
    lowInventoryStockList: {
      url: 'api/report/ad-hoc/low-inventory-stock/list',
      method: 'GET',
      isArray: true
    },
    noteList: {
      url: 'api/report/ad-hoc/note/list',
      method: 'GET',
      isArray: true,
      params: {
        userId: '@_userId',
        startDate: '@_startDate',
        endDate: '@_endDate',
        branchId: '@_branchId',
        noteType: '@_noteType'
      }
    },
    patientsByCategoryList: {
      url: 'api/report/ad-hoc/patient/device-category/list',
      method: 'GET',
      isArray: true
    },
    patientBirthdayList: {
      url: 'api/report/ad-hoc/patient/birthday/list',
      method: 'GET',
      isArray: true
    },
    patientsWithCriticalMessage: {
			url: 'api/report/ad-hoc/patient/critical-messages/list',
			method: 'GET',
			isArray: true
		},
		expiringPreAuths: {
			url: 'api/report/ad-hoc/insurance-verification/expiring-pre-auth',
			method: 'GET',
			isArray: true
		},
		patientByReferralSource: {
			url: 'api/report/ad-hoc/patient/referral-source/list',
			method: 'GET',
			isArray: true
		},
		clericalProductivity: {
      url: 'api/report/ad-hoc/user/clerical-productivity/list',
      method: 'GET',
      isArray: true,
      params: {
        branchId: '@_branchId',
        startDate: '@_startDate',
        endDate: '@_endDate'
      }
    },
		dashBoardsByQuickSight: {
			url: 'api/report/ad-hoc/quick-sight/dashboards',
			method: 'GET',
			isArray: true
		},
		tableListings: {
			url: 'api/report/ad-hoc/table/listings',
			method: 'GET',
			isArray: true
		},
		tableMetadata: {
			url: 'api/report/ad-hoc/table/metadata/:className',
			method: 'GET',
			isArray: true,
			params: {
				className: '@_className'
			}
		},
		uncollected: {
			url: 'api/report/ad-hoc/uncollected/list',
			method: 'GET',
			isArray: true,
			params: {
				startDate: '@_startDate',
				endDate: '@_endDate',
				branchId: '@_branchId'
			}
		},
		averageDaysToDeliverByDeviceType: {
			url: 'api/report/ad-hoc/device-type-prescription',
			method: 'GET',
			isArray: true,
		},
		fabricationsPastDueDate: {
			url: 'api/report/ad-hoc/fabrications-past-due-date',
			method: 'GET',
			isArray: true
		},
		overdueRental: {
			url: "api/prescription-l-code/overdue-rental",
			method: 'GET',
			isArray: true,
			params: {
				branchId: '@_branchId'
			}
		},
		dailySalesOutstandingWidget: {
			url: 'api/report/ad-hoc/widget/daily-sales-outstanding',
			method: 'GET',
			isArray: true,
			params: {
				startDate: '@_startDate',
				branchIds: '@_branchIds'
			}
		}
	});
}
