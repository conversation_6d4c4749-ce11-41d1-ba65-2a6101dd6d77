package com.nymbl.ai.notes.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentation;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentationContainer;
import com.nymbl.ai.notes.data.clinicaldoc.Section;
import com.nymbl.ai.notes.data.conversation.Conversation;
import com.nymbl.ai.notes.data.conversation.ConversationContainer;
import com.nymbl.ai.notes.data.conversation.TranscriptSegment;
import com.nymbl.ai.notes.dto.*;
import com.nymbl.ai.notes.exception.TranscriptionDetailException;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.repository.TranscriptionDetailRepository;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.Appointment;
import com.nymbl.tenant.model.Note;
import com.nymbl.tenant.model.Prescription;
import com.nymbl.tenant.service.AppointmentService;
import com.nymbl.tenant.service.NoteService;
import com.nymbl.tenant.service.NotificationService;
import com.nymbl.tenant.service.PrescriptionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class TranscriptionDetailServiceTest {

    @Mock
    private TranscriptionDetailRepository transcriptionDetailRepository;

    @Mock
    private TranscriptionUtil transcriptionUtil;

    @Mock
    private NotificationService notificationService;

    @Mock
    private UserService userService;

    @Mock
    private NoteService noteService;

    @Mock
    private AwsUtil awsUtil;

    @Mock
    private AWSS3Service awsS3Service;

    @Mock
    private PrescriptionService prescriptionService;

    @Mock
    private AppointmentService appointmentService;

    private TranscriptionDetailService transcriptionDetailService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        transcriptionDetailService = new TranscriptionDetailService(
                transcriptionDetailRepository,
                transcriptionUtil,
                notificationService,
                prescriptionService,
                appointmentService,
                noteService,
                awsUtil,
                userService
        );
    }

    @Test
    void testSearch() {
        List<Long> practitionerId = Arrays.asList(1L, 2L);
        Long patientId = 1L;
        Long appointmentId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        String status = "completed";
        Pageable pageable = mock(Pageable.class);
        Page<TranscriptionDetail> page = mock(Page.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);

        when(transcriptionDetailRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(page);
        when(page.getContent()).thenReturn(Collections.singletonList(transcriptionDetail));
        when(transcriptionUtil.convertToDto(transcriptionDetail)).thenReturn(mock(TranscriptionDetailsDto.class));

        Optional<List<TranscriptionDetailsDto>> result = transcriptionDetailService.search(practitionerId, patientId, appointmentId, startDate, endDate, status, false, pageable);

        assertTrue(result.isPresent());
        verify(transcriptionDetailRepository, times(1)).findAll(any(Specification.class), eq(pageable));
        verify(transcriptionUtil, times(1)).convertToDto(transcriptionDetail);
    }

    @Test
    void testGetDetailsById() throws TranscriptionDetailException {
        Long detailsId = 1L;
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);

        when(transcriptionDetailRepository.findById(detailsId)).thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionUtil.convertToDto(transcriptionDetail)).thenReturn(mock(TranscriptionDetailsDto.class));

        Optional<TranscriptionDetailsDto> result = transcriptionDetailService.getDetailsById(detailsId);

        assertTrue(result.isPresent());
        verify(transcriptionDetailRepository, times(1)).findById(detailsId);
        verify(transcriptionUtil, times(1)).convertToDto(transcriptionDetail);
    }

    @Test
    void testWriteCompletedNotesFromBucket() throws TranscriptionDetailException, JsonProcessingException, IOException {
        // Setup test data
        AiNoteWebhookDto aiNoteWebhookDto = mock(AiNoteWebhookDto.class);
        String jobName = "123456";
        Long detailId = 123456L;
        String bucketName = "test-bucket";
        String eventTime = "2034-02-25T00:00:00.000Z";
        Long createdById = 1L;

        // Create valid JSON for summary
        String summaryJson = "{\"clinicalDocumentation\":{\"sections\":[]}}";
        byte[] summaryBytes = summaryJson.getBytes(StandardCharsets.UTF_8);

        // Mock TranscriptionDetail
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        when(transcriptionDetail.getId()).thenReturn(detailId);
        when(transcriptionDetail.getCreatedById()).thenReturn(createdById);
        when(transcriptionDetail.getOutputBucketName()).thenReturn(bucketName);
        when(transcriptionDetail.getJobName()).thenReturn(jobName);

        // Mock ClinicalDocumentationContainer
        ClinicalDocumentationContainer container = new ClinicalDocumentationContainer();
        ClinicalDocumentation clinicalDocumentation = new ClinicalDocumentation();
        List<Section> sections = new ArrayList<>();
        clinicalDocumentation.setSections(sections);
        container.setClinicalDocumentation(clinicalDocumentation);

        // Mock AWS S3 service
        when(awsUtil.getUsEast1Client()).thenReturn(awsS3Service);
        when(awsS3Service.getFile(eq(bucketName), eq(jobName + "/summary.json"))).thenReturn(Optional.of(summaryBytes));

        // Mock transcription util
        when(transcriptionUtil.getTranscriptionDetailsId(jobName)).thenReturn(detailId);
        when(transcriptionUtil.fromJson(anyString(), eq(ClinicalDocumentationContainer.class))).thenReturn(container);
        when(transcriptionUtil.filterAndOrderSectionsInRawJson(anyString())).thenReturn(summaryJson);

        // Mock webhook DTO
        when(aiNoteWebhookDto.getBucketName()).thenReturn(bucketName);
        when(aiNoteWebhookDto.getEventTime()).thenReturn(eventTime);
        when(aiNoteWebhookDto.getJobName()).thenReturn(jobName);

        // Mock repository
        when(transcriptionDetailRepository.findById(detailId)).thenReturn(Optional.of(transcriptionDetail));

        // Mock note service response
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        savedNote.setId(123L);
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        // Execute test
        boolean result = transcriptionDetailService.writeCompletedNotesFromBucket(aiNoteWebhookDto);

        // Verify results
        assertTrue(result);

        // Verify transcription detail updates
        verify(transcriptionDetail).setStatus(AINOTE.READY);
        verify(transcriptionDetail).setEndTime(any(Timestamp.class));
        verify(transcriptionDetail).setUpdatedAt(any(Timestamp.class));
        verify(transcriptionDetail).setUpdatedById(createdById);
        verify(transcriptionDetail).setNoteId(123L);
        verify(transcriptionDetail).setReviewed(false);
        verify(transcriptionDetail).setSubject("AI Note: Save As Draft Generated Subject Line");

        // Verify AWS interactions
        verify(awsS3Service).getFile(bucketName, jobName + "/summary.json");

        // Verify repository save
        verify(transcriptionDetailRepository).save(transcriptionDetail);

        // Verify notification creation
        verify(notificationService).createAINoteNotification(transcriptionDetail);
    }

    @Test
    void testViewGeneratedNotes() throws JsonProcessingException, TranscriptionDetailException {
        Long transcriptionDetailsId = 1L;

        // Mock conversation data
        ConversationContainer conversation = new ConversationContainer();
        Conversation conversation1 = new Conversation();
        List<TranscriptSegment> segments = new ArrayList<>();
        conversation1.setTranscriptSegments(segments);
        conversation.setConversation(conversation1);

        // Mock clinical documentation data
        ClinicalDocumentationContainer clinicalDocumentation = new ClinicalDocumentationContainer();
        ClinicalDocumentation documentation = new ClinicalDocumentation();
        List<Section> sections = new ArrayList<>();
        documentation.setSections(sections);
        clinicalDocumentation.setClinicalDocumentation(documentation);

        // Mock transcription detail
        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setStatus(AINOTE.READY);
        transcriptionDetail.setArchived(false);
        transcriptionDetail.setSubject("Test Subject");
        transcriptionDetail.setOutputBucketName("test-bucket");
        transcriptionDetail.setJobName("test-job");

        // Setup mocks
        when(transcriptionDetailRepository.findById(transcriptionDetailsId))
            .thenReturn(Optional.of(transcriptionDetail));
        when(awsUtil.getUsEast1Client()).thenReturn(awsS3Service);
        when(awsS3Service.getFile(anyString(), anyString())).thenReturn(Optional.of("test".getBytes(StandardCharsets.UTF_8)));
        when(transcriptionUtil.fromJson(anyString(), eq(ConversationContainer.class))).thenReturn(conversation);
        when(transcriptionUtil.fromJson(anyString(), eq(ClinicalDocumentationContainer.class)))
            .thenReturn(clinicalDocumentation);

        // Execute test
        Optional<TranscriptionAppointmentNotesDto> result = transcriptionDetailService.viewGeneratedNotes(transcriptionDetailsId);

        // Verify results
        assertTrue(result.isPresent());
        TranscriptionAppointmentNotesDto dto = result.get();
        assertEquals(transcriptionDetailsId, dto.getTranscriptionDetailsId());
        assertEquals(transcriptionDetail.isReviewed(), dto.isReviewed());
        assertEquals(transcriptionDetail.getSubject(), dto.getSubject());
        assertEquals(AINOTE.READY, dto.getStatus());
        assertFalse(dto.isArchived());
        assertNotNull(dto.getConversation());

        // Verify interactions
        verify(transcriptionDetailRepository, times(1)).findById(transcriptionDetailsId);
        verify(awsUtil, times(2)).getUsEast1Client();
        verify(awsS3Service, times(2)).getFile(anyString(), anyString());
        verify(transcriptionUtil, times(1)).fromJson(anyString(), eq(ConversationContainer.class));
        verify(transcriptionUtil, times(1)).fromJson(anyString(), eq(ClinicalDocumentationContainer.class));
    }

    @Test
    void testReviewNotes() throws TranscriptionDetailException {
        // Setup request
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);
        request.setAction(AINOTE.DRAFT);
        request.setSubject("Test Subject");
        request.setReviewedNoteString("Test note content");
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);

        // Mock transcription detail
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        when(transcriptionDetail.isArchived()).thenReturn(false);

        // Mock user
        User user = mock(User.class);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(false);

        // Mock note service response
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        savedNote.setId(123L);
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);

        // Setup mocks
        when(transcriptionDetailRepository.findById(request.getTranscriptionDetailId()))
            .thenReturn(Optional.of(transcriptionDetail));
        when(userService.getCurrentUser()).thenReturn(user);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        // Execute test
        String result = transcriptionDetailService.reviewNotes(request);

        // Verify results
        assertEquals("success", result);
        
        // Verify interactions
        verify(transcriptionDetailRepository).findById(request.getTranscriptionDetailId());
        verify(transcriptionDetail).setSubject(request.getSubject());
        verify(transcriptionDetailRepository).save(transcriptionDetail);
        verify(transcriptionDetail).setStatus(request.getAction());
        verify(noteService).saveNote(any(Note.class), eq("draft"));
    }

    @Test
    void testGetClinicalNotesString_printsInOrderAndSkipsInvalidSections() {
        ClinicalDocumentation clinicalDocumentation = mock(ClinicalDocumentation.class);

        Section chiefComplaint = mock(Section.class);
        Section historyOfPresentIllness = mock(Section.class);
        Section pastMedicalHistory = mock(Section.class);
        Section reviewOfSystems = mock(Section.class);
        Section physicalExam = mock(Section.class);
        Section plan = mock(Section.class);
        Section irrelevantSection = mock(Section.class);

        when(chiefComplaint.getSectionName()).thenReturn("CHIEF_COMPLAINT");
        when(historyOfPresentIllness.getSectionName()).thenReturn("HISTORY_OF_PRESENT_ILLNESS");
        when(pastMedicalHistory.getSectionName()).thenReturn("PAST_MEDICAL_HISTORY");
        when(reviewOfSystems.getSectionName()).thenReturn("REVIEW_OF_SYSTEMS");
        when(physicalExam.getSectionName()).thenReturn("PHYSICAL_EXAMINATION");
        when(plan.getSectionName()).thenReturn("PLAN");
        when(irrelevantSection.getSectionName()).thenReturn("PAST_FAMILY_HISTORY");

        when(chiefComplaint.printSectionDetails()).thenReturn("Chief Complaint");
        when(historyOfPresentIllness.printSectionDetails()).thenReturn("History of Present Illness");
        when(pastMedicalHistory.printSectionDetails()).thenReturn("Past Medical History");
        when(reviewOfSystems.printSectionDetails()).thenReturn("Review of Systems");
        when(physicalExam.printSectionDetails()).thenReturn("Physical Examination");
        when(plan.printSectionDetails()).thenReturn("Plan");
        when(irrelevantSection.printSectionDetails()).thenReturn("Should not appear");

        List<Section> allSections = Arrays.asList(
                irrelevantSection,
                reviewOfSystems,
                chiefComplaint,
                plan,
                historyOfPresentIllness,
                physicalExam,
                pastMedicalHistory
        );

        when(clinicalDocumentation.getSections()).thenReturn(allSections);

        String result = transcriptionDetailService.getClinicalNotesString(clinicalDocumentation);

        assertTrue(result.contains("Chief Complaint"));
        assertTrue(result.contains("History of Present Illness"));
        assertTrue(result.contains("Past Medical History"));
        assertTrue(result.contains("Review of Systems"));
        assertTrue(result.contains("Physical Examination"));
        assertTrue(result.contains("Plan"));
        assertFalse(result.contains("Should not appear"));

        assertTrue(result.indexOf("Chief Complaint") < result.indexOf("History of Present Illness"));
        assertTrue(result.indexOf("History of Present Illness") < result.indexOf("Past Medical History"));
        assertTrue(result.indexOf("Past Medical History") < result.indexOf("Review of Systems"));
        assertTrue(result.indexOf("Review of Systems") < result.indexOf("Physical Examination"));
        assertTrue(result.indexOf("Physical Examination") < result.indexOf("Plan"));

        assertTrue(result.contains("<br />"));
    }

    @Test
    void testGetTranscriptsString() {
        Conversation conversation = mock(Conversation.class);
        TranscriptSegment segment = mock(TranscriptSegment.class);
        List<TranscriptSegment> segments = Arrays.asList(segment, segment);

        when(conversation.getTranscriptSegments()).thenReturn(segments);
        when(segment.printTranscriptSegment()).thenReturn("segment");

        String result = transcriptionDetailService.getTranscriptsString(conversation);

        assertTrue(result.contains("segment"));
    }

    @Test
    void testArchiveTranscription() throws TranscriptionDetailException {
        Long prescriptionId = 1L;
        Long appointmentId = 1L;
        
        ArchiveRequest request = new ArchiveRequest();
        request.setTranscriptionDetailId(1L);
        request.setArchiveNote(true);

        User user = mock(User.class);
        when(user.getId()).thenReturn(1L);

        Prescription prescription = mock(Prescription.class);
        Appointment appointment = mock(Appointment.class);

        when(prescriptionService.findOne(prescriptionId)).thenReturn(prescription);
        when(appointmentService.findOne(appointmentId)).thenReturn(appointment);

        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        when(transcriptionDetail.getStatus()).thenReturn(AINOTE.READY);
        when(transcriptionDetail.getPrescriptionId()).thenReturn(prescriptionId);
        when(transcriptionDetail.getAppointmentId()).thenReturn(appointmentId);

        // Set up treating practitioner validation
        when(appointment.getUserFourId()).thenReturn(1L); // User is the treating practitioner
        when(appointment.getUserId()).thenReturn(null);
        when(prescription.getTreatingPractitionerId()).thenReturn(null);

        when(transcriptionDetailRepository.findById(1L)).thenReturn(Optional.of(transcriptionDetail));
        when(userService.getCurrentUser()).thenReturn(user);
        String result = transcriptionDetailService.archiveTranscription(request);

        assertEquals("success", result);
        verify(transcriptionDetailRepository, times(1)).findById(1L);
        verify(transcriptionDetailRepository, times(1)).save(transcriptionDetail);
    }

    @Test
    void testArchiveTranscription_NotFound() {
        ArchiveRequest request = new ArchiveRequest();
        request.setTranscriptionDetailId(1L);

        when(transcriptionDetailRepository.findById(1L)).thenReturn(Optional.empty());

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.archiveTranscription(request);
        });
    }

    @Test
    void testIsNotValidTreatingPractitioner() throws TranscriptionDetailException {
        Long prescriptionId = 1L;
        Long appointmentId = 1L;
        Long userId = 1L;

        Prescription prescription = mock(Prescription.class);
        Appointment appointment = mock(Appointment.class);

        when(prescriptionService.findOne(prescriptionId)).thenReturn(prescription);
        when(appointmentService.findOne(appointmentId)).thenReturn(appointment);
        
        // Test case 1: User is not the treating practitioner from appointment's userFourId
        when(appointment.getUserFourId()).thenReturn(2L);
        when(appointment.getUserId()).thenReturn(null);
        when(prescription.getTreatingPractitionerId()).thenReturn(null);

        boolean result = transcriptionDetailService.isNotValidTreatingPractitioner(prescriptionId, appointmentId, userId);
        assertTrue(result);

        // Test case 2: User is not the treating practitioner from appointment's userId
        when(appointment.getUserFourId()).thenReturn(null);
        when(appointment.getUserId()).thenReturn(2L);
        when(prescription.getTreatingPractitionerId()).thenReturn(null);

        result = transcriptionDetailService.isNotValidTreatingPractitioner(prescriptionId, appointmentId, userId);
        assertTrue(result);

        // Test case 3: User is not the treating practitioner from prescription
        when(appointment.getUserFourId()).thenReturn(null);
        when(appointment.getUserId()).thenReturn(null);
        when(prescription.getTreatingPractitionerId()).thenReturn(2L);

        result = transcriptionDetailService.isNotValidTreatingPractitioner(prescriptionId, appointmentId, userId);
        assertTrue(result);

        // Test case 4: User is the treating practitioner (should return false)
        when(appointment.getUserFourId()).thenReturn(1L);
        when(appointment.getUserId()).thenReturn(null);
        when(prescription.getTreatingPractitionerId()).thenReturn(null);

        result = transcriptionDetailService.isNotValidTreatingPractitioner(prescriptionId, appointmentId, userId);
        assertFalse(result);

        // Test case 5: No treating practitioner found (should throw exception)
        when(appointment.getUserFourId()).thenReturn(null);
        when(appointment.getUserId()).thenReturn(null);
        when(prescription.getTreatingPractitionerId()).thenReturn(null);

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.isNotValidTreatingPractitioner(prescriptionId, appointmentId, userId);
        });
    }

    @Test
    void testIsNotValidTreatingPractitioner_Error() {
        Long prescriptionId = 1L;
        Long appointmentId = 1L;
        Long userId = 1L;

        when(prescriptionService.findOne(prescriptionId)).thenReturn(null);
        when(appointmentService.findOne(appointmentId)).thenReturn(null);

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.isNotValidTreatingPractitioner(prescriptionId, appointmentId, userId);
        });
    }

    @Test
    void testIsInValidCareExtenderOrResident() throws TranscriptionDetailException {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setNoteId(1L);
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);

        Note note = mock(Note.class);
        Appointment appointment = mock(Appointment.class);
        Prescription prescription = mock(Prescription.class);
        User user = mock(User.class);

        when(noteService.findOne(1L)).thenReturn(note);
        when(note.getAppointment()).thenReturn(appointment);
        when(note.getPrescription()).thenReturn(prescription);
        when(appointment.getUserFourId()).thenReturn(2L);
        when(appointment.getUserId()).thenReturn(3L);
        when(prescription.getTreatingPractitionerId()).thenReturn(4L);
        when(prescription.getResidentId()).thenReturn(5L);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);

        boolean result = transcriptionDetailService.isInValidCareExtenderOrResident(request, 1L);

        assertTrue(result);
    }

    @Test
    void testIsInValidCareExtenderOrResident_Error() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        User user = mock(User.class);
        when(prescriptionService.findOne(any())).thenReturn(null);
        when(appointmentService.findOne(any())).thenReturn(null);

        when(userService.getCurrentUser()).thenReturn(user);
        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.isInValidCareExtenderOrResident(request, 1L);
        });
    }

    @Test
    void testSaveNote_NewNote() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);
        request.setPatientId(1L);
        request.setTreatingPractitionerId(1L);
        request.setReviewedNoteString("Test note");
        request.setSubject("Test subject");
        request.setAction(AINOTE.PUBLISHED);

        User user = mock(User.class);
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);

        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        Note result = transcriptionDetailService.saveNote(request, 1L, null, 1L);

        assertEquals(savedNote, result);
        verify(noteService, times(1)).saveNote(any(Note.class), eq("publish"));
    }

    @Test
    void testSaveNote_ExistingNote() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setReviewedNoteString("Updated note");
        request.setSubject("Updated subject");
        request.setAction(AINOTE.DRAFT);

        Note existingNote = new Note();
        User user = mock(User.class);
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);

        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        Note result = transcriptionDetailService.saveNote(request, 1L, existingNote, 1L);

        assertEquals(savedNote, result);
        verify(noteService, times(1)).saveNote(any(Note.class), eq("draft"));
    }

    @Test
    void testSearch_EmptyResults() {
        List<Long> practitionerId = Arrays.asList(1L, 2L);
        Long patientId = 1L;
        Long appointmentId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        String status = "completed";
        Pageable pageable = mock(Pageable.class);
        Page<TranscriptionDetail> page = mock(Page.class);

        when(transcriptionDetailRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(page);
        when(page.getContent()).thenReturn(Collections.emptyList());

        Optional<List<TranscriptionDetailsDto>> result = transcriptionDetailService.search(
            practitionerId, patientId, appointmentId, startDate, endDate, status, false, pageable);

        assertTrue(result.isPresent());
        assertTrue(result.get().isEmpty());
    }

    @Test
    void testGetDetailsById_NotFound() throws TranscriptionDetailException {
        Long detailsId = 1L;
        when(transcriptionDetailRepository.findById(detailsId)).thenReturn(Optional.empty());

        Optional<TranscriptionDetailsDto> result = transcriptionDetailService.getDetailsById(detailsId);

        assertTrue(result.isEmpty());
    }

    @Test
    void testReviewNotes_ArchivedNote() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);
        request.setAction(AINOTE.DRAFT);
        
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        
        when(transcriptionDetailRepository.findById(request.getTranscriptionDetailId()))
            .thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionDetail.isArchived()).thenReturn(true);
        when(userService.getCurrentUser()).thenReturn(user);

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.reviewNotes(request);
        });
    }

    @Test
    void testReviewNotes_SignActionNotAllowed() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);
        request.setAction(AINOTE.SIGNED);
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);
        
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        Prescription prescription = mock(Prescription.class);
        Appointment appointment = mock(Appointment.class);
        
        when(transcriptionDetailRepository.findById(request.getTranscriptionDetailId()))
            .thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(false);
        when(prescriptionService.findOne(request.getPrescriptionId())).thenReturn(prescription);
        when(appointmentService.findOne(request.getAppointmentId())).thenReturn(appointment);
        when(prescription.getTreatingPractitionerId()).thenReturn(2L);
        when(prescription.getResidentId()).thenReturn(3L);
        when(appointment.getUserFourId()).thenReturn(4L);
        when(appointment.getUserId()).thenReturn(5L);

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.reviewNotes(request);
        });
    }

    @Test
    void testReviewNotes_SignActionAllowedForSuperAdmin() throws TranscriptionDetailException {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);
        request.setAction(AINOTE.SIGNED);
        request.setPrescriptionId(1L);
        request.setSubject("Test Subject");
        request.setReviewedNoteString("Test note content");
        
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        
        when(transcriptionDetailRepository.findById(request.getTranscriptionDetailId()))
            .thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(true);
        
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        savedNote.setId(123L);
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        String result = transcriptionDetailService.reviewNotes(request);

        assertEquals("success", result);
        verify(transcriptionDetail).setSubject(request.getSubject());
        verify(transcriptionDetailRepository).save(transcriptionDetail);
        verify(transcriptionDetail).setStatus(request.getAction());
    }

    @Test
    void testReviewNotes_PublishActionNotAllowed() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);
        request.setAction(AINOTE.PUBLISHED);
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);
        
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        Prescription prescription = mock(Prescription.class);
        Appointment appointment = mock(Appointment.class);
        
        when(transcriptionDetailRepository.findById(request.getTranscriptionDetailId()))
            .thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(false);
        when(prescriptionService.findOne(request.getPrescriptionId())).thenReturn(prescription);
        when(appointmentService.findOne(request.getAppointmentId())).thenReturn(appointment);
        when(prescription.getTreatingPractitionerId()).thenReturn(2L);
        when(appointment.getUserFourId()).thenReturn(3L);
        when(appointment.getUserId()).thenReturn(4L);

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.reviewNotes(request);
        });
    }

    @Test
    void testReviewNotes_PublishActionAllowedForSuperAdmin() throws TranscriptionDetailException {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);
        request.setAction(AINOTE.PUBLISHED);
        request.setPrescriptionId(1L);
        request.setSubject("Test Subject");
        request.setReviewedNoteString("Test note content");
        
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        
        when(transcriptionDetailRepository.findById(request.getTranscriptionDetailId()))
            .thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(true);
        
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        savedNote.setId(123L);
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        String result = transcriptionDetailService.reviewNotes(request);

        assertEquals("success", result);
        verify(transcriptionDetail).setSubject(request.getSubject());
        verify(transcriptionDetailRepository).save(transcriptionDetail);
        verify(transcriptionDetail).setStatus(request.getAction());
    }

    @Test
    void testViewGeneratedNotes_NotFound() {
        Long transcriptionDetailsId = 1L;
        when(transcriptionDetailRepository.findById(transcriptionDetailsId))
            .thenReturn(Optional.empty());

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.viewGeneratedNotes(transcriptionDetailsId);
        });
    }

    @Test
    void testViewGeneratedNotes_JsonProcessingError() throws JsonProcessingException {
        Long transcriptionDetailsId = 1L;
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        
        when(transcriptionDetailRepository.findById(transcriptionDetailsId))
            .thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionDetail.getStatus()).thenReturn(AINOTE.READY);
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(transcriptionDetail.getOutputBucketName()).thenReturn("test-bucket");
        when(transcriptionDetail.getJobName()).thenReturn("test-job");
        
        when(awsUtil.getUsEast1Client()).thenReturn(awsS3Service);
        when(awsS3Service.getFile(anyString(), anyString())).thenReturn(Optional.of("test".getBytes(StandardCharsets.UTF_8)));
        
        when(transcriptionUtil.fromJson(anyString(), eq(ConversationContainer.class)))
            .thenReturn(new ConversationContainer());
        when(transcriptionUtil.fromJson(anyString(), eq(ClinicalDocumentationContainer.class)))
            .thenThrow(new JsonProcessingException("Error") {});

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.viewGeneratedNotes(transcriptionDetailsId);
        });
    }

    @Test
    void testLoadForeignKeys() {
        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setNoteId(1L);

        Note mockNote = mock(Note.class);
        when(noteService.findOne(1L)).thenReturn(mockNote);

        transcriptionDetailService.loadForeignKeys(transcriptionDetail);

        assertEquals(mockNote, transcriptionDetail.getNote());
        verify(noteService).findOne(1L);
    }

    @Test
    void testLoadForeignKeys_NullNote() {
        transcriptionDetailService.loadForeignKeys(null);
        // Should not throw any exceptions
    }

    @Test
    void testLoadForeignKeys_NoNoteId() {
        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();

        transcriptionDetailService.loadForeignKeys(transcriptionDetail);

        assertNull(transcriptionDetail.getNote());
        verify(noteService, never()).findOne(any());
    }

    @Test
    void testViewGeneratedNotes_ReviewedNote() throws JsonProcessingException, TranscriptionDetailException {
        Long transcriptionDetailsId = 1L;
        
        // Create mocks
        Note mockNote = mock(Note.class);
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        ConversationContainer conversationContainer = new ConversationContainer();
        ClinicalDocumentationContainer clinicalDocumentationContainer = new ClinicalDocumentationContainer();

        // Mock AWS S3 service
        when(awsUtil.getUsEast1Client()).thenReturn(awsS3Service);
        when(awsS3Service.getFile(anyString(), anyString())).thenReturn(Optional.of("test".getBytes(StandardCharsets.UTF_8)));

        when(noteService.findOne(1L)).thenReturn(mockNote);
        when(mockNote.getNote()).thenReturn("Reviewed note content");
        when(mockNote.getSubject()).thenReturn("Note Subject");

        when(transcriptionDetail.getStatus()).thenReturn(AINOTE.DRAFT);
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(transcriptionDetail.getNoteId()).thenReturn(1L);
        when(transcriptionDetail.getNote()).thenReturn(mockNote);
        when(transcriptionDetail.getSubject()).thenReturn("Note Subject");
        when(transcriptionDetail.getOutputBucketName()).thenReturn("test-bucket");
        when(transcriptionDetail.getJobName()).thenReturn("test-job");
        when(transcriptionDetail.isReviewed()).thenReturn(true);

        when(transcriptionDetailRepository.findById(transcriptionDetailsId))
            .thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionUtil.fromJson(anyString(), eq(ConversationContainer.class)))
            .thenReturn(conversationContainer);
        when(transcriptionUtil.fromJson(anyString(), eq(ClinicalDocumentationContainer.class)))
            .thenReturn(clinicalDocumentationContainer);

        Optional<TranscriptionAppointmentNotesDto> result = transcriptionDetailService.viewGeneratedNotes(transcriptionDetailsId);

        assertTrue(result.isPresent());
        TranscriptionAppointmentNotesDto dto = result.get();
        assertEquals("Reviewed note content", dto.getReviewNoteString());
        assertEquals("Note Subject", dto.getSubject());
        assertEquals(AINOTE.DRAFT, dto.getStatus());
        assertFalse(dto.isArchived());
        
        verify(transcriptionDetailRepository).findById(transcriptionDetailsId);
        verify(noteService).findOne(1L);
        verify(transcriptionUtil).fromJson(anyString(), eq(ConversationContainer.class));
        verify(transcriptionUtil).fromJson(anyString(), eq(ClinicalDocumentationContainer.class));
        verify(awsS3Service, times(2)).getFile(anyString(), anyString());
    }

    @Test
    void testSaveTranscriptionChanges() {
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);

        transcriptionDetailService.saveTranscriptionChanges(transcriptionDetail);

        verify(transcriptionDetailRepository).save(transcriptionDetail);
    }

    @Test
    void testReviewNotes_SignedToDraftNotAllowed() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);
        request.setAction(AINOTE.DRAFT);
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);
        
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        Prescription prescription = mock(Prescription.class);
        Appointment appointment = mock(Appointment.class);
        
        when(transcriptionDetailRepository.findById(request.getTranscriptionDetailId()))
            .thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(transcriptionDetail.getStatus()).thenReturn(AINOTE.SIGNED);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(false);
        when(prescriptionService.findOne(request.getPrescriptionId())).thenReturn(prescription);
        when(appointmentService.findOne(request.getAppointmentId())).thenReturn(appointment);
        when(prescription.getTreatingPractitionerId()).thenReturn(2L);
        when(appointment.getUserFourId()).thenReturn(3L);
        when(appointment.getUserId()).thenReturn(4L);

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.reviewNotes(request);
        });
    }

    @Test
    void testReviewNotes_PublishedNoteCannotBeEdited() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);
        request.setAction(AINOTE.DRAFT);
        request.setPrescriptionId(1L);
        request.setAppointmentId(1L);
        
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        
        when(transcriptionDetailRepository.findById(request.getTranscriptionDetailId()))
            .thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(transcriptionDetail.getStatus()).thenReturn(AINOTE.PUBLISHED);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(false);

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.reviewNotes(request);
        });
    }

    @Test
    void testReviewNotes_SaveError() {
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);
        request.setAction(AINOTE.DRAFT);
        request.setSubject("Test Subject");
        request.setReviewedNoteString("Test note content");
        
        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        User user = mock(User.class);
        
        when(transcriptionDetailRepository.findById(request.getTranscriptionDetailId()))
            .thenReturn(Optional.of(transcriptionDetail));
        when(transcriptionDetail.isArchived()).thenReturn(false);
        when(userService.getCurrentUser()).thenReturn(user);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(false);
        when(noteService.saveNote(any(Note.class), anyString())).thenThrow(new RuntimeException("Save error"));

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.reviewNotes(request);
        });
    }

    @Test
    void testWriteCompletedNotesFromBucket_NotFound() {
        AiNoteWebhookDto aiNoteWebhookDto = mock(AiNoteWebhookDto.class);
        String jobName = "123456";
        Long detailId = 123456L;

        when(transcriptionUtil.getTranscriptionDetailsId(jobName)).thenReturn(detailId);
        when(transcriptionDetailRepository.findById(detailId)).thenReturn(Optional.empty());
        when(aiNoteWebhookDto.getJobName()).thenReturn(jobName);

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.writeCompletedNotesFromBucket(aiNoteWebhookDto);
        });
    }

    @Test
    void testWriteCompletedNotesFromBucket_S3Error() throws TranscriptionDetailException {
        AiNoteWebhookDto aiNoteWebhookDto = mock(AiNoteWebhookDto.class);
        String jobName = "123456";
        Long detailId = 123456L;
        String bucketName = "test-bucket";
        String eventTime = "2034-02-25T00:00:00.000Z";

        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        when(transcriptionDetail.getId()).thenReturn(detailId);
        when(transcriptionDetail.getOutputBucketName()).thenReturn(bucketName);
        when(transcriptionDetail.getJobName()).thenReturn(jobName);
        when(transcriptionDetail.getCreatedById()).thenReturn(1L);

        when(transcriptionUtil.getTranscriptionDetailsId(jobName)).thenReturn(detailId);
        when(transcriptionDetailRepository.findById(detailId)).thenReturn(Optional.of(transcriptionDetail));
        when(awsUtil.getUsEast1Client()).thenReturn(awsS3Service);
        when(awsS3Service.getFile(anyString(), anyString())).thenReturn(Optional.empty());
        when(aiNoteWebhookDto.getJobName()).thenReturn(jobName);
        when(aiNoteWebhookDto.getBucketName()).thenReturn(bucketName);
        when(aiNoteWebhookDto.getEventTime()).thenReturn(eventTime);

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.writeCompletedNotesFromBucket(aiNoteWebhookDto);
        });
    }

    @Test
    void testArchiveTranscription_InvalidStatus() {
        ArchiveRequest request = new ArchiveRequest();
        request.setTranscriptionDetailId(1L);
        request.setArchiveNote(true);

        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        when(transcriptionDetail.getStatus()).thenReturn(AINOTE.PUBLISHED);

        when(transcriptionDetailRepository.findById(1L)).thenReturn(Optional.of(transcriptionDetail));

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.archiveTranscription(request);
        });
    }

    @Test
    void testArchiveTranscription_NotAuthorized() {
        ArchiveRequest request = new ArchiveRequest();
        request.setTranscriptionDetailId(1L);
        request.setArchiveNote(true);

        User user = mock(User.class);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(false);

        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        when(transcriptionDetail.getStatus()).thenReturn(AINOTE.READY);
        when(transcriptionDetail.getPrescriptionId()).thenReturn(1L);
        when(transcriptionDetail.getAppointmentId()).thenReturn(1L);

        Prescription prescription = mock(Prescription.class);
        Appointment appointment = mock(Appointment.class);

        when(prescriptionService.findOne(1L)).thenReturn(prescription);
        when(appointmentService.findOne(1L)).thenReturn(appointment);
        when(appointment.getUserFourId()).thenReturn(2L);
        when(appointment.getUserId()).thenReturn(3L);
        when(prescription.getTreatingPractitionerId()).thenReturn(4L);

        when(transcriptionDetailRepository.findById(1L)).thenReturn(Optional.of(transcriptionDetail));
        when(userService.getCurrentUser()).thenReturn(user);

        assertThrows(TranscriptionDetailException.class, () -> {
            transcriptionDetailService.archiveTranscription(request);
        });
    }

    @Test
    void testArchiveTranscription_UnarchiveNote() throws TranscriptionDetailException, JsonProcessingException, IOException {
        ArchiveRequest request = new ArchiveRequest();
        request.setTranscriptionDetailId(1L);
        request.setArchiveNote(false);

        User user = mock(User.class);
        when(user.getId()).thenReturn(1L);
        when(user.getIsSuperAdmin()).thenReturn(true);

        TranscriptionDetail transcriptionDetail = mock(TranscriptionDetail.class);
        when(transcriptionDetail.getStatus()).thenReturn(AINOTE.READY);
        when(transcriptionDetail.getPrescriptionId()).thenReturn(1L);
        when(transcriptionDetail.getAppointmentId()).thenReturn(1L);
        when(transcriptionDetail.getOutputBucketName()).thenReturn("test-bucket");
        when(transcriptionDetail.getJobName()).thenReturn("test-job");

        // Mock AWS S3 service
        when(awsUtil.getUsEast1Client()).thenReturn(awsS3Service);
        when(awsS3Service.getFile(anyString(), anyString())).thenReturn(Optional.of("test".getBytes(StandardCharsets.UTF_8)));

        // Mock transcription util
        ClinicalDocumentationContainer container = new ClinicalDocumentationContainer();
        ClinicalDocumentation clinicalDocumentation = new ClinicalDocumentation();
        List<Section> sections = new ArrayList<>();
        clinicalDocumentation.setSections(sections);
        container.setClinicalDocumentation(clinicalDocumentation);
        
        when(transcriptionUtil.fromJson(anyString(), eq(ClinicalDocumentationContainer.class))).thenReturn(container);
        when(transcriptionUtil.filterAndOrderSectionsInRawJson(anyString())).thenReturn("test");

        // Mock note service response
        Map<String, Object> noteMap = new HashMap<>();
        Note savedNote = new Note();
        savedNote.setId(123L);
        noteMap.put(OptimisticLockingUtil.SAVED, savedNote);
        when(noteService.saveNote(any(Note.class), anyString())).thenReturn(noteMap);

        when(transcriptionDetailRepository.findById(1L)).thenReturn(Optional.of(transcriptionDetail));
        when(userService.getCurrentUser()).thenReturn(user);

        String result = transcriptionDetailService.archiveTranscription(request);

        assertEquals("success", result);
        verify(transcriptionDetail).setArchived(false);
        verify(transcriptionDetail).setStatus(AINOTE.READY);
        verify(transcriptionDetail).setNoteId(123L);
        verify(transcriptionDetail).setSubject("AI Note: Save As Draft Generated Subject Line");
    }

    @Test
    void testGetClinicalNotesString_NullInput() {
        String result = transcriptionDetailService.getClinicalNotesString(null);
        assertEquals("", result);
    }

    @Test
    void testGetClinicalNotesString_EmptySections() {
        ClinicalDocumentation clinicalDocumentation = mock(ClinicalDocumentation.class);
        when(clinicalDocumentation.getSections()).thenReturn(Collections.emptyList());

        String result = transcriptionDetailService.getClinicalNotesString(clinicalDocumentation);
        assertEquals("", result);
    }

    @Test
    void testGetTranscriptsString_NullInput() {
        String result = transcriptionDetailService.getTranscriptsString(null);
        assertEquals("", result);
    }

    @Test
    void testGetTranscriptsString_EmptySegments() {
        Conversation conversation = mock(Conversation.class);
        when(conversation.getTranscriptSegments()).thenReturn(Collections.emptyList());

        String result = transcriptionDetailService.getTranscriptsString(conversation);
        assertEquals("", result);
    }
}
