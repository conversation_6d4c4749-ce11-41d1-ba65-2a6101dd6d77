package com.nymbl.ai.notes.service;

import com.nymbl.ai.notes.dto.AINOTE;
import com.nymbl.ai.notes.dto.TranscriptionUploadRequest;
import com.nymbl.ai.notes.model.TranscriptionAudio;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.repository.TranscriptionAudioRepository;
import com.nymbl.ai.notes.repository.TranscriptionDetailRepository;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.model.Appointment;
import com.nymbl.tenant.service.Fake;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import software.amazon.awssdk.services.transcribe.TranscribeClient;
import software.amazon.awssdk.services.transcribe.model.MedicalScribeJob;
import software.amazon.awssdk.services.transcribe.model.StartMedicalScribeJobRequest;
import software.amazon.awssdk.services.transcribe.model.StartMedicalScribeJobResponse;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class HealthScribeServiceTest {

    @Mock
    private TranscribeClient transcribeClient;

    @Mock
    private TranscriptionDetailRepository transcriptionDetailRepository;

    @Mock
    private TranscriptionAudioRepository transcriptionAudioRepository;

    @Mock
    private TranscriptionUtil transcriptionUtil;

    @Mock
    private AwsUtil awsUtil;

    @Mock
    private AWSS3Service awss3Service;

    @InjectMocks
    private HealthScribeService healthScribeService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(healthScribeService, "audioBucket", "test-audio-bucket");
        ReflectionTestUtils.setField(healthScribeService, "filePrefix", "test-prefix/");
        ReflectionTestUtils.setField(healthScribeService, "outputBucket", "test-output-bucket");
        ReflectionTestUtils.setField(healthScribeService, "iamrole", "test-iam-role");
        ReflectionTestUtils.setField(healthScribeService, "activeProfile", "test");
        
        // Add mock behavior for formatTime
        when(transcriptionUtil.formatTime(anyLong())).thenAnswer(invocation -> {
            long seconds = invocation.getArgument(0);
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            long secs = seconds % 60;
            return String.format("%02d:%02d:%02d", hours, minutes, secs);
        });
    }

    @Test
    void testInitiateRecordingSuccess() {
        Appointment appointment = Fake.getAppointment();

        TranscriptionUploadRequest request = new TranscriptionUploadRequest();
        request.setAppointmentId(1L);
        request.setPatientId(2L);
        request.setBranchId(3L);
        request.setPractitionerId(4L);

        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setId(100L);
        transcriptionDetail.setAppointmentId(appointment.getId());
        transcriptionDetail.setAppointment(appointment);

        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn("mockedTenant");

            when(transcriptionDetailRepository.save(any(TranscriptionDetail.class)))
                    .thenReturn(transcriptionDetail);

            Long result = healthScribeService.initiateRecording(request);

            assertEquals(100L, result);
            verify(transcriptionDetailRepository, times(1)).save(any(TranscriptionDetail.class));
        }
    }

    @Test
    void testInitiateRecordingFailure() {
        TranscriptionUploadRequest request = new TranscriptionUploadRequest();
        request.setAppointmentId(1L);

        try (MockedStatic<TenantContext> mockedTenantContext = mockStatic(TenantContext.class)) {
            mockedTenantContext.when(TenantContext::getCurrentTenant).thenReturn("mockedTenant");

            when(transcriptionDetailRepository.save(any(TranscriptionDetail.class)))
                    .thenThrow(new RuntimeException("Database error"));

            assertThrows(RuntimeException.class, () -> healthScribeService.initiateRecording(request));
        }
    }

    @Test
    void testUploadAudioFileToS3_Success() throws IOException, InterruptedException {
        // Setup
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.wav",
            "audio/wav", "test audio content".getBytes()
        );
        Long detailsId = 1L;

        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setId(detailsId);
        transcriptionDetail.setJobName("test-job");
        transcriptionDetail.setAudioFileName("test-prefix/test.wav");

        when(transcriptionDetailRepository.findById(detailsId))
            .thenReturn(Optional.of(transcriptionDetail));
        when(awsUtil.getUsEast1Client()).thenReturn(awss3Service);
        when(awss3Service.uploadFileToS3(any(), anyString(), anyString()))
            .thenReturn(true);
        when(transcriptionUtil.makeJobName(any(TranscriptionDetail.class)))
            .thenReturn("test-job");

        StartMedicalScribeJobResponse mockResponse = mock(StartMedicalScribeJobResponse.class);
        MedicalScribeJob mockJob = mock(MedicalScribeJob.class);
        when(mockResponse.medicalScribeJob()).thenReturn(mockJob);
        when(mockJob.startTime()).thenReturn(java.time.Instant.now());
        when(transcribeClient.startMedicalScribeJob(any(StartMedicalScribeJobRequest.class)))
            .thenReturn(mockResponse);

        // Execute
        String result = healthScribeService.uploadAudioFileToS3(file, detailsId);

        // Verify
        assertEquals("success", result);
        verify(transcriptionDetailRepository).save(any(TranscriptionDetail.class));
    }

    @Test
    void testUploadAudioFileToS3_Failure() throws IOException, InterruptedException {
        // Setup
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.wav",
            "audio/wav", "test audio content".getBytes()
        );
        Long detailsId = 1L;

        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setId(detailsId);
        transcriptionDetail.setJobName("test-job");
        transcriptionDetail.setAudioFileName("test-file.wav");

        when(transcriptionDetailRepository.findById(detailsId))
            .thenReturn(Optional.of(transcriptionDetail));
        when(awsUtil.getUsEast1Client()).thenReturn(awss3Service);
        when(awss3Service.uploadFileToS3(any(), anyString(), anyString()))
            .thenThrow(new IOException("Upload failed"));
        when(transcriptionUtil.makeJobName(any(TranscriptionDetail.class)))
            .thenReturn("test-job");

        // Execute
        String result = healthScribeService.uploadAudioFileToS3(file, detailsId);

        // Verify
        assertEquals("Exception: while attempting to create notes", result);
        verify(transcriptionAudioRepository).save(any(TranscriptionAudio.class));
        verify(transcriptionDetailRepository, never()).save(any(TranscriptionDetail.class));
        verify(transcribeClient, never()).startMedicalScribeJob(any(StartMedicalScribeJobRequest.class));
    }

    @Test
    void testFormatTime() {
        // Test cases for different time durations
        when(transcriptionUtil.formatTime(0L)).thenReturn("00:00:00");
        when(transcriptionUtil.formatTime(1L)).thenReturn("00:00:01");
        when(transcriptionUtil.formatTime(60L)).thenReturn("00:01:00");
        when(transcriptionUtil.formatTime(3600L)).thenReturn("01:00:00");
        when(transcriptionUtil.formatTime(3661L)).thenReturn("01:01:01");

        assertEquals("00:00:00", healthScribeService.formatTime(0));
        assertEquals("00:00:01", healthScribeService.formatTime(1));
        assertEquals("00:01:00", healthScribeService.formatTime(60));
        assertEquals("01:00:00", healthScribeService.formatTime(3600));
        assertEquals("01:01:01", healthScribeService.formatTime(3661));
    }

    @Test
    void testMakeTranscriptionDetail() {
        // Setup
        TranscriptionUploadRequest request = new TranscriptionUploadRequest();
        request.setAppointmentId(1L);
        request.setPatientId(2L);
        request.setBranchId(3L);
        request.setPractitionerId(4L);
        request.setAudioLength(120.5);
        request.setFileExtension(".wav");
        request.setTenant("test_tenant");

        Map<String, String> pathVariables = new HashMap<>();
        pathVariables.put("appointmentId", "1");
        pathVariables.put("patientId", "2");
        when(transcriptionUtil.convertToMap(request)).thenReturn(pathVariables);
        when(transcriptionUtil.replacePathVariables(anyString(), anyMap()))
            .thenReturn("test-prefix/");

        // Execute
        TranscriptionDetail result = healthScribeService.makeTranscriptionDetail(request, AINOTE.STARTED);

        // Verify
        assertNotNull(result);
        assertEquals(1L, result.getAppointmentId());
        assertEquals(2L, result.getPatientId());
        assertEquals(3L, result.getBranchId());
        assertEquals(4L, result.getPractitionerId());
        assertEquals(AINOTE.STARTED, result.getStatus());
        assertTrue(result.getMediaFileLocation().startsWith("s3://"));
    }

    @Test
    void testGetOutputBucketName() {
        // Test for non-prod environment
        String result = healthScribeService.getOutputBucketName("test_tenant");
        assertEquals("test-output-bucket-test.tenant-test", result);

        // Test for prod environment
        ReflectionTestUtils.setField(healthScribeService, "activeProfile", "prod");
        result = healthScribeService.getOutputBucketName("test_tenant");
        assertEquals("test-output-bucket-test.tenant", result);
    }

    @Test
    void testTranscribeAudio() {
        // Setup
        TranscriptionDetail transcriptionDetail = new TranscriptionDetail();
        transcriptionDetail.setMediaFileLocation("s3://test-bucket/test.wav");
        transcriptionDetail.setOutputBucketName("test-output-bucket");
        String jobName = "test-job";

        StartMedicalScribeJobResponse mockResponse = mock(StartMedicalScribeJobResponse.class);
        when(transcribeClient.startMedicalScribeJob(any(StartMedicalScribeJobRequest.class)))
            .thenReturn(mockResponse);

        // Execute
        StartMedicalScribeJobResponse result = healthScribeService.transcribeAudio(transcriptionDetail, jobName);

        // Verify
        assertNotNull(result);
        verify(transcribeClient).startMedicalScribeJob(any(StartMedicalScribeJobRequest.class));
    }
}
