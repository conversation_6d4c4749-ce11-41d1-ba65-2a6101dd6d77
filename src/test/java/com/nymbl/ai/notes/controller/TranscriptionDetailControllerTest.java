package com.nymbl.ai.notes.controller;

import com.nymbl.ai.notes.dto.ReviewedNoteRequest;
import com.nymbl.ai.notes.dto.TranscriptionAppointmentNotesDto;
import com.nymbl.ai.notes.dto.TranscriptionDetailsDto;
import com.nymbl.ai.notes.exception.TranscriptionDetailException;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.service.TranscriptionDetailService;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

class TranscriptionDetailControllerTest {

    @Mock
    private TranscriptionDetailService transcriptionDetailService;

    @InjectMocks
    private TranscriptionDetailController transcriptionDetailController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSearchWithResults() {
        // Prepare test data
        List<Long> practitionerIds = List.of(1L, 2L);
        Long patientId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        Long appointmentId = 1L;
        String status = "COMPLETED";
        Pageable pageable = PageRequest.of(0, 1000);

        // Mock the service method
        List<TranscriptionDetailsDto> mockResults = List.of(new TranscriptionDetailsDto());
        when(transcriptionDetailService.search(practitionerIds, patientId, appointmentId, startDate, endDate, status, false, pageable))
                .thenReturn(Optional.of(mockResults));

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.search(practitionerIds, patientId, startDate, endDate, appointmentId, status, pageable, mock(HttpServletRequest.class));

        // Verify the response
        assertEquals(ResponseEntity.ok(mockResults), response);
        verify(transcriptionDetailService, times(1)).search(practitionerIds, patientId, appointmentId, startDate, endDate, status, false, pageable);
    }

    @Test
    void testSearchWithoutResults() {
        // Prepare test data
        List<Long> practitionerIds = List.of(1L, 2L);
        Long patientId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        Long appointmentId = 1L;
        String status = "COMPLETED";
        Pageable pageable = PageRequest.of(0, 1000);

        // Mock the service method
        when(transcriptionDetailService.search(practitionerIds, patientId, appointmentId, startDate, endDate, status, false, pageable))
                .thenReturn(Optional.empty());

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.search(practitionerIds, patientId, startDate, endDate, appointmentId, status, pageable, mock(HttpServletRequest.class));

        // Verify the response
        assertEquals(ResponseEntity.noContent().build(), response);
        verify(transcriptionDetailService, times(1)).search(practitionerIds, patientId, appointmentId, startDate, endDate, status, false, pageable);
    }

    @Test
    void testSearchWithArchivedStatus() {
        // Prepare test data
        List<Long> practitionerIds = List.of(1L, 2L);
        Long patientId = 1L;
        Date startDate = new Date();
        Date endDate = new Date();
        Long appointmentId = 1L;
        String status = "ARCHIVED";
        Pageable pageable = PageRequest.of(0, 1000);

        // Mock the service method
        List<TranscriptionDetailsDto> mockResults = List.of(new TranscriptionDetailsDto());
        when(transcriptionDetailService.search(practitionerIds, patientId, appointmentId, startDate, endDate, null, true, pageable))
                .thenReturn(Optional.of(mockResults));

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.search(practitionerIds, patientId, startDate, endDate, appointmentId, status, pageable, mock(HttpServletRequest.class));

        // Verify the response
        assertEquals(ResponseEntity.ok(mockResults), response);
        verify(transcriptionDetailService, times(1)).search(practitionerIds, patientId, appointmentId, startDate, endDate, null, true, pageable);
    }

    @Test
    void testViewWithResults() throws TranscriptionDetailException {
        // Prepare test data
        Long transcriptionAppointmentNotesId = 1L;
        TranscriptionAppointmentNotesDto mockResult = new TranscriptionAppointmentNotesDto();

        // Mock the service method
        when(transcriptionDetailService.viewGeneratedNotes(transcriptionAppointmentNotesId)).thenReturn(Optional.of(mockResult));

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.view(transcriptionAppointmentNotesId);

        // Verify the response
        assertEquals(ResponseEntity.ok(mockResult), response);
        verify(transcriptionDetailService, times(1)).viewGeneratedNotes(transcriptionAppointmentNotesId);
    }

    @Test
    void testViewWithoutResults() throws TranscriptionDetailException {
        // Prepare test data
        Long transcriptionAppointmentNotesId = 1L;

        // Mock the service method
        when(transcriptionDetailService.viewGeneratedNotes(transcriptionAppointmentNotesId)).thenReturn(Optional.empty());

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.view(transcriptionAppointmentNotesId);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().build(), response);
        verify(transcriptionDetailService, times(1)).viewGeneratedNotes(transcriptionAppointmentNotesId);
    }

    @Test
    void testViewWithNotFoundError() throws TranscriptionDetailException {
        // Prepare test data
        Long transcriptionAppointmentNotesId = 1L;

        // Mock the service method to throw exception with "not found" message
        when(transcriptionDetailService.viewGeneratedNotes(transcriptionAppointmentNotesId))
                .thenThrow(new TranscriptionDetailException("not found"));

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.view(transcriptionAppointmentNotesId);

        // Verify the response
        assertEquals(ResponseEntity.notFound().build(), response);
        verify(transcriptionDetailService, times(1)).viewGeneratedNotes(transcriptionAppointmentNotesId);
    }

    @Test
    void testDetailsWithResults() throws TranscriptionDetailException {
        // Prepare test data
        Long transcriptionDetailsId = 1L;
        TranscriptionDetailsDto mockResult = new TranscriptionDetailsDto();

        // Mock the service method
        when(transcriptionDetailService.getDetailsById(transcriptionDetailsId)).thenReturn(Optional.of(mockResult));

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.details(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.ok(mockResult), response);
        verify(transcriptionDetailService, times(1)).getDetailsById(transcriptionDetailsId);
    }

    @Test
    void testDetailsWithoutResults() throws TranscriptionDetailException {
        // Prepare test data
        Long transcriptionDetailsId = 1L;

        // Mock the service method
        when(transcriptionDetailService.getDetailsById(transcriptionDetailsId)).thenReturn(Optional.empty());

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.details(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.notFound().build(), response);
        verify(transcriptionDetailService, times(1)).getDetailsById(transcriptionDetailsId);
    }

    @Test
    void testNotesWithResults() throws TranscriptionDetailException {
        // Prepare test data
        Long transcriptionDetailsId = 1L;
        TranscriptionDetail mockTranscriptionDetail = new TranscriptionDetail();
        String mockClinicalNotesString = "Mock clinical notes";

        // Mock the service methods
        when(transcriptionDetailService.findByTranscriptionDetailId(transcriptionDetailsId)).thenReturn(mockTranscriptionDetail);
        when(transcriptionDetailService.populateAiNote(mockTranscriptionDetail, "/summary.json")).thenReturn(mockClinicalNotesString);

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.notes(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.ok(mockClinicalNotesString), response);
        verify(transcriptionDetailService, times(1)).findByTranscriptionDetailId(transcriptionDetailsId);
        verify(transcriptionDetailService, times(1)).populateAiNote(mockTranscriptionDetail, "/summary.json");
    }

    @Test
    void testNotesWithoutResults() {
        // Prepare test data
        Long transcriptionDetailsId = 1L;

        // Mock the service method to return null when no results found
        when(transcriptionDetailService.findByTranscriptionDetailId(transcriptionDetailsId))
                .thenReturn(null);

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.notes(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().body("not found"), response);
        verify(transcriptionDetailService, times(1)).findByTranscriptionDetailId(transcriptionDetailsId);
    }

    @Test
    void testTranscriptWithResults() throws TranscriptionDetailException {
        // Prepare test data
        Long transcriptionDetailsId = 1L;
        TranscriptionDetail mockTranscriptionDetail = new TranscriptionDetail();
        String mockTranscriptsString = "Mock transcripts";

        // Mock the service methods
        when(transcriptionDetailService.findByTranscriptionDetailId(transcriptionDetailsId)).thenReturn(mockTranscriptionDetail);
        when(transcriptionDetailService.populateAiNote(mockTranscriptionDetail, "/transcript.json")).thenReturn(mockTranscriptsString);

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.transcript(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.ok(mockTranscriptsString), response);
        verify(transcriptionDetailService, times(1)).findByTranscriptionDetailId(transcriptionDetailsId);
        verify(transcriptionDetailService, times(1)).populateAiNote(mockTranscriptionDetail, "/transcript.json");
    }

    @Test
    void testTranscriptWithoutResults() throws TranscriptionDetailException {
        // Prepare test data
        Long transcriptionDetailsId = 1L;

        // Mock the service method
        when(transcriptionDetailService.viewGeneratedNotes(transcriptionDetailsId)).thenReturn(Optional.empty());

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.transcript(transcriptionDetailsId);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().build(), response);
    }

    @Test
    void testSaveReviewedNotesSuccess() throws TranscriptionDetailException {
        // Prepare test data
        ReviewedNoteRequest request = new ReviewedNoteRequest();

        // Mock the service method
        when(transcriptionDetailService.reviewNotes(request)).thenReturn("success");

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.saveReviewedNotes(request);

        // Verify the response
        assertEquals(ResponseEntity.ok("success"), response);
        verify(transcriptionDetailService, times(1)).reviewNotes(request);
    }

    @Test
    void testSaveReviewedNotesFailure() throws TranscriptionDetailException {
        // Prepare test data
        ReviewedNoteRequest request = new ReviewedNoteRequest();

        // Mock the service method
        when(transcriptionDetailService.reviewNotes(request)).thenReturn("failure");

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.saveReviewedNotes(request);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().body("failure"), response);
        verify(transcriptionDetailService, times(1)).reviewNotes(request);
    }

    @Test
    void testSaveReviewedNotesServerError() throws TranscriptionDetailException {
        // Prepare test data
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setReviewedNoteString("Test note");

        // Mock the service method to throw exception with a non-null message
        when(transcriptionDetailService.reviewNotes(request))
                .thenThrow(new TranscriptionDetailException("server error occurred"));

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.saveReviewedNotes(request);

        // Verify the response
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertEquals("server error occurred", response.getBody());
        verify(transcriptionDetailService, times(1)).reviewNotes(request);
    }

    @Test
    void testSaveReviewedNotesWithNotFoundError() throws TranscriptionDetailException {
        // Prepare test data
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);

        // Mock the service method to throw exception with "not found" message
        when(transcriptionDetailService.reviewNotes(request))
                .thenThrow(new TranscriptionDetailException("not found"));

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.saveReviewedNotes(request);

        // Verify the response
        assertEquals(ResponseEntity.notFound().build(), response);
        verify(transcriptionDetailService, times(1)).reviewNotes(request);
    }

    @Test
    void testSaveReviewedNotesWithServerError() throws TranscriptionDetailException {
        // Prepare test data
        ReviewedNoteRequest request = new ReviewedNoteRequest();
        request.setTranscriptionDetailId(1L);

        // Mock the service method to throw exception
        when(transcriptionDetailService.reviewNotes(request))
                .thenThrow(new TranscriptionDetailException("server error occurred"));

        // Call the controller method
        ResponseEntity<?> response = transcriptionDetailController.saveReviewedNotes(request);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().body("server error occurred"), response);
        verify(transcriptionDetailService, times(1)).reviewNotes(request);
    }

}
