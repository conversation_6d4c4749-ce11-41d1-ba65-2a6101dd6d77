package com.nymbl.ai.notes.controller;

import com.nymbl.ai.notes.dto.TranscriptionUploadRequest;
import com.nymbl.ai.notes.service.HealthScribeService;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.master.model.Company;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.master.service.CompanyService;
import com.nymbl.tenant.TenantContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class HealthScribeControllerTest {

    @Mock
    private HealthScribeService healthScribeService;

    @Mock
    private TranscriptionUtil transcriptionUtil;

    @Mock
    private CompanyService companyService;

    @Mock
    private AwsUtil awsUtil;

    @Mock
    private AWSS3Service awss3Service;

    @InjectMocks
    private HealthScribeController healthScribeController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(awsUtil.getUsEast1Client()).thenReturn(awss3Service);
    }

    @Test
    void testUploadAudioSuccess() {
        // Prepare test data
        MultipartFile file = new MockMultipartFile("file", "audio.m4a", MediaType.MULTIPART_FORM_DATA_VALUE, "audio content".getBytes());
        Long practitionerId = 1L;
        Long appointmentId = 2L;
        Long patientId = 3L;
        Long prescriptionId = 4L;
        Long branchId = 5L;
        Long detailsId = 100L;

        // Mock the service methods
        when(healthScribeService.initiateRecording(any(TranscriptionUploadRequest.class))).thenReturn(detailsId);
        when(healthScribeService.uploadAudioFileToS3(any(MultipartFile.class), eq(detailsId))).thenReturn("success");
        when(transcriptionUtil.getFileExtension(any())).thenReturn(".wav");

        // Call the controller method
        ResponseEntity<?> response = healthScribeController.uploadAudio(file, practitionerId, appointmentId, patientId, prescriptionId, branchId, 0);

        // Verify the response
        assertEquals(ResponseEntity.ok("success"), response);
        verify(healthScribeService, times(1)).initiateRecording(any(TranscriptionUploadRequest.class));
        verify(healthScribeService, times(1)).uploadAudioFileToS3(any(MultipartFile.class), eq(detailsId));
    }

    @Test
    void testUploadAudioFailure() {
        // Prepare test data
        MultipartFile file = new MockMultipartFile("file", "audio.m4a", MediaType.MULTIPART_FORM_DATA_VALUE, "audio content".getBytes());
        Long practitionerId = 1L;
        Long appointmentId = 2L;
        Long patientId = 3L;
        Long prescriptionId = 4L;
        Long branchId = 5L;
        Long detailsId = 100L;

        // Mock the service methods
        when(healthScribeService.initiateRecording(any(TranscriptionUploadRequest.class))).thenReturn(detailsId);
        when(healthScribeService.uploadAudioFileToS3(any(MultipartFile.class), eq(detailsId))).thenReturn("Exception");
        when(transcriptionUtil.getFileExtension(any())).thenReturn(".wav");

        // Call the controller method
        ResponseEntity<?> response = healthScribeController.uploadAudio(file, practitionerId, appointmentId, patientId, prescriptionId, branchId, 0);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().body("Exception"), response);
        verify(healthScribeService, times(1)).initiateRecording(any(TranscriptionUploadRequest.class));
        verify(healthScribeService, times(1)).uploadAudioFileToS3(any(MultipartFile.class), eq(detailsId));
    }

    @Test
    void testUploadAudioWithException() {
        // Prepare test data
        MultipartFile file = new MockMultipartFile("file", "audio.m4a", MediaType.MULTIPART_FORM_DATA_VALUE, "audio content".getBytes());
        Long practitionerId = 1L;
        Long appointmentId = 2L;
        Long patientId = 3L;
        Long prescriptionId = 4L;
        Long branchId = 5L;

        // Mock the service methods to throw an exception
        when(healthScribeService.initiateRecording(any(TranscriptionUploadRequest.class)))
            .thenThrow(new RuntimeException("Test exception"));
        when(transcriptionUtil.getFileExtension(any())).thenReturn(".wav");

        // Call the controller method
        ResponseEntity<?> response = healthScribeController.uploadAudio(file, practitionerId, appointmentId, patientId, prescriptionId, branchId, 0);

        // Verify the response
        assertEquals(ResponseEntity.internalServerError().body("Test exception"), response);
        verify(healthScribeService, times(1)).initiateRecording(any(TranscriptionUploadRequest.class));
        verify(healthScribeService, never()).uploadAudioFileToS3(any(), any());
    }

    @Test
    void testCreateBucketSuccess() {
        // Mock tenant context
        String tenant = "test-tenant";
        try (MockedStatic<TenantContext> tenantContextMockedStatic = mockStatic(TenantContext.class)) {
            tenantContextMockedStatic.when(TenantContext::getCurrentTenant).thenReturn(tenant);

            // Mock company service
            when(companyService.findByKey(tenant)).thenReturn(new Company());
            when(healthScribeService.getOutputBucketName(tenant)).thenReturn("test-bucket");
            when(awss3Service.doesBucketExist("test-bucket")).thenReturn(false);

            // Call the controller method
            ResponseEntity<?> response = healthScribeController.createBucket();

            // Verify the response
            assertEquals(ResponseEntity.ok("success"), response);
            verify(awss3Service, times(1)).createBucketAndAddLambdaTrigger(eq("test-bucket"), any());
        }
    }

    @Test
    void testCreateBucketWithInvalidTenant() {
        // Mock tenant context
        String tenant = "invalid-tenant";
        try (MockedStatic<TenantContext> tenantContextMockedStatic = mockStatic(TenantContext.class)) {
            tenantContextMockedStatic.when(TenantContext::getCurrentTenant).thenReturn(tenant);

            // Mock company service to return null for invalid tenant
            when(companyService.findByKey(tenant)).thenReturn(null);

            // Call the controller method
            ResponseEntity<?> response = healthScribeController.createBucket();

            // Verify the response
            assertEquals(ResponseEntity.internalServerError().body("Invalid tenant Provided"), response);
            verify(awss3Service, never()).createBucketAndAddLambdaTrigger(any(), any());
        }
    }

    @Test
    void testCreateBucketWhenBucketExists() {
        // Mock tenant context
        String tenant = "test-tenant";
        try (MockedStatic<TenantContext> tenantContextMockedStatic = mockStatic(TenantContext.class)) {
            tenantContextMockedStatic.when(TenantContext::getCurrentTenant).thenReturn(tenant);

            // Mock company service
            when(companyService.findByKey(tenant)).thenReturn(new Company());
            when(healthScribeService.getOutputBucketName(tenant)).thenReturn("test-bucket");
            when(awss3Service.doesBucketExist("test-bucket")).thenReturn(true);

            // Call the controller method
            ResponseEntity<?> response = healthScribeController.createBucket();

            // Verify the response
            assertEquals(ResponseEntity.ok("Bucket already exists!!!"), response);
            verify(awss3Service, never()).createBucketAndAddLambdaTrigger(any(), any());
        }
    }

    @Test
    void testCreateBucketWithException() {
        // Mock tenant context
        String tenant = "test-tenant";
        try (MockedStatic<TenantContext> tenantContextMockedStatic = mockStatic(TenantContext.class)) {
            tenantContextMockedStatic.when(TenantContext::getCurrentTenant).thenReturn(tenant);

            // Mock company service
            when(companyService.findByKey(tenant)).thenReturn(new Company());
            when(healthScribeService.getOutputBucketName(tenant)).thenReturn("test-bucket");
            when(awss3Service.doesBucketExist("test-bucket")).thenReturn(false);
            doThrow(new RuntimeException("AWS error")).when(awss3Service).createBucketAndAddLambdaTrigger(any(), any());

            // Call the controller method
            ResponseEntity<?> response = healthScribeController.createBucket();

            // Verify the response
            assertEquals(ResponseEntity.internalServerError().body("AWS error"), response);
            verify(awss3Service, times(1)).createBucketAndAddLambdaTrigger(any(), any());
        }
    }
}

