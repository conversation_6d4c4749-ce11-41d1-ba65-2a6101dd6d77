package com.nymbl.ai.notes.aspect;

import com.nymbl.ai.notes.dto.AINOTE;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.service.TranscriptionDetailService;
import com.nymbl.config.dto.NoteDTO;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.Note;
import org.aspectj.lang.JoinPoint;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.sql.Timestamp;
import java.time.OffsetDateTime;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

class NoteSaveAspectTest {

    @Mock
    private TranscriptionDetailService transcriptionDetailService;

    @Mock
    private UserService userService;

    @Mock
    private JoinPoint joinPoint;

    @InjectMocks
    private NoteSaveAspect aspect;

    private Note note;
    private TranscriptionDetail tapNote;
    private User currentUser;
    private NoteDTO noteDTO;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        note = new Note();
        note.setId(1L);
        note.setSubject("Test");
        note.setTranscriptionDetailId(42L);

        tapNote = new TranscriptionDetail();
        tapNote.setSubject("Old Subject");
        tapNote.setStatus(AINOTE.DRAFT);

        currentUser = new User();
        currentUser.setId(1L);
        when(userService.getCurrentUser()).thenReturn(currentUser);

        noteDTO = new NoteDTO();
        when(joinPoint.getArgs()).thenReturn(new Object[]{noteDTO});
    }

    @Test
    void testAfterControllerSave_PublishedNote_TriggersSave() {
        note.setPublished(true);
        noteDTO.setAction("publish");

        when(transcriptionDetailService.findByTranscriptionDetailId(42L)).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, times(1)).saveTranscriptionChanges(tapNote);
        assertEquals("Test", tapNote.getSubject());
        assertEquals(AINOTE.PUBLISHED, tapNote.getStatus());
    }

    @Test
    void testAfterControllerSave_SignedNote_TriggersSave() {
        note.setUserSignedAt(Timestamp.valueOf(OffsetDateTime.now().toLocalDateTime()));
        note.setTranscriptionDetailId(42L);
        note.setPublished(false);
        noteDTO.setAction("sign");
        
        tapNote.setStatus(AINOTE.DRAFT);
        
        when(transcriptionDetailService.findByTranscriptionDetailId(42L)).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, times(1)).saveTranscriptionChanges(tapNote);
        assertEquals(AINOTE.SIGNED, tapNote.getStatus());
        assertEquals("Test", tapNote.getSubject());
    }

    @Test
    void testAfterControllerSave_UnsignedNote_TriggersSave() {
        note.setUserSignedAt(null);
        note.setTranscriptionDetailId(42L);
        note.setPublished(false);
        noteDTO.setAction("unsign");
        
        tapNote.setStatus(AINOTE.SIGNED);
        
        when(transcriptionDetailService.findByTranscriptionDetailId(42L)).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, times(1)).saveTranscriptionChanges(tapNote);
        assertEquals(AINOTE.DRAFT, tapNote.getStatus());
        assertEquals("Test", tapNote.getSubject());
    }

    @Test
    void testAfterControllerSave_AlreadyPublished_NoAction() {
        tapNote.setStatus(AINOTE.PUBLISHED);
        note.setPublished(true);
        noteDTO.setAction("publish");

        when(transcriptionDetailService.findByTranscriptionDetailId(42L)).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, never()).saveTranscriptionChanges(any());
    }

    @Test
    void testAfterControllerSave_InvalidBody_NoAction() {
        ResponseEntity<?> response = ResponseEntity.ok("Some unrelated response");

        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, never()).saveTranscriptionChanges(any());
    }

    @Test
    void testAfterControllerSave_NullResponseOrNote_NoAction() {
        aspect.afterControllerSave(joinPoint, null);

        ResponseEntity<?> response = ResponseEntity.ok(null);
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, never()).saveTranscriptionChanges(any());
    }

    @Test
    void testAfterControllerSave_NullTranscriptionDetail_NoAction() {
        note.setTranscriptionDetailId(42L);
        noteDTO.setAction("publish");

        when(transcriptionDetailService.findByTranscriptionDetailId(42L)).thenReturn(null);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, never()).saveTranscriptionChanges(any());
    }

    @Test
    void testAfterControllerSave_NullAction_NoAction() {
        note.setTranscriptionDetailId(42L);
        noteDTO.setAction(null);

        when(transcriptionDetailService.findByTranscriptionDetailId(42L)).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, never()).saveTranscriptionChanges(any());
    }

    @Test
    void testAfterControllerSave_EmptyAction_NoAction() {
        note.setTranscriptionDetailId(42L);
        noteDTO.setAction("");

        when(transcriptionDetailService.findByTranscriptionDetailId(42L)).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, never()).saveTranscriptionChanges(any());
    }

    @Test
    void testAfterControllerSave_NullTranscriptionDetailId_NoAction() {
        note.setTranscriptionDetailId(null);
        noteDTO.setAction("publish");

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, never()).saveTranscriptionChanges(any());
    }

    @Test
    void testAfterControllerSave_NonNoteResponseBody_NoAction() {
        noteDTO.setAction("publish");

        ResponseEntity<?> response = ResponseEntity.ok("Not a Note object");
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, never()).saveTranscriptionChanges(any());
    }

    @Test
    void testAfterControllerSave_NoNoteDTOInArgs_NoAction() {
        note.setTranscriptionDetailId(42L);
        when(joinPoint.getArgs()).thenReturn(new Object[]{});

        when(transcriptionDetailService.findByTranscriptionDetailId(42L)).thenReturn(tapNote);

        ResponseEntity<?> response = ResponseEntity.ok(note);
        aspect.afterControllerSave(joinPoint, response);

        verify(transcriptionDetailService, never()).saveTranscriptionChanges(any());
    }
}
