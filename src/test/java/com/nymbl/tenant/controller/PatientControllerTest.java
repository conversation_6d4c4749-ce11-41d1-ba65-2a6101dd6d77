package com.nymbl.tenant.controller;

import com.nymbl.config.service.TwilioService;
import com.nymbl.config.utils.FileUtil;
import com.nymbl.tenant.model.Patient;
import com.nymbl.tenant.service.Fake;
import com.nymbl.tenant.service.PatientIntakeService;
import com.nymbl.tenant.service.PatientMergeService;
import com.nymbl.tenant.service.PatientService;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;

import static com.nymbl.config.utils.OptimisticLockingUtil.OPTIMISTIC_MESSAGE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

class PatientControllerTest {

    @Mock
    private PatientService mockPatientService;
    @Mock
    private TwilioService mockTwilioService;
    @Mock
    private PatientIntakeService mockPatientIntakeService;
    @Mock
    private PatientController patientControllerUnderTest;
    @Mock
    private PatientMergeService patientMergeService;
    @Mock
    private FileUtil mockFileUtil;

    @BeforeEach
    void setUp() {
        openMocks(this);
        patientControllerUnderTest = new PatientController(mockPatientService, mockTwilioService, mockPatientIntakeService, patientMergeService, mockFileUtil);
    }

//    @Test
//    void testSave() {
//        // Setup
//        Map<String, Object> respMap = new HashMap<>();
//        final Patient patient = new Patient();
//        final HttpServletRequest request = null;
//        final ResponseEntity<?> expectedResult = new ResponseEntity<>(patient, HttpStatus.OK);
//
//        respMap.put(SAVED, patient);
//        when(mockPatientService.canInactivate(patient)).thenReturn(true);
//        when(mockPatientService.saveForVersion(patient)).thenReturn(respMap);
//
//        // Run the test
//        final ResponseEntity<?> result = patientControllerUnderTest.save(patient, request);
//
//        // Verify the results
//        assertEquals(expectedResult, result);
//    }

    //    @Test
    void testSaveWithObjectOptimisticLockingFailureException() {
        // Setup
        Patient patient = Fake.getPatient();
        HttpServletRequest request = null;
        Map<String, Object> respMap = new HashMap<>();
        respMap.put(OPTIMISTIC_MESSAGE, "This patient has been edited by another user.  Please reload/re-open to get the latest version and re-save with your changes.");
        ResponseEntity<?> expectedResult = new ResponseEntity<>(respMap, HttpStatus.BAD_REQUEST);
        when(mockPatientService.canInactivate(patient)).thenReturn(true);
        when(mockPatientService.saveForVersion(patient)).thenReturn(respMap);

        ResponseEntity<?> result = patientControllerUnderTest.save(patient, request);
        assertEquals(expectedResult, result);
    }

//    @Test
//    void testSaveWithException() {
//        // Setup
//        Patient patient = Fake.getPatient();
//        HttpServletRequest request = null;
//        ResponseEntity<?> expectedResult = new ResponseEntity<>("{ \"message\" : \"Patient cannot be flagged 'inactive'. Claims submissions exists and/or Payments have been applied for this patient.\" }", HttpStatus.BAD_REQUEST);
//        when(mockPatientService.canInactivate(patient)).thenReturn(false);
//        when(mockPatientService.save(patient)).thenThrow(ObjectOptimisticLockingFailureException.class);
//
//        ResponseEntity<?> result = patientControllerUnderTest.save(patient, request);
//        assertEquals(expectedResult, result);
//    }


    @Test
    void testSavePatientWithObjectOptimisticLockingFailureException() {
        // Setup
        Patient patient = Fake.getPatient();
        patient.setCellPhone(null);
        HttpServletRequest request = null;
        Map<String, Object> respMap = new HashMap<>();
        respMap.put(OPTIMISTIC_MESSAGE, "This patient has been edited by another user.  Please reload/re-open to get the latest version and re-save with your changes.");

        ResponseEntity<?> expectedResult = new ResponseEntity<>(respMap, HttpStatus.BAD_REQUEST);
        when(mockPatientService.canInactivate(patient)).thenReturn(false);
        when(mockPatientService.saveForVersion(patient)).thenReturn(respMap);

        ResponseEntity<?> result = patientControllerUnderTest.savePatient(patient, request);
        assertEquals(expectedResult, result);
    }

    @Test
    void testGetPatients() {
        // Setup
        final String keyword = "keyword";
        final Boolean active = false;
        final Long branchId = 0L;
        final HttpServletRequest request = null;
        final ResponseEntity<?> expectedResult = new ResponseEntity<>(Arrays.asList(), HttpStatus.OK);
        when(mockPatientService.getPatients("keyword", false, 0L)).thenReturn(Arrays.asList());

        // Run the test
        final ResponseEntity<?> result = patientControllerUnderTest.getPatients(keyword, active, branchId, request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testSearch() {
        // Setup
        final String q = "q";
        final Boolean inactive = false;
        final HttpServletRequest request = null;
        final ResponseEntity<?> expectedResult = new ResponseEntity<>(new HashSet<Patient>(), HttpStatus.OK);
        when(mockPatientService.search("text", false)).thenReturn(new HashSet<>());

        // Run the test
        final ResponseEntity<?> result = patientControllerUnderTest.search(q, inactive, request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

//    @Test
    void testSort() {
        // Setup
        final Long branchId = 0L;
        final Boolean active = false;
        final String keyword = "keyword";
        final Long primaryPractitionerId = 0L;
        final Pageable pageable = null;
        final HttpServletRequest request = null;
        final Page<Patient> p = null;
        final ResponseEntity<?> expectedResult = new ResponseEntity<>(p, HttpStatus.OK);

        when(mockPatientService.sort("keyword", false, 0L, 0L, null)).thenReturn(p);

        // Run the test
        final ResponseEntity<?> result = patientControllerUnderTest.sort(branchId, active, keyword, primaryPractitionerId, pageable, request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testValidateUniquePatient() {
        // Setup
        final String firstName = "firstName";
        final String lastName = "lastName";
        final Date dob = new GregorianCalendar(2017, 1, 1, 0, 0, 0).getTime();
        final HttpServletRequest request = null;
        final ResponseEntity<?> expectedResult = new ResponseEntity<>(Arrays.asList(), HttpStatus.OK);
        when(mockPatientService.validateUniquePatient("firstName", "lastName", new GregorianCalendar(2017, 1, 1, 0, 0, 0).getTime())).thenReturn(Arrays.asList());

        // Run the test
        final ResponseEntity<?> result = patientControllerUnderTest.validateUniquePatient(firstName, lastName, dob, request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testTextMedicalHistoryForm() {
        // Setup
        Patient patient = Fake.getPatient();
        final Long patientId = 0L;
        final HttpServletRequest request = null;
        final ResponseEntity<?> expectedResult = new ResponseEntity<>(null, HttpStatus.OK);
        when(mockTwilioService.sendMedicalHistoryForm(patient)).thenReturn("result");

        // Run the test
        final ResponseEntity<?> result = patientControllerUnderTest.textMedicalHistoryForm(patientId, request);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testMergePatient() {
        // Setup
        final Long fromPatientId = 1L;
        final Long toPatientId = 2L;
        final HttpServletRequest request = null;
        final ResponseEntity<?> expectedResult = ResponseEntity.noContent().build();
        doNothing().when(patientMergeService).mergePatient(fromPatientId, toPatientId);

        // Run the test
        ResponseEntity<?> result = patientControllerUnderTest.mergePatient(fromPatientId, toPatientId, request);

        // Verify the results
        assertEquals(expectedResult.getStatusCode(), result.getStatusCode());
    }

}
