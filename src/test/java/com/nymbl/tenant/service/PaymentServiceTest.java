package com.nymbl.tenant.service;

import com.nymbl.config.clearingHouse.WaystarAPI;
import com.nymbl.config.dto.DailyCloseReport.DailyCloseReportDTO;
import com.nymbl.config.dto.*;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.*;
import com.nymbl.tenant.specification.PaymentSpecs;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

@SuppressWarnings("unchecked")
class PaymentServiceTest {

    @Mock
    private PaymentRepository mockPaymentRepository;
    @Mock
    private AppliedPayment_L_CodeRepository mockAppliedPaymentLCodeRepository;
    @Mock
    private ClaimRepository mockClaimRepository;
    @Mock
    private PhoneNumberRepository mockPhoneNumberRepository;
    @Mock
    private UserService mockUserService;
    @Mock
    private AppliedPaymentRepository mockAppliedPaymentRepository;
    @Mock
    private AppliedPaymentL_CodeService mockAppliedPaymentLCodeService;
    @Mock
    private ClaimSubmissionService mockClaimSubmissionService;
    @Mock
    private ClaimSubmissionRepository mockClaimSubmissionRepository;
    @Mock
    private SystemSettingService mockSystemSettingService;
    @Mock
    private BranchRepository mockBranchRepository;
    @Mock
    private InsuranceVerificationLCodeService mockInsuranceVerificationLCodeService;
    @Mock
    private InsuranceVerificationService mockInsuranceVerificationService;
    @Mock
    private AppliedPaymentService mockAppliedPaymentService;
    @Mock
    private ClaimService mockClaimService;
    @Mock
    private AdjustmentService mockAdjustmentService;
    @Mock
    private L_CodeCategoryService l_codeCategoryService;
    @Mock
    private L_CodeRepository l_codeRepository;
    @Mock
    private PatientService mockPatientService;
    @Mock
    private AutoPostPatientService mockAutoPostPatientService;
    @Mock
    private AutoPostService mockAutoPostService;
    @Mock
    private EntityManager mockEntityManager;
    @Mock
    private FeatureFlagService mockFeatureFlagService;
    @Mock
    private GeneralLedgerLiveService mockGeneralLedgerLiveService;
    @Mock
    private WaystarAPI mockWaystarAPI;

    @Mock
    private GeneralLedgerService2 mockGeneralLedgerService2;
    @Mock
    private WellFormedPaymentService mockWellFormedPaymentService;

    private PaymentService paymentServiceUnderTest;

    private final Fake<Payment> fake = new Fake<>();

    @BeforeEach
    void setUp() {
        openMocks(this);
        paymentServiceUnderTest = new PaymentService(
                mockPaymentRepository,
                mockAppliedPaymentLCodeRepository,
                mockPhoneNumberRepository,
                mockUserService,
                mockAppliedPaymentRepository,
                mockClaimSubmissionService,
                mockClaimSubmissionRepository,
                mockBranchRepository,
                mockAppliedPaymentService,
                mockClaimService,
                mockAdjustmentService,
                l_codeCategoryService,
                l_codeRepository,
                mockInsuranceVerificationLCodeService,
                mockPatientService,
                mockAutoPostPatientService,
                mockAutoPostService,
                mock(WaystarAPI.class),
                mockFeatureFlagService,
                mockGeneralLedgerLiveService,
                mockGeneralLedgerService2,
                mockWellFormedPaymentService
        );
    }

    @Test
    void testLoadForeignKeys() {
        // Setup
        final Payment o = new Payment();
        User user = new User();
        user.setId(Fake.ID);
        o.setCreatedById(Fake.ID);
        when(mockUserService.getUserById(Fake.ID)).thenReturn(user);

        // Run the test
        paymentServiceUnderTest.loadForeignKeys(o);

        // Verify the results
        assertEquals(Fake.ID, o.getCreatedBy().getId());
    }

    @Test
    void testSearch() {
        // Setup
        final Long id = Fake.ID;
        final Long patientId = Fake.ID;
        final String insuranceCompany = "insuranceCompany";
        LocalDate localDate = LocalDate.of(2021, 1, 20);
        java.util.Date checkStartAndEndDate = java.util.Date.from(localDate.atStartOfDay()
                .atZone(ZoneId.systemDefault())
                .toInstant());
        final String checkNumber = "checkNumber";
        final String totalAmount = "totalAmount";
        final String payerType = "payerType";
        final String unappliedType = "unappliedType";
        final Long branchId = Fake.ID;
        final Long createdBy = Fake.ID;
        LocalDate localCreatedAt = LocalDate.of(2021, 10, 20);
        final java.util.Date createdAt = java.util.Date.from(localCreatedAt.atStartOfDay()
                .atZone(ZoneId.systemDefault())
                .toInstant());
        java.util.Date appliedDate = null;
        final Boolean patientDeposits = false;
        final Pageable pageable = PageRequest.of(0, 1000, Sort.Direction.DESC, "id");
        final Boolean bulkPayments = false;
        final Boolean showSLAdjustments = false;


        Payment payment = new Payment();
        payment.setId(Fake.ID);
        Page<Payment> pp = fake.getFakePageImpl(payment);

        Payment payment2 = new Payment();
        payment2.setId(Fake.ID2);
        Page<Payment> pp2 = fake.getFakePageImpl(payment2);

        PaymentSpecs spec = new PaymentSpecs(id, patientId, insuranceCompany, checkStartAndEndDate, checkStartAndEndDate, checkNumber, totalAmount, payerType, unappliedType, branchId, createdBy, createdAt, appliedDate, patientDeposits, bulkPayments, showSLAdjustments, null, null);

        when(mockPaymentRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(pp);
        when(mockPaymentRepository.findAll(any(Pageable.class))).thenReturn(pp2);

        Query mockedQuery = mock(Query.class);
        when(mockEntityManager.createNativeQuery(anyString())).thenReturn(mockedQuery);
        when(mockedQuery.getSingleResult()).thenReturn(1L);
        List<Object[]> objectList = new ArrayList<>();
        objectList.add(Fake.getPaymentDTOObjectArray());
        when(mockedQuery.getResultList()).thenReturn(objectList);

        // Run the test
        final Page<PaymentDTO> result = paymentServiceUnderTest.search(id, patientId, insuranceCompany, checkStartAndEndDate, checkStartAndEndDate, checkNumber, totalAmount, payerType, unappliedType, branchId, createdBy, createdAt, appliedDate, patientDeposits, false, false, null, null, false, pageable, mockEntityManager);

        // Verify the results
        assertEquals("10861", result.getContent().get(0).getId().toString());

        final Page<PaymentDTO> result2 = paymentServiceUnderTest.search(null, null, null, null, null, null, null, null, null, null, null, null, null, false, false, false, null, null, false, pageable, mockEntityManager);

        // Verify the results
        assertEquals("10861", result.getContent().get(0).getId().toString());
    }

    @Test
    void testGetByAutoPostPatientIdAndUnappliedAmountGreaterThan() {
        // Setup
        final Long autoPostPatientId = Fake.ID;
        final BigDecimal unappliedAmount = new BigDecimal("0.00");
        final List<Payment> expectedResult = Arrays.asList();
        when(mockPaymentRepository.getByAutoPostPatientIdAndUnappliedAmountGreaterThan(Fake.ID, new BigDecimal("0.00"))).thenReturn(Arrays.asList());

        // Run the test
        final List<Payment> result = paymentServiceUnderTest.getByAutoPostPatientIdAndUnappliedAmountGreaterThan(autoPostPatientId, unappliedAmount);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testGetByAutoPostIdAndUnappliedAmountGreaterThan() {
        // Setup
        final Long autoPostId = Fake.ID;
        final BigDecimal unappliedAmount = new BigDecimal("0.00");
        final List<Payment> expectedResult = Arrays.asList();
        when(mockPaymentRepository.getByAutoPostPatient_AutoPostIdAndUnappliedAmountGreaterThan(Fake.ID, new BigDecimal("0.00"))).thenReturn(Arrays.asList());

        // Run the test
        final List<Payment> result = paymentServiceUnderTest.getByAutoPostIdAndUnappliedAmountGreaterThan(autoPostId, unappliedAmount);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testFindByInsuranceCompanyId() {
        // Setup
        final Long insuranceCompanyId = Fake.ID;
        final List<Payment> expectedResult = Arrays.asList();
        when(mockPaymentRepository.findByInsuranceCompanyId(Fake.ID)).thenReturn(Arrays.asList());

        // Run the test
        final List<Payment> result = paymentServiceUnderTest.findByInsuranceCompanyId(insuranceCompanyId);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testFindByPatientId() {
        // Setup
        final Long patientId = Fake.ID;
        final List<Payment> expectedResult = Arrays.asList();
        when(mockPaymentRepository.findByPatientId(Fake.ID)).thenReturn(Arrays.asList());

        // Run the test
        final List<Payment> result = paymentServiceUnderTest.findByPatientId(patientId);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testLoadAbleToApply() {
        // Setup
        final Long patientId = Fake.ID;
        final Long paymentId = Fake.ID;

        final List<Payment> expectedResult = Arrays.asList();
        Payment payment = new Payment();
        payment.setId(Fake.ID);
        when(mockPaymentRepository.findByPatientIdAndUnappliedAmountGreaterThan(Fake.ID, new BigDecimal("0.00"))).thenReturn(Arrays.asList(payment));

        // Run the test
        final List<Payment> result = paymentServiceUnderTest.loadAbleToApply(patientId, paymentId);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testPercentCollectedByMonth() {
        // Setup
        final Long branchId = Fake.ID;
        final Long numberMonths = Fake.ID;
        ClaimSubmission claimSubmission = new ClaimSubmission();
        claimSubmission.setId(Fake.ID);
        Claim claim = new Claim();
        claim.setId(Fake.ID);
        claim.setPrescriptionId(Fake.ID);
        claim.setTotalClaimAmount(BigDecimal.TEN);
        claimSubmission.setClaim(claim);
        claimSubmission.setClaimId(Fake.ID);
        when(mockClaimSubmissionService.getUniqueByClaimId(any(), any(Date.class), any(Date.class))).thenReturn(Arrays.asList(claimSubmission));
        AppliedPayment appliedPayment = new AppliedPayment();
        appliedPayment.setId(Fake.ID);
        appliedPayment.setAmountApplied(BigDecimal.TEN);
        when(mockAppliedPaymentRepository.findByClaimId(any())).thenReturn(Arrays.asList(appliedPayment));

        // Run the test
        final Map<String, List<Object>> result = paymentServiceUnderTest.percentCollectedByMonth(branchId, numberMonths);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        int expectedMonths = calendar.get(Calendar.MONTH);

        // Verify the results
        assertEquals(1, result.get("months").size());
        assertEquals(expectedMonths, result.get("months").get(0));  //Expected value needs to be dynamic, based on current month
        assertEquals(1, result.get("positions").size());
        assertEquals(0, result.get("positions").get(0));
        assertEquals(1, result.get("percentCollected").size());
        assertEquals("1.0E+2", result.get("percentCollected").get(0).toString());
    }

    @Test
    void testMonthlyBillingsByPractitioner() {
        // Setup
        final Long branchId = Fake.ID;
        final java.util.Date startDate = new GregorianCalendar(2017, 1, 1, 0, 0, 0).getTime();
        final java.util.Date endDate = new GregorianCalendar(2017, 1, 1, 0, 0, 0).getTime();
        final String lCodeDisplay = "lCodes";
        final L_Code lCode = new L_Code();
        PractitionerBillingTotalDTO pbtDTO = new PractitionerBillingTotalDTO();
        pbtDTO.setAllowableTotal(BigDecimal.TEN);
        pbtDTO.setBillingTotal(BigDecimal.TEN);
        final List<PractitionerBillingTotalDTO> expectedResult = Collections.singletonList(pbtDTO);
        Object[] objectArray = new Object[]{Fake.ID, Fake.ID, BigDecimal.TEN, BigDecimal.TEN,};
        when(mockInsuranceVerificationLCodeService.getPractitionerBillingTotalsBySubmissionDateBetweenAndBranchId(new GregorianCalendar(2017, 1, 1, 0, 0, 0).getTime(), new GregorianCalendar(2017, 1, 1, 0, 0, 0).getTime(), Fake.ID, true)).thenReturn(Collections.singletonList(objectArray));
        when(mockInsuranceVerificationLCodeService.getPractitionerBillingTotalsCategoriesBySubmissionDateBetweenAndBranchId(new GregorianCalendar(2017, 1, 1, 0, 0, 0).getTime(), new GregorianCalendar(2017, 1, 1, 0, 0, 0).getTime(), Fake.getBranch().getId())).thenReturn(Collections.singletonList(objectArray));
        User user = new User();
        user.setId(Fake.ID);
        user.setFirstName("F");
        user.setLastName("L");
        when(mockUserService.getUserById(eq(Fake.ID))).thenReturn(user);

        // Run the test
        final List<PractitionerBillingResultsDTO> result = paymentServiceUnderTest.monthlyBillingsByPractitioner(branchId, startDate, endDate, lCodeDisplay, "true");

        // Verify the results
        assertEquals(BigDecimal.TEN, result.get(0).getOverallAllowableTotal());
        assertEquals(BigDecimal.TEN, result.get(0).getOverallAllowableTotal());
        assertEquals(Fake.ID, result.get(0).getPractitioner().getId());
        assertEquals(expectedResult, result.get(0).getPractitionerBillingTotalDTOs());
    }

    @Test
    void testBillingCollectionsByMonth() {
        // Setup
        final Long branchId = Fake.ID;
        final Long numberMonths = Fake.ID;
        ClaimSubmission claimSubmission = new ClaimSubmission();
        claimSubmission.setId(Fake.ID);
        Claim claim = new Claim();
        claim.setId(Fake.ID);
        claim.setPrescriptionId(Fake.ID);
        claim.setTotalClaimAmount(BigDecimal.TEN);
        claimSubmission.setClaim(claim);
        claimSubmission.setClaimId(Fake.ID);
        when(mockClaimSubmissionService.getUniqueByClaimId(any(), any(Date.class), any(Date.class))).thenReturn(Arrays.asList(claimSubmission));
        AppliedPayment appliedPayment = new AppliedPayment();
        appliedPayment.setId(Fake.ID);
        appliedPayment.setAmountApplied(BigDecimal.TEN);
        when(mockAppliedPaymentRepository.findByClaimId(Fake.ID)).thenReturn(Arrays.asList(appliedPayment));

        // Run the test
        final Map<String, List<Object>> result = paymentServiceUnderTest.billingCollectionsByMonth(branchId, numberMonths);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        int expectedMonths = calendar.get(Calendar.MONTH);

        // Verify the results
        assertEquals(1, result.get("months").size());
        assertEquals(expectedMonths, result.get("months").get(0));  //Expected value needs to be dynamic, based on current month
        assertEquals(1, result.get("positions").size());
        assertEquals(0, result.get("positions").get(0));
        assertEquals(1, result.get("billings").size());
        assertEquals(10.0, result.get("billings").get(0));
        assertEquals(1, result.get("collected").size());
        assertEquals(10.0, result.get("collected").get(0));
    }

    @Test
    void testBillingCollectionsByYear() {
        // Setup
        final Long branchId = Fake.ID;
        final Long startYear = Fake.ID;
        final Long endYear = Fake.ID;
        ClaimSubmission claimSubmission = new ClaimSubmission();
        claimSubmission.setId(Fake.ID);
        Claim claim = new Claim();
        claim.setId(Fake.ID);
        claim.setPrescriptionId(Fake.ID);
        claim.setTotalClaimAmount(BigDecimal.TEN);
        claimSubmission.setClaim(claim);
        claimSubmission.setClaimId(Fake.ID);
        when(mockClaimSubmissionService.getUniqueByClaimId(any(), any(Date.class), any(Date.class))).thenReturn(Arrays.asList(claimSubmission));
        Payment payment = new Payment();
        payment.setId(Fake.ID);
        payment.setClaimId(Fake.ID);
        when(mockPaymentRepository.findByClaimId(Fake.ID)).thenReturn(Arrays.asList(payment));

        // Run the test
        final Map<String, List<Object>> result = paymentServiceUnderTest.billingCollectionsByYear(branchId, startYear, endYear);

        // Verify the results

        assertEquals(1, result.get("years").size());
        assertEquals(1, result.get("years").get(0));
        assertEquals(1, result.get("billings").size());
        assertEquals(10.0, result.get("billings").get(0));
        assertEquals(1, result.get("collected").size());
        assertEquals(0.0, result.get("collected").get(0));
    }

    @Test
    void testFindByClaimId() {
        // Setup
        final List<Payment> payments = Fake.getPaymentList();
        when(mockPaymentRepository.findByClaimId(Fake.ID)).thenReturn(payments);
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final List<Payment> result = paymentServiceUnderTest.findByClaimId(Fake.ID);

        // Verify the results
        assertEquals(Fake.ID, result.get(0).getId());
        assertEquals(Fake.ID, result.get(0).getCreatedBy().getId());
    }

    @Test
    void testPopulateClaimAppliedPayments() {
        // Setup
        final ClaimSubmission cs = Fake.getClaimSubmission();
        when(mockAppliedPaymentService.getByClaimId(any())).thenReturn(Fake.getAppliedPaymentList());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final List<Payment> result = paymentServiceUnderTest.populateClaimAppliedPayments(cs);

        // Verify the results
        assertEquals("insurance_payment_credit_card", result.get(0).getPaymentType());
        assertEquals("insurance_company", result.get(0).getPayerType());
        assertEquals(Fake.ID, result.get(0).getCreatedBy().getId());
    }

    @Test
    void testGetClaimSubmissions() {
        // Setup
        final Date startDate = new Date(Fake.ID);
        final Date endDate = new Date(Fake.ID);
        final Long branchId = Fake.ID;
        final String deviceType = "deviceType";
        final List<ClaimSubmission> expectedResult = Arrays.asList();
        when(mockClaimSubmissionService.findBySubmissionDateBetween(new Date(Fake.ID), new Date(Fake.ID), Fake.ID, "deviceType")).thenReturn(Arrays.asList());

        // Run the test
        final List<ClaimSubmission> result = paymentServiceUnderTest.getClaimSubmissions(startDate, endDate, branchId, deviceType);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testPopulateUnappliedPayments() {
        // Setup
        final Date startDate = new Date(Fake.ID);
        final Date endDate = new Date(Fake.ID);
        final Long branchId = Fake.ID;
        final String deviceType = "deviceType";
        final List<Payment> expectedResult = Arrays.asList();
        when(mockPaymentRepository.findUnappliedPatientPaymentsByBranchId(any(), any(), any())).thenReturn(Arrays.asList());
        when(mockPaymentRepository.findUnappliedPatientPayments(any(), any())).thenReturn(Arrays.asList());
        when(mockPaymentRepository.findUnappliedPatientPaymentsByDeviceType(any(), any(), any())).thenReturn(Arrays.asList());
        when(mockPaymentRepository.findUnappliedPatientPaymentsByBranchIdAndDeviceType(any(), any(), any(), any())).thenReturn(Arrays.asList());

        // Run the test
        final List<Payment> result = paymentServiceUnderTest.populateUnappliedPayments(startDate, endDate, branchId, deviceType);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    void testBillingsByLCode() {
        // Setup

        final Date startDate = new Date(Fake.ID);
        final Date endDate = new Date(Fake.ID);
        Object[] lCodeBilling = new Object[]{0, 1, 2, 3};
        List<Object[]> list = new ArrayList<>();
        list.add(lCodeBilling);
        when(mockClaimSubmissionRepository.getLCodeBillingTotalsBySubmissionDateBetween(any(java.util.Date.class), any(java.util.Date.class))).thenReturn(list);

        // Run the test
        final List<L_CodeBillingDTO> result = paymentServiceUnderTest.billingsByLCode(startDate, endDate);

        // Verify the results
        assertEquals("0", result.get(0).getLCodeName());
        assertEquals(Integer.valueOf(1), result.get(0).getQuantity());
        assertEquals(BigDecimal.valueOf(Double.valueOf("3.0")), result.get(0).getBillingTotal());
        assertEquals(BigDecimal.valueOf(Double.valueOf("2.0")), result.get(0).getLCodeTotal());
    }

    @Test
    void testDailyClose() {
        // Setup
        final String payeeType = "%";
        final String dateOption = "payment";
        final Date startDate = new Date(Fake.ID);
        final Date endDate = new Date(Fake.ID);
        final Long branchId = Fake.ID;
        final DailyCloseReportDTO expectedResult = new DailyCloseReportDTO();
        Branch branch = new Branch();
        branch.setId(Fake.ID);
        Adjustment adjustment = new Adjustment();
        adjustment.setId((long) 9);
        adjustment.setName("No Authorization");
        adjustment.setOperation("+");
        when(mockBranchRepository.findAll()).thenReturn(List.of(Fake.getBranch()));

        when(mockPaymentRepository.getAllDailyClosePaymentsForReport(anyString(), anyString(), eq("payment"), anyString(), anyList())).thenReturn(List.of(Fake.createDCPayment()));
        when(mockPaymentRepository.getAllDailyClosePaymentsForReport(anyString(), anyString(), eq("deposit"), anyString(), anyList())).thenReturn(List.of(Fake.createDCPayment()));
        when(mockAppliedPaymentRepository.getBranchesBulkPaymentAppliedPaymentsByDateOption(anyList(), any(Date.class), any(Date.class), eq("payment"))).thenReturn(Collections.singletonList(Fake.createAppliedDCPayment()));
        when(mockAppliedPaymentRepository.getBranchesBulkPaymentAppliedPaymentsByDateOption(anyList(), any(Date.class), any(Date.class), eq("deposit"))).thenReturn(Collections.singletonList(Fake.createAppliedDCPayment()));
        when(mockAdjustmentService.findById(any(Long.class))).thenReturn(adjustment);

        // Run the test
        final DailyCloseReportDTO result = paymentServiceUnderTest.dailyClose(payeeType, dateOption, startDate, endDate, branchId);

        // Verify the results

        assertEquals(BigDecimal.valueOf(202.22), result.getReportCredit());
        assertEquals(BigDecimal.valueOf(202.22), result.getReportApplied());
        assertEquals(BigDecimal.valueOf(202.22), result.getReportTotalDeposited());
    }

    @Test
    void testGetPractitionerCommissions() {
        // Setup
        final Date startDate = new Date(Fake.ID);
        final Date endDate = new Date(Fake.ID);
        final Long branchId = 1L;
        final String dateOption = "payment";
        final String deviceType = "%";
        when(mockAppliedPaymentLCodeRepository.getAllAppliedPaymentLCodesByDateBetweenAndBranchAndDeviceType(startDate, endDate, branchId, dateOption, deviceType)).thenReturn(Fake.getAppliedPayment_L_CodeList());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        List<Long> adjustments = new ArrayList<>();
        adjustments.add(Fake.ID);
        List<PractitionerCommissionsDTO> result = paymentServiceUnderTest.getPractitionerCommissions(startDate, endDate, branchId, dateOption, deviceType);

        // Verify the results
        assertEquals(BigDecimal.TEN, result.get(0).getTotalPayments());
        assertEquals(BigDecimal.TEN, result.get(0).getInsurancePayments());
        assertEquals(BigDecimal.ZERO, result.get(0).getInsuranceRefunds());
        assertEquals(BigDecimal.TEN, result.get(0).getTotalInsurancePaid());
        assertEquals(BigDecimal.ZERO, result.get(0).getPatientPayments());
        assertEquals(BigDecimal.ZERO, result.get(0).getPatientRefunds());
        assertEquals(BigDecimal.ZERO, result.get(0).getTotalPatientPaid());
    }

//    @Test
    void testGetPaymentsForCashSummary() {
        // Setup
        when(mockPaymentRepository.getPaymentsForCashSummary(any(), any(), any(), any(), any())).thenReturn(Fake.getPaymentList());
        when(mockUserService.findOne(Fake.ID)).thenReturn(Fake.getUserFakeID());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final List<Payment> result = paymentServiceUnderTest.getPaymentsForCashSummary(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), "dateOption", "deviceType", 0L);

        // Verify the results
        assertEquals(1, result.size());
        assertEquals(Fake.ID, result.get(0).getCreatedBy().getId());
    }

    //    @Test
    void testGetPaymentsByBranchAndDate() {
        // Setup
        when(mockPaymentRepository.getPaymentsByBranchAndDate(any(), any(), any(), any())).thenReturn(Fake.getPaymentList());
        when(mockUserService.findOne(Fake.ID)).thenReturn(Fake.getUserFakeID());
        when(mockUserService.getUserById(Fake.ID)).thenReturn(Fake.getUserFakeID());

        // Run the test
        final List<Payment> result = paymentServiceUnderTest.getPaymentsByBranchAndDate(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), "dateOption", 0L);

        // Verify the results
        assertEquals(1, result.size());
    }

    @Test
    void testGetAppliedAndUnappliedPaymentTotals() {
        // Setup
        Object[] objects = new Object[]{25, 25, 25, 25, 25, 25, 25, 25, 25, 25};
        List<Object[]> list = new ArrayList<>();
        list.add(objects);
        when(mockPaymentRepository.getAppliedAndUnappliedPaymentTotals(any(), any())).thenReturn(list);

        // Run the test
        final List<Object[]> result = paymentServiceUnderTest.getAppliedAndUnappliedPaymentTotals(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertEquals(1, result.size());
        assertEquals(25, result.get(0)[0]);
    }

    @Test
    void testGetSumOfPaymentsByDateAndBranch() {
        // Setup

        when(mockPaymentRepository.getSumOfPaymentsByDateAndBranch(any(), any(), any(), any())).thenReturn(new BigDecimal("0.00"));

        // Run the test
        BigDecimal result = paymentServiceUnderTest.getSumOfPaymentsByDateAndBranch(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), "dateOption", 0L);

        // Verify the results
        assertEquals(new BigDecimal("0.00"), result);

        when(mockPaymentRepository.getSumOfPaymentsByDateAndBranch(any(), any(), any(), any())).thenReturn(BigDecimal.TEN);
        result = paymentServiceUnderTest.getSumOfPaymentsByDateAndBranch(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), "dateOption", 0L);

        // Verify the results
        assertEquals(BigDecimal.TEN, result);
    }

    @Test
    void testGetLineAdjustmentTotalsByDate() {
        // Setup
        Object[] objects = new Object[]{10};
        List<Object[]> list = new ArrayList<>();
        list.add(objects);
        when(mockPaymentRepository.getLineAdjustmentTotalsByDate(any(), any(), any())).thenReturn(list);

        // Run the test
        final List<Object[]> result = paymentServiceUnderTest.getLineAdjustmentTotalsByDate(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), "dateOption");

        // Verify the results
        assertEquals(10, result.get(0)[0]);
    }

    @Test
    void testGetARAdjustmentTotalsByDate() {
        // Setup
        Object[] objects = new Object[]{10};
        List<Object[]> list = new ArrayList<>();
        list.add(objects);
        when(mockPaymentRepository.getARAdjustmentTotalsByDate(any(), any(), any())).thenReturn(list);

        // Run the test
        final List<Object[]> result = paymentServiceUnderTest.getARAdjustmentTotalsByDate(new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), new GregorianCalendar(2019, Calendar.JANUARY, 1).getTime(), "dateOption");

        // Verify the results
        assertEquals(10, result.get(0)[0]);
    }
}
