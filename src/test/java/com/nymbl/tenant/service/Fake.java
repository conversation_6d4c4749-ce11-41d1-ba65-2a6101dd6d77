package com.nymbl.tenant.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nymbl.config.dto.DailyCloseReport.DCPayment;
import com.nymbl.config.dto.L_CodeDTO;
import com.nymbl.config.dto.PatientInsuranceDTO;
import com.nymbl.config.dto.StatementDTO;
import com.nymbl.config.enums.InsuranceType;
import com.nymbl.config.enums.PriceOption;
import com.nymbl.config.enums.form1500.*;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.master.model.*;
import com.nymbl.tenant.interfaces.IInsuranceCompany;
import com.nymbl.tenant.interfaces.IInsuranceVerification;
import com.nymbl.tenant.interfaces.IInsuranceVerification_L_Code;
import com.nymbl.tenant.interfaces.IPrescription_L_Code;
import com.nymbl.tenant.model.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.data.projection.SpelAwareProxyProjectionFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;

public class Fake<T> {
    public static final Long ID = 1L;
    public static final Long ID2 = 2L;
    public static final Long ID3 = 3L;
    /**
     * patientId
     */
    public static final Long ID4 = 4L;

    public static final Long NEW_CLAIM_ID = 11L;
    public static final Long POST_TO_PT_RESPONSIBILITY_ID = 19L;
    public static final Long QUEUED_ID = 21L;
    public static final Long RECENTLY_POSTED_ID = 24L;
    public static final Long SENT_TO_NEXT_PAYER_ID = 29L;

    public static User getUserFakeID() {
        return getUser(Fake.ID);
    }

    public static User getUserFakeID2() {
        return getUser(Fake.ID2);
    }

    public static User getUserFakeID3() {
        return getUser(Fake.ID3);
    }

    public static User getUser(Long id) {
        User user = new User();
        user.setId(id);
        user.setUsername("flName");
        user.setPassword("password");
        user.setFirstName("firstName");
        user.setLastName("lastName");
        user.setEmail("<EMAIL>");
        user.setIsMultiUser(false);
        user.setIsQuicksightUser(false);
        Privilege privilege = new Privilege();
        privilege.setId(Fake.ID);
        privilege.setName("privilegeName");
        Role role = new Role();
        role.setId(Fake.ID);
        role.setName("roleName");

        // SCRUM-3888: Deleted...necessary?
        //role.setUsers(Collections.singleton(user));

        Privilege privilege1 = new Privilege();
        privilege1.setId(Fake.ID2);
        privilege1.setName("privilege1Name");

        // SCRUM-3888: Deleted...necessary?
        //role.setPrivileges(Collections.singleton(privilege1));

        // SCRUM-3888: Deleted...necessary?
        //privilege.setRoles(Collections.singleton(role));

        //SCRUM-3888: UNECESSARY
        //Set<Role> roleList = Collections.emptySet();
        //Set<Privilege> privilegeList = Collections.emptySet();
        //user.setPrivileges(privilegeList);
        //user.setRoles(roleList);
        user.setCompany(getCompany());
        user.setCompanyId(Fake.ID);
        return user;
    }

    public static java.sql.Date getSqlDate(String yyyy_MM_dd) {
        return java.sql.Date.valueOf(yyyy_MM_dd);
    }

    public static Claim getClaim() {
        Claim claim = new Claim();
        claim.setId(Fake.ID);
        claim.setUserId(Fake.ID);
        claim.setPatientInsurance(getPatientInsurance());
        claim.setPatientInsuranceId(claim.getPatientInsurance().getId());
        claim.setResponsiblePatientInsurance(getPatientInsurance());
        claim.setResponsiblePatientInsuranceId(claim.getPatientInsurance().getId());
        claim.setTotalClaimBalance(BigDecimal.TEN);
        claim.setPrescription(Fake.getPrescription());
        claim.setPrescriptionId(claim.getPrescription().getId());
        claim.setCreatedAt(new Date(Calendar.getInstance().getTime().getTime()));
        claim.setCreatedById(Fake.ID);
        claim.setUpdatedAt(new Date(Calendar.getInstance().getTime().getTime()));
        claim.setUpdatedById(Fake.ID);
        claim.setNymblStatusId(Fake.NEW_CLAIM_ID);
        claim.setNymblStatus(Fake.getNymblStatus());
        return claim;
    }

    public static NymblStatus getNymblStatus() {
        NymblStatus nymblStatus = new NymblStatus();
        nymblStatus.setId(1L);
        nymblStatus.setIsCustom(false);
        nymblStatus.setKey("new_claim");
        nymblStatus.setName("New Claim");
        nymblStatus.setType("claim");
        return nymblStatus;
    }

    public static List<Claim> getClaimList() {
        List<Claim> claimList = new ArrayList<>();
        claimList.add(getClaim());
        return claimList;
    }

    public static ClaimSubmission getClaimSubmission() {
        ClaimSubmission claimSubmission = new ClaimSubmission();
        claimSubmission.setId(Fake.ID);
        claimSubmission.setClaimId(Fake.getClaim().getId());
        claimSubmission.setClaim(Fake.getClaim());
        claimSubmission.setPatientInsurance(Fake.getPatientInsurance());
        claimSubmission.setSubmittedBy(Fake.getUserFakeID());
        claimSubmission.setSubmittedById(Fake.ID);
        return claimSubmission;
    }

    public static List<Object[]> getClaimSubmissionObjectArrayList() {
        List<Object[]> claimSubmissionList = new ArrayList<>();
        Object[] objects = new Object[]{Fake.getClaimSubmission()};
        claimSubmissionList.add(objects);
        return claimSubmissionList;
    }

    public static List<ClaimSubmission> getClaimSubmissionList() {
        List<ClaimSubmission> claimSubmissionList = new ArrayList<>();
        claimSubmissionList.add(Fake.getClaimSubmission());
        return claimSubmissionList;
    }

    public static List<InsuranceVerification> getInsuranceVerificationsOtherSelfPay() {
        InsuranceVerification insuranceVerification0 = new InsuranceVerification();
        insuranceVerification0.setId(Long.parseLong("12638"));
        insuranceVerification0.setPrescriptionId(Long.parseLong("11621"));
        insuranceVerification0.setPatientInsuranceId(Long.parseLong("11225"));
        insuranceVerification0.setCarrierType("other");
        insuranceVerification0.setDeductibleAmount(BigDecimal.valueOf(Long.parseLong("0")));
        insuranceVerification0.setAmountMet(BigDecimal.valueOf(Long.parseLong("0")));
        insuranceVerification0.setBenefitsPayable("0/100");
        insuranceVerification0.setOutOfPocketAmount(BigDecimal.valueOf(Long.parseLong("0")));
        insuranceVerification0.setOutOfPocketRemaining(BigDecimal.valueOf(Long.parseLong("0")));
        insuranceVerification0.setRemainingAnnualLimit(BigDecimal.valueOf(Long.parseLong("0")));
        insuranceVerification0.setVerifiedById(Fake.ID);
        List<InsuranceVerification> insuranceVerificationList = new ArrayList<>();
        insuranceVerificationList.add(insuranceVerification0);
        return insuranceVerificationList;
    }

    public static IInsuranceCompany getIInsuranceCompany() {
        return new IInsuranceCompany() {
            @Override
            public Long getId() {
                return 1L;
            }

            @Override
            public String getName() {
                return "WellMed-United Healthcare";
            }
        };
    }

    public static L_CodeFee getLCodeFee() {
        L_CodeFee lCodeFee = new L_CodeFee();
        lCodeFee.setId(Fake.ID);
        lCodeFee.setFee(BigDecimal.TEN);
        lCodeFee.setRuralFee(BigDecimal.TEN);
        lCodeFee.setPriceOption(PriceOption.CUSTOM);
        lCodeFee.setFeeSchedule(getFeeSchedule());
        lCodeFee.setFeeScheduleId(getFeeSchedule().getId());
        return lCodeFee;
    }

    public static List<InsuranceVerification_L_Code> getInsuranceVerification_l_codeList() {
        List<InsuranceVerification_L_Code> insuranceVerificationLCodeList = new ArrayList<>();
        insuranceVerificationLCodeList.add(getInsuranceVerification_l_code());
        return insuranceVerificationLCodeList;
    }

    public static List<IInsuranceVerification_L_Code> getIInsuranceVerification_l_codeList() {
        List<IInsuranceVerification_L_Code> insuranceVerificationLCodeList = new ArrayList<>();
        insuranceVerificationLCodeList.add(getIInsuranceVerification_l_code());
        return insuranceVerificationLCodeList;
    }

    public static IInsuranceVerification_L_Code getIInsuranceVerification_l_code() {
        IInsuranceVerification_L_Code insuranceVerification_L_Code0 = new IInsuranceVerification_L_Code() {
            @Override
            public Long getId() {
                return 220L;
            }

            @Override
            public BigDecimal getAllowableFee() {
                return new BigDecimal("54.91");
            }

            @Override
            public BigDecimal getBillingFee() {
                return new BigDecimal("54.91");
            }

            @Override
            public BigDecimal getTotalAllowable() {
                return new BigDecimal("109.82");
            }

            @Override
            public BigDecimal getTotalCharge() {
                return new BigDecimal("109.82");
            }

            @Override
            public Boolean getCovered() {
                return null;
            }

            @Override
            public Boolean getBill() {
                return null;
            }

            @Override
            public Boolean getUseSalesTax() {
                return null;
            }

            @Override
            public BigDecimal getSalesTax() {
                return null;
            }

            @Override
            public Date getStartDate() {
                return null;
            }

            @Override
            public Date getExpirationDate() {
                return null;
            }

            @Override
            public String getAuthNumber() {
                return null;
            }

            @Override
            public Long getInsuranceVerificationId() {
                return null;
            }

            @Override
            public Long getPrescription_l_codeId() {
                return null;
            }
        };
        return insuranceVerification_L_Code0;
    }

    public static InsuranceVerification_L_Code getInsuranceVerification_l_code() {
        InsuranceVerification_L_Code insuranceVerification_L_Code0 = new InsuranceVerification_L_Code();
        insuranceVerification_L_Code0.setId(Long.parseLong("220"));
        insuranceVerification_L_Code0.setTotalCharge(BigDecimal.valueOf(Double.valueOf("109.82")));
        insuranceVerification_L_Code0.setPrescriptionLCodeId(Long.parseLong("220"));
        insuranceVerification_L_Code0.setPrescriptionLCode(getPrescription_l_code());
        insuranceVerification_L_Code0.setBillingFee(BigDecimal.valueOf(Double.valueOf("54.91")));
        insuranceVerification_L_Code0.setInsuranceVerificationId(Long.parseLong("97"));
        insuranceVerification_L_Code0.setTotalAllowable(BigDecimal.valueOf(Double.valueOf("109.82")));
        insuranceVerification_L_Code0.setCovered(true);
        insuranceVerification_L_Code0.setBill(true);
        insuranceVerification_L_Code0.setAllowableFee(BigDecimal.valueOf(Double.valueOf("54.91")));
        insuranceVerification_L_Code0.setUseSalesTax(false);
        insuranceVerification_L_Code0.setInsuranceVerification(getInsuranceVerification());
        insuranceVerification_L_Code0.setInsuranceVerificationId(getInsuranceVerification().getId());
        return insuranceVerification_L_Code0;
    }

    public static PrescriptionDiagnosisCode getPrescriptionDiagnosisCode() {
        PrescriptionDiagnosisCode prescriptionDiagnosisCode = new PrescriptionDiagnosisCode();
        prescriptionDiagnosisCode.setId(Fake.ID);
        prescriptionDiagnosisCode.setPrescriptionId(Fake.getPrescription().getId());
        prescriptionDiagnosisCode.setPrescription(Fake.getPrescription());
        prescriptionDiagnosisCode.setDiagnosisCode(Fake.getDiagnosisCode());
        prescriptionDiagnosisCode.setDiagnosisCodeId(Fake.getDiagnosisCode().getId());
        return prescriptionDiagnosisCode;
    }

    public static List<PrescriptionDiagnosisCode> getPrescriptionDiagnosisCodeList() {
        List<PrescriptionDiagnosisCode> prescriptionDiagnosisCodeList = new ArrayList<>();
        prescriptionDiagnosisCodeList.add(Fake.getPrescriptionDiagnosisCode());
        return prescriptionDiagnosisCodeList;
    }

    public static DiagnosisCode getDiagnosisCode() {
        DiagnosisCode diagnosisCode10 = new DiagnosisCode();
        diagnosisCode10.setId(Fake.ID);
        diagnosisCode10.setName("TYPE 2 DIABETES MELLITUS WITH DIABETIC POLYNEUROPATHY");
        diagnosisCode10.setId(Long.parseLong("5044"));
        diagnosisCode10.setCodeSet("icd-10");
        diagnosisCode10.setActive(true);
        diagnosisCode10.setCode("E11.42");
        return diagnosisCode10;
    }

    public static DeliveryLocation getDeliveryLocation() {
        DeliveryLocation deliveryLocation = new DeliveryLocation();
        deliveryLocation.setId(Fake.ID);
        deliveryLocation.setName("Test Delivery Location");
        deliveryLocation.setStreetAddress("101 Nymbl Way");
        deliveryLocation.setCity("Lexington");
        deliveryLocation.setState("KY");
        deliveryLocation.setZipcode("40513");
        deliveryLocation.setNpi("npi");
        return deliveryLocation;
    }

    public static L_CodeCategory getL_codeCategory() {
        L_CodeCategory lCodeCategory0 = new L_CodeCategory();
        lCodeCategory0.setId(Long.parseLong("2"));
        lCodeCategory0.setClassification("main_type");
        lCodeCategory0.setCategory("Orthotics");
        lCodeCategory0.setParent(lCodeCategory0);
        lCodeCategory0.setId(Long.parseLong("7"));
        lCodeCategory0.setParentId(Long.parseLong("2"));
        lCodeCategory0.setClassification("sub_type");
        lCodeCategory0.setCategory("Footwear");
        lCodeCategory0.setParent(lCodeCategory0);
        lCodeCategory0.setId(Long.parseLong("39"));
        lCodeCategory0.setParentId(Long.parseLong("7"));
        lCodeCategory0.setClassification("category");
        lCodeCategory0.setCategory("Footwear");
        return lCodeCategory0;
    }

    public static L_Code getL_codeA5500() {
        L_Code lCode0 = new L_Code();
        lCode0.setId(Fake.ID);
        lCode0.setName("A5500");
        lCode0.setId(Long.parseLong("4956"));
        lCode0.setCreatedAt(new java.sql.Timestamp(1451858400000L));
        lCode0.setLCodeCategoryId(Long.parseLong("262"));
        lCode0.setTaxable(false);
        lCode0.setFriendlyDescription("DIAB SHOE FOR DENSITY INSERT");
        lCode0.setUpdatedAt(new java.sql.Timestamp(1520873970000L));
        lCode0.setActive(true);
        lCode0.setDescription("FOR DIABETICS ONLY, FITTING (INCLUDING FOLLOW-UP), CUSTOM PREPARATION AND SUPPLY OF OFF-THE-SHELF DEPTH-INLAY SHOE MANUFACTURED TO ACCOMMODATE MULTI- DENSITY INSERT(S), PER SHOE");
        lCode0.setVariable(false);
        lCode0.setAlert(false);
        lCode0.setLCodeCategory(getL_codeCategory());
        lCode0.setSurveyLink("lCode survey link");
        return lCode0;
    }

    public static List<Prescription_L_Code> getPrescription_l_codeList() {
        List<Prescription_L_Code> prescriptionLCodeList = new ArrayList<>();
        prescriptionLCodeList.add(getPrescription_l_code());
        return prescriptionLCodeList;
    }

    public static List<IPrescription_L_Code> getIPrescription_l_codeList() {
        List<IPrescription_L_Code> prescriptionLCodeList = new ArrayList<>();
        prescriptionLCodeList.add(getIPrescription_l_code());
        return prescriptionLCodeList;
    }

    public static IPrescription_L_Code getIPrescription_l_code() {

        return new IPrescription_L_Code() {
            @Override
            public Long getId() {
                return 220L;
            }

            @Override
            public String getAbnReason() {
                return null;
            }

            @Override
            public String getAbnDeviceType() {
                return null;
            }

            @Override
            public String getModifier1() {
                return null;
            }

            @Override
            public String getModifier2() {
                return null;
            }

            @Override
            public String getModifier3() {
                return null;
            }

            @Override
            public String getModifier4() {
                return null;
            }

            @Override
            public Long getOrderNum() {
                return null;
            }

            @Override
            public String getLCodeJustification() {
                return null;
            }

            @Override
            public String getDetailedJustification() {
                return null;
            }

            @Override
            public Long getQuantity() {
                return null;
            }

            @Override
            public Date getDateOfService() {
                return null;
            }

            @Override
            public Date getDateOfServiceEnd() {
                return null;
            }

            @Override
            public Long getPlaceOfService() {
                return null;
            }

            @Override
            public Boolean getEmergency() {
                return null;
            }

            @Override
            public String getEspdt() {
                return null;
            }

            @Override
            public Long getPrescriptionId() {
                return 99L;
            }

            @Override
            public Long getLCodeId() {
                return null;
            }

            @Override
            public Long getDiagnosisCode1id() {
                return null;
            }

            @Override
            public Long getDiagnosisCode2id() {
                return null;
            }

            @Override
            public Long getDiagnosisCode3id() {
                return null;
            }

            @Override
            public Long getDiagnosisCode4id() {
                return null;
            }

            @Override
            public Boolean getShowOnDelivery() {
                return null;
            }

            @Override
            public String getRentalStatus() {
                return null;
            }

            @Override
            public String getLCodeName() {
                return "A5500";
            }
        };
    }

    public static Prescription_L_Code getPrescription_l_code() {
        Prescription_L_Code prescriptionLCode0 = new Prescription_L_Code();
        prescriptionLCode0.setId(Long.parseLong("220"));
        prescriptionLCode0.setPrescriptionId(Long.parseLong("99"));
        prescriptionLCode0.setPrescription(getPrescription());
//        prescriptionLCode0.setTotalCharge(BigDecimal.valueOf(Double.valueOf("109.82")));
        prescriptionLCode0.setDateOfService(java.sql.Date.valueOf(LocalDate.of(2019, 10, 29)));
        prescriptionLCode0.setRentalStatus("IN");
        prescriptionLCode0.setPos(PlaceOfService.Home);
        prescriptionLCode0.setEmergency(false);
        prescriptionLCode0.setLCode(getL_codeA5500());
        prescriptionLCode0.setModifier1("LT");
        prescriptionLCode0.setModifier2("RT");
        prescriptionLCode0.setModifier3("KX");
        prescriptionLCode0.setModifier4("");
        prescriptionLCode0.setQuantity(Long.parseLong("2"));
        prescriptionLCode0.setDiagnosisCode1id(Long.parseLong("5044"));
        prescriptionLCode0.setDiagnosisCode1(getDiagnosisCode());
//        prescriptionLCode0.setBillingFee(BigDecimal.valueOf(Double.valueOf("54.91")));
        prescriptionLCode0.setOrderNum(Long.parseLong("1"));
        prescriptionLCode0.setShowOnDelivery(true);
        prescriptionLCode0.setLCodeId(Long.parseLong("4956"));
//        prescriptionLCode0.setInsuranceCovered(true);
//        prescriptionLCode0.setTotalAllowable(BigDecimal.valueOf(Double.valueOf("109.82")));
//        prescriptionLCode0.setAllowableFee(BigDecimal.valueOf(Double.valueOf("54.91")));
//        prescriptionLCode0.setBalance(BigDecimal.valueOf(Double.valueOf("146.42")));
        return prescriptionLCode0;
    }

    public static Prescription getPrescription() {
        Prescription prescription0 = new Prescription();
        prescription0.setBranch(Fake.getBranch());
        prescription0.setId(Long.parseLong("99"));


        prescription0.setPatient(getPatient());
        prescription0.setPatientId(getPatient().getId());

        prescription0.setPatientInsurance(Fake.getPatientInsurance());
        prescription0.setPatientInsuranceId(Fake.getPatientInsurance().getId());

        prescription0.setReferringPhysician(getReferringPhysician());
        prescription0.setReferringPhysicianId(getReferringPhysician().getId());

        prescription0.setPatientInsurance(getPatientInsurance());
        prescription0.setPatientInsuranceId(getPatientInsurance().getId());
        prescription0.setAccidentType("");
        prescription0.setAccidentState("");
        prescription0.setDeliveredOn(new java.sql.Timestamp(1572333118000L));
        prescription0.setTreatingPractitionerId(Fake.ID);
        prescription0.setTreatingPractitioner(Fake.getUserFakeID());
        prescription0.setDeliveryLocation("primary_branch");
        prescription0.setDeliveryLocationAddress("5282 Medical Drive, Suite 105, San Antonio, TX 78229");
        prescription0.setReferringPhysicianId(Long.parseLong("26"));
        prescription0.setPrescriptionDate(java.sql.Date.valueOf(LocalDate.of(2018, 11, 9)));
        prescription0.setKLevel("Unspecified");
        prescription0.setDeviceTypeId(Long.parseLong("17"));
        prescription0.setAdditionalComponentNotes("<p>Anodyne Women's A5512 - 3 Pairs (Med./Wide) from Anodyne Shoes</p>");
        prescription0.setPatientRecall(false);
        prescription0.setChecklistCompleted(false);
        prescription0.setFabricationCompleted(false);
        prescription0.setRecallReason("");
        prescription0.setSignedBy("patient");
        prescription0.setPrescriptionImageFront("");
        prescription0.setPrescriptionImageBack("");
        prescription0.setActive(true);
        prescription0.setArchived(false);
        prescription0.setDeviceType(getDeviceType());
        prescription0.setCreatedById(Long.parseLong("430"));
        prescription0.setSignature("data:image/png;base64,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");
        prescription0.setSignedDate(new java.sql.Date(Long.parseLong("1572321600000")));
        prescription0.setNote("Hello World");
        return prescription0;
    }

    public static List<Prescription> getPrescriptionList() {
        List<Prescription> prescriptionList = new ArrayList<>();
        prescriptionList.add(Fake.getPrescription());
        return prescriptionList;
    }

    public static PrescriptionSection getPrescriptionSection() {
        PrescriptionSection prescriptionSection = new PrescriptionSection();
        prescriptionSection.setId(Fake.ID);
        prescriptionSection.setUserId(Fake.ID);
        prescriptionSection.setSection("l_code_selection");
        return prescriptionSection;
    }

    public static List<PrescriptionSection> getPrescriptionSectionList() {
        List<PrescriptionSection> prescriptionSectionList = new ArrayList<>();
        prescriptionSectionList.add(Fake.getPrescriptionSection());
        return prescriptionSectionList;
    }

    public static DeviceType getDeviceType() {
        DeviceType deviceType0 = new DeviceType();
        deviceType0.setId(Fake.ID);
        deviceType0.setName("DIABETIC SHOES AND INSERTS");
        deviceType0.setId(Long.parseLong("17"));
        deviceType0.setUpperOrLower("lower");
        deviceType0.setLeftOrRight("bilateral");
        deviceType0.setOrthoticOrProsthetic("orthotic");
        deviceType0.setSurvey_link("device type survey link");
        return deviceType0;
    }

    public static Physician getReferringPhysician() {
        Physician referringPhysician0 = new Physician();
        referringPhysician0.setId(Long.parseLong("26"));
        referringPhysician0.setState("UT");
        referringPhysician0.setStreetAddress("1831 S General McMullen Dr");
        referringPhysician0.setCity("San Antonio");
        referringPhysician0.setZipcode("78226-1109");
        referringPhysician0.setFirstName("Fernando");
        referringPhysician0.setLastName("Valdez");
        referringPhysician0.setNpi("**********");
        referringPhysician0.setLicenseNumber("L7630");
        referringPhysician0.setVerifiedOn(new java.sql.Date(Long.parseLong("1543986000000")));
        referringPhysician0.setTaxonomyCode("207Q00000X");
        referringPhysician0.setPecosVerified(true);
        referringPhysician0.setActive(true);
        referringPhysician0.setCredentials("MD");
        return referringPhysician0;
    }

    public static IInsuranceVerification getIInsuranceVerification() {
        return new IInsuranceVerification() {

            @Override
            public Long getId() {
                return 97L;
            }

            @Override
            public String getCarrierType() {
                return InsuranceType.primary.name();
            }

            @Override
            public Boolean getVerified() {
                return true;
            }

            @Override
            public Date getVerifiedOn() {
                return new java.sql.Date(Long.parseLong("1550034000000"));
            }

            @Override
            public Long getVerifiedById() {
                return 430L;
            }

            @Override
            public Boolean getCurrentCoverage() {
                return true;
            }

            @Override
            public Date getEffectiveDate() {
                return new java.sql.Date(Long.parseLong("1546318800000"));
            }

            @Override
            public Date getTerminationDate() {
                return null;
            }

            @Override
            public String getPreAuthPhone() {
                return null;
            }

            @Override
            public Boolean getPtResponsibleForDeductible() {
                return null;
            }

            @Override
            public Boolean getPtResponsibleForCoIns() {
                return true;
            }

            @Override
            public Boolean getPtResponsibleForCoPay() {
                return null;
            }

            @Override
            public Boolean getPtResponsibleForOther() {
                return null;
            }

            @Override
            public BigDecimal getDeductibleAmount() {
                return BigDecimal.ZERO;
            }

            @Override
            public String getPtOtherResponsibility() {
                return null;
            }

            @Override
            public String getNetworkType() {
                return null;
            }

            @Override
            public String getBenefitsPayable() {
                return "80/20";
            }

            @Override
            public BigDecimal getAmountMet() {
                return BigDecimal.ZERO;
            }

            @Override
            public Boolean getOopActive() {
                return null;
            }

            @Override
            public BigDecimal getOutOfPocketAmount() {
                return BigDecimal.ZERO;
            }

            @Override
            public BigDecimal getOutOfPocketRemaining() {
                return BigDecimal.ZERO;
            }

            @Override
            public Boolean getOopIncludeDeductible() {
                return null;
            }

            @Override
            public Boolean getOopIncludeCoIns() {
                return null;
            }

            @Override
            public Boolean getOopIncludeCoPay() {
                return null;
            }

            @Override
            public Boolean getOopNotIncludeDeductible() {
                return null;
            }

            @Override
            public Boolean getOopNotIncludeCoIns() {
                return null;
            }

            @Override
            public Boolean getOopNotIncludeCoPay() {
                return null;
            }

            @Override
            public String getLifeTimeMax() {
                return null;
            }

            @Override
            public String getAnnualMax() {
                return null;
            }

            @Override
            public Boolean getAnnualBenefitLimitActive() {
                return null;
            }

            @Override
            public BigDecimal getRemainingAnnualLimit() {
                return BigDecimal.ZERO;
            }

            @Override
            public Boolean getDmeLimitations() {
                return null;
            }

            @Override
            public Boolean getDmeExclusions() {
                return null;
            }

            @Override
            public Boolean getDmeProvisions() {
                return null;
            }

            @Override
            public Boolean getDmeLimitationMedicalNecessity() {
                return null;
            }

            @Override
            public Boolean getDmeLimitation1PerYear() {
                return null;
            }

            @Override
            public Boolean getDmeLimitation2Year() {
                return null;
            }

            @Override
            public String getDmeLimitationOther() {
                return null;
            }

            @Override
            public Boolean getDmeExclusionOrthotics() {
                return null;
            }

            @Override
            public Boolean getDmeExclusionProsthetics() {
                return null;
            }

            @Override
            public Boolean getDmeExclusionShoes() {
                return null;
            }

            @Override
            public String getDmeExclusionOther() {
                return null;
            }

            @Override
            public Boolean getDmeProvisionMedicalNecessity() {
                return null;
            }

            @Override
            public String getDmeProvisionOther() {
                return null;
            }

            @Override
            public String getRequiredPaperwork() {
                return null;
            }

            @Override
            public Boolean getWaitingPeriods() {
                return null;
            }

            @Override
            public Date getWaitingStartDate() {
                return null;
            }

            @Override
            public Date getWaitingEndDate() {
                return null;
            }

            @Override
            public Date getAuthorizationDate() {
                return null;
            }

            @Override
            public Date getExpirationDate() {
                return null;
            }

            @Override
            public Boolean getReferralNeeded() {
                return null;
            }

            @Override
            public String getReferralNumber() {
                return null;
            }

            @Override
            public Boolean getPreAuthNecessary() {
                return null;
            }

            @Override
            public String getRequiredIfOver() {
                return null;
            }

            @Override
            public Boolean getWithoutPreAuth() {
                return null;
            }

            @Override
            public Date getAuthInitiationDate() {
                return null;
            }

            @Override
            public Date getAuthApprovalDate() {
                return null;
            }

            @Override
            public Date getAuthDenialDate() {
                return null;
            }

            @Override
            public String getAuthDenialReason() {
                return null;
            }

            @Override
            public Date getAuthAppealDate() {
                return null;
            }

            @Override
            public String getAuthAppealNotes() {
                return null;
            }

            @Override
            public String getNotes() {
                return null;
            }

            @Override
            public String getPreAuthNotes() {
                return null;
            }

            @Override
            public Long getPrescriptionId() {
                return 99L;
            }

            @Override
            public Long getPatientInsuranceId() {
                return 116L;
            }

            @Override
            public String getInsuranceCompanyName() {
                return null;
            }

            @Override
            public Long getPatientInsuranceCompanyId() {
                return null;
            }

            @Override
            public Boolean getPatientInsuranceActive() {
                return null;
            }
        };
    }

    public static InsuranceVerification getInsuranceVerification() {
        InsuranceVerification insuranceVerification0 = new InsuranceVerification();
        insuranceVerification0.setId(Long.parseLong("97"));
        insuranceVerification0.setPrescriptionId(Long.parseLong("99"));
        insuranceVerification0.setPatientInsuranceId(Long.parseLong("116"));
        insuranceVerification0.setPatientInsurance(getPatientInsurance());
        insuranceVerification0.setDeductibleAmount(BigDecimal.valueOf(Double.valueOf("0")));
        insuranceVerification0.setAmountMet(BigDecimal.valueOf(Double.valueOf("0")));
        insuranceVerification0.setBenefitsPayable("80/20");
        insuranceVerification0.setOutOfPocketAmount(BigDecimal.valueOf(Double.valueOf("0")));
        insuranceVerification0.setOutOfPocketRemaining(BigDecimal.valueOf(Double.valueOf("0")));
        insuranceVerification0.setRemainingAnnualLimit(BigDecimal.valueOf(Double.valueOf("0")));
        insuranceVerification0.setPtResponsibleForCoIns(true);
        insuranceVerification0.setVerifiedById(Long.parseLong("430"));
        insuranceVerification0.setVerified(true);
        insuranceVerification0.setVerifiedOn(new java.sql.Date(Long.parseLong("1550034000000")));
        insuranceVerification0.setCurrentCoverage(true);
        insuranceVerification0.setEffectiveDate(new java.sql.Date(Long.parseLong("1546318800000")));
        insuranceVerification0.setNetworkType("In-Network");
        insuranceVerification0.setCarrierType(InsuranceType.primary.name());
        return insuranceVerification0;
    }

    public static List<InsuranceVerification> getInsuranceVerificationList() {
        List<InsuranceVerification> insuranceVerificationList = new ArrayList<>();
        insuranceVerificationList.add(getInsuranceVerification());
        return insuranceVerificationList;
    }

    public static List<IInsuranceVerification> getIInsuranceVerificationList() {
        List<IInsuranceVerification> insuranceVerificationList = new ArrayList<>();
        insuranceVerificationList.add(getIInsuranceVerification());
        return insuranceVerificationList;
    }

    public static InsuranceCompanyBranch getInsuranceCompanyBranch() {
        InsuranceCompanyBranch insuranceCompanyBranch0 = new InsuranceCompanyBranch();
        insuranceCompanyBranch0.setId(Fake.ID);
        insuranceCompanyBranch0.setName("Main");
        insuranceCompanyBranch0.setId(Long.parseLong("1"));
        insuranceCompanyBranch0.setState("TX");
        insuranceCompanyBranch0.setCity("San Antonio");
        return insuranceCompanyBranch0;
    }

    public static InsuranceCompany getInsuranceCompany() {
        InsuranceCompany insuranceCompany0 = new InsuranceCompany();
        insuranceCompany0.setId(Fake.ID);
        insuranceCompany0.setName("WellMed-United Healthcare");
        insuranceCompany0.setId(Long.parseLong("1"));
        insuranceCompany0.setDefaultAllowable(getFeeSchedule());
        insuranceCompany0.setDefaultBillingAmount(getFeeSchedule());
        insuranceCompany0.setPayerTypeId(Long.parseLong("5"));
        insuranceCompany0.setAlert(false);
        insuranceCompany0.setFollowUpMessage("");
        insuranceCompany0.setDefaultBillingAmountId(Long.parseLong("2"));
        insuranceCompany0.setDefaultAllowableId(Long.parseLong("2"));
        insuranceCompany0.setActive(true);
        insuranceCompany0.setFaxNumber("");
        insuranceCompany0.setPayerType(getPayerTypeGroup());
        insuranceCompany0.setPhoneNumber("**********");
        insuranceCompany0.setClearingHousePayer(getClearingHousePayer());
        insuranceCompany0.setClearingHousePayerId(getClearingHousePayer().getId());
        insuranceCompany0.setForm1500TemplateId(Fake.ID);
        insuranceCompany0.setForm1500Template(getForm1500Template());
        insuranceCompany0.setSelfPay(false);
        return insuranceCompany0;
    }

    public static ClearingHousePayer getClearingHousePayer() {
        ClearingHousePayer chp = new ClearingHousePayer();
        chp.setId(Fake.ID);
        chp.setName("Clearing House");
        chp.setPayerId("*********");
        return chp;
    }

    public static List<InsuranceCompany> getInsuranceCompanyList() {
        List<InsuranceCompany> insuranceCompanyList = new ArrayList<>();
        insuranceCompanyList.add(getInsuranceCompany());
        return insuranceCompanyList;
    }

    public static PayerType getPayerTypeGroup() {
        PayerType payerType0 = new PayerType();
        payerType0.setId(Fake.ID);
        payerType0.setName("Group");
        payerType0.setId(Long.parseLong("5"));
        return payerType0;
    }

    public static Form1500Template getForm1500Template() {
        Form1500Template form1500Template = new Form1500Template();
        form1500Template.setId(Fake.ID);
        form1500Template.setName("Default");
        form1500Template.setBox12PatientSignature(Signature.LEAVE_BLANK);
        form1500Template.setBox13InsuredSignature(Signature.LEAVE_BLANK);
        form1500Template.setBox14And15ShowAccidentDate(AccidentDate.BOX_14);
        form1500Template.setBox17AOtherIDQualifier("");
        form1500Template.setBox17AOtherIDEntry("");
        form1500Template.setBox24BPlaceOfService(PlaceOfService.Home);
        form1500Template.setBox24H("");
        form1500Template.setBox24IRenderingProviderOtherIDQualifier("DN");
        form1500Template.setBox31ProviderInformation(ProviderInformation.FACILITY_INFORMATION);
        form1500Template.setBox32ServiceFacilityLocation("Dairy Queen");
        form1500Template.setBox32Hide(false);
        form1500Template.setBillingStreetAddress("59 East Main St");
        form1500Template.setBox32FacilityState("MA");
        form1500Template.setBox32FacilityCity("Marlborough");
        form1500Template.setBox32AFacilityNpi("**********");
        form1500Template.setBox32BFacilityTaxonomy("98744");
        form1500Template.setBox33BBillingTaxonomy("2259778");
        form1500Template.setUseBranchTaxId(true);
        form1500Template.setUseInsuranceBranchName(true);
        form1500Template.setBillingCompanyName("Bill Co.");
        form1500Template.setBillingStreetAddress("13 Walnut Way");
        form1500Template.setBillingCity("New York");
        form1500Template.setBillingState("NY");
        form1500Template.setBillingZipcode("11111");
        form1500Template.setBillingPhoneNumber("**********");
        form1500Template.setBillingFaxNumber("**********");
        form1500Template.setBillingNpi("**********");
        form1500Template.setBox24JRenderingProviderOtherIDRow1(null);
        form1500Template.setBox24JRenderingProviderOtherIDRow2(RenderingProviderOtherId.OTHER);
        form1500Template.setBox24JOtherNpi("**********");
        return form1500Template;
    }

    public static FeeSchedule getFeeSchedule() {
        FeeSchedule defaultAllowable0 = new FeeSchedule();
        defaultAllowable0.setId(Fake.ID);
        defaultAllowable0.setName("75% Medicare");
        defaultAllowable0.setId(Long.parseLong("2"));
        defaultAllowable0.setUpdatedAt(new java.sql.Timestamp(1543942476000L));
        defaultAllowable0.setUniversalMultiplier(true);
        defaultAllowable0.setMultiplier(BigDecimal.valueOf(Double.valueOf("-25")));
        return defaultAllowable0;
    }

    public static List<PatientInsurance> getPatientInsuranceList() {
        List<PatientInsurance> patientInsuranceList = new ArrayList<>();
        patientInsuranceList.add(getPatientInsurance());
        return patientInsuranceList;
    }

    public static PatientInsurance getPatientInsurance() {
        PatientInsurance patientInsurance0 = new PatientInsurance();
        patientInsurance0.setId(Long.parseLong("116"));
        patientInsurance0.setState("TX");
        patientInsurance0.setPlanNumber("");
        patientInsurance0.setEmployer("");
        patientInsurance0.setInsuranceCompanyBranchId(Long.parseLong("1"));
        patientInsurance0.setActive(true);
        patientInsurance0.setCity("San Antonio");
        patientInsurance0.setDob(java.sql.Date.valueOf(LocalDate.of(1957, 11, 30)));
        patientInsurance0.setPatient(getPatient());
        patientInsurance0.setPatientId(ID4);
        patientInsurance0.setRelationToSubscriber("self");
        patientInsurance0.setInsuranceCompany(getInsuranceCompany());
        patientInsurance0.setInsuranceCompanyBranch(getInsuranceCompanyBranch());
        patientInsurance0.setStreetAddress("1259 Thompson Pl");
        patientInsurance0.setZipcode("78226");
        patientInsurance0.setInsuranceNumber("*********");
        patientInsurance0.setFirstName("P");
        patientInsurance0.setLastName("B");
        patientInsurance0.setGender("Female");
        patientInsurance0.setPhoneNumber("");
        patientInsurance0.setGroupNumber("");
        patientInsurance0.setInsuranceCompanyId(Long.parseLong("1"));
        patientInsurance0.setTraceNumber(Long.parseLong("*********"));
        patientInsurance0.setSsn("*********");
        return patientInsurance0;
    }

    public static PatientInsuranceDTO getPatientInsuranceDTO() {
        PatientInsuranceDTO patientInsuranceDTO = new PatientInsuranceDTO(getPatientInsurance());
        patientInsuranceDTO.setInsuranceVerification(getInsuranceVerification());
        return patientInsuranceDTO;
    }

    public static List<PatientInsuranceDTO> getPatientInsuranceDtoList() {
        List<PatientInsuranceDTO> patientInsuranceDTOList = new ArrayList<>();
        patientInsuranceDTOList.add(getPatientInsuranceDTO());
        return patientInsuranceDTOList;
    }

    public static Patient getPatient() {

        Patient patient0 = new Patient();
        patient0.setId(ID4);
        patient0.setState("TX");
        patient0.setWorkPhone("");
        patient0.setMaritalStatus("married");
        patient0.setNickname("");
        patient0.setEmploymentStatus("unemployed");
        patient0.setManualHipaaSignedDate(java.sql.Date.valueOf(LocalDate.of(2019, 1, 8)));
        patient0.setActive(true);
        patient0.setEmail("");
        LocalDateTime l = LocalDateTime.of(2018, 12, 12, 15, 32, 40);
        patient0.setCreatedAt(Timestamp.valueOf(l));
        patient0.setCity("San Antonio");
        patient0.setDob(java.sql.Date.valueOf(LocalDate.of(1957, 11, 30)));
        patient0.setStreetAddress("1259 Thompson Pl");
        patient0.setZipcode("78226");
        patient0.setFirstName("Patricia");
        patient0.setMiddleName("A");
        patient0.setLastName("Benner");
        patient0.setGender("Female");
        patient0.setHomePhone("");
        patient0.setPrimaryBranch(getBranch());
        patient0.setCellPhone("**********");
        patient0.setSpanishText(false);
        patient0.setPrimaryBranchId(Long.parseLong("1"));
        patient0.setSsn("*********");
        patient0.setPrimaryPractitionerId(Fake.ID);
        return patient0;
    }

    public static List<Patient> getPatientList() {
        List<Patient> patientList = new ArrayList<>();
        patientList.add(Fake.getPatient());
        return patientList;
    }

    public static Company getCompany() {
        Company company = new Company();
        company.setId(Fake.ID);
        company.setActive(true);
        company.setName("successful");
        company.setKey("successful");
        return company;
    }

    public static List<Branch> getBranchList() {
        List<Branch> branchList = new ArrayList<>();
        branchList.add(getBranch());
        return branchList;
    }

    public static Branch getBranch() {
        Branch primaryBranch0 = new Branch();
        ClearingHouseMain chm = new ClearingHouseMain();
        chm.setId(Fake.ID);
        chm.setHostUrl("sshftp.zirmed.com");
        chm.setDownload("/Download");
        chm.setUpload("/Upload/");
        chm.setPort(22);

        ClearingHouse ch = new ClearingHouse();
        ch.setId(Fake.ID);
        ch.setRestUser("RTElg178610");
        ch.setRestPassword("weVid2phew");
        ch.setAccountNumber("178610");
        ch.setPassword("bivAvOC8re");
        ch.setActive(true);
        ch.setHmacKey("Um6ibRt2MDn2/xqUyWJNxKPnMc6jB+yPQJS978EH");
        ch.setClearingHouse(chm);
        ch.setClearingHouseId(chm.getId());
        primaryBranch0.setInClearingHouse(ch);
        primaryBranch0.setOutClearingHouse(ch);
        primaryBranch0.setId(Fake.ID);
        primaryBranch0.setName("San Antonio");
        primaryBranch0.setId(Long.parseLong("1"));
        primaryBranch0.setState("TX");
        primaryBranch0.setPoBox("4242 Medical Dr, Suite 2100, San Antonio, TX 78229");
        primaryBranch0.setBillingFaxNumber("**********");
        primaryBranch0.setHideCompanyName(false);
        primaryBranch0.setUseBranchName(false);
        primaryBranch0.setUseSalesTax(false);
        primaryBranch0.setCode("SAN");
        primaryBranch0.setActive(true);
        primaryBranch0.setFaxNumber("**********");
        primaryBranch0.setCity("San Antonio");
        primaryBranch0.setNpi("**********");
        primaryBranch0.setStreetAddress("5282 Medical Drive, Suite 105");
        primaryBranch0.setZipcode("78229");
        primaryBranch0.setPhoneNumber("**********");
        primaryBranch0.setTaxIdType("leave_blank");
        primaryBranch0.setTagLine("Neu Limbs, LLC.");
        primaryBranch0.setBillingPhoneNumber("**********");
        primaryBranch0.setBillingCompanyName("Neu Limbs, LLC.");
        primaryBranch0.setBillingStreetAddress("5282 Medical Drive, Suite 105");
        primaryBranch0.setBillingCity("San Antonio");
        primaryBranch0.setBillingState("TX");
        primaryBranch0.setBillingZipcode("78229");
        primaryBranch0.setAdditionalText("with Hill Country O&P.");
        primaryBranch0.setHideServiceFacilityLocation(false);
        primaryBranch0.setSendNotifications(true);
        primaryBranch0.setTimezone("America/Chicago");
        return primaryBranch0;
    }

    public static Map<String, String> getBillingDefaultSettings() {
        Map<String, String> map = new HashMap<>();
        map.put("default_billing_fee_schedule", "2");
        map.put("downpayment_percentage", "30");
        map.put("delivery_percentage", "70");
        map.put("auto_post_percentage", "100");
        return map;
    }

    public static Map<String, String> getClaimDefaultSettings() {
        Map<String, String> map = new HashMap<>();
        map.put("insured_id_number", "none");
        map.put("patient_signature", "print_name");
        map.put("insured_signature", "signature_on_file");
        map.put("show_accident_date", "box_15");
        map.put("physician_to_use", "referring_physician");
        map.put("physician_id_to_use", "npi");
        map.put("physician_id_qualifier", "1c");
        map.put("place_of_service", "21");
        map.put("rendering_provider_npi", "practitioner_npi");
        map.put("rendering_provider_other_id", "leave_blank");
        map.put("rendering_provider_other_id_qualifier", "0b");
        map.put("tax_id_to_use", "ssn");
        map.put("tax_id_number", "");
        map.put("facility_taxonomy", "");
        map.put("billing_taxonomy", "");
        map.put("provider_information", "facility_information");
        map.put("format_837", "zirmed");
        map.put("use_icb_name_for_hcfa", "Y");
        map.put("use_single_claim", "N");
        return map;
    }

    public static Payment getPayment() {
        Payment payment = new Payment();
        payment.setId(Fake.ID);
        payment.setPaymentType("insurance_payment_credit_card");
        payment.setPayerType("insurance_company");
        payment.setAdjustment(BigDecimal.ZERO);
        payment.setAmount(BigDecimal.TEN);
        payment.setUnappliedAmount(BigDecimal.ONE);
        payment.setCreatedById(Fake.ID);
        payment.setPrescription(getPrescription());
        return payment;
    }

    public static Payment getPaymentPatientCash() {
        Payment payment = new Payment();
        payment.setId(Fake.ID);
        payment.setPaymentType("cash");
        payment.setPayerType("patient");
        payment.setAmount(BigDecimal.TEN);
        payment.setUnappliedAmount(BigDecimal.ONE);
        payment.setAdjustment(BigDecimal.ZERO);
        payment.setCreatedById(Fake.ID);
        return payment;
    }

    public static Payment getPaymentPatientAdjustmentCash() {
        Payment payment = new Payment();
        payment.setId(Fake.ID);
        payment.setPaymentType("cash");
        payment.setPayerType("patient_adjustment");
        payment.setAdjustment(BigDecimal.TEN);
        Adjustment adjustment = getAdjustmentNegativeWithdrawCashRefund();
        payment.setAdjustmentType(adjustment);
        payment.setAmount(BigDecimal.TEN);
        payment.setUnappliedAmount(BigDecimal.ONE);
        payment.setCreatedById(Fake.ID);
        return payment;
    }

    public static Payment getPaymentAdjustmentCash() {
        Payment payment = new Payment();
        payment.setId(Fake.ID);
        payment.setPaymentType("cash");
        payment.setPayerType("adjustment");
        payment.setAdjustment(BigDecimal.TEN);
        Adjustment adjustment = getAdjustmentNegativeWithdrawCashRefund();
        payment.setAdjustmentType(adjustment);
        payment.setAmount(BigDecimal.TEN);
        payment.setUnappliedAmount(BigDecimal.ONE);
        payment.setCreatedById(Fake.ID);
        return payment;
    }

    public static Adjustment getAdjustmentNegativeWithdrawCashRefund() {
        Adjustment adjustment = new Adjustment();
        adjustment.setId(Fake.ID);
        adjustment.setWithdraw(true);
        adjustment.setSelected(true);
        adjustment.setName("Refund");
        adjustment.setPaymentTypeOrigin("cash");
        adjustment.setOperation("-");
        return adjustment;
    }

    public static Adjustment getAdjustmentPositiveCash() {
        Adjustment adjustment = new Adjustment();
        adjustment.setId(Fake.ID);
        adjustment.setWithdraw(true);
        adjustment.setSelected(true);
        adjustment.setName("PositiveCash");
        adjustment.setPaymentTypeOrigin("cash");
        adjustment.setOperation("+");
        return adjustment;
    }

    public static List<Payment> getPaymentList() {
        List<Payment> paymentList = new ArrayList<>();
        paymentList.add(getPayment());
        return paymentList;
    }

    public static AppliedPayment_L_Code getAppliedPayment_L_Code() {
        AppliedPayment_L_Code appliedPayment_l_code = new AppliedPayment_L_Code();
        appliedPayment_l_code.setId(Fake.ID);
        appliedPayment_l_code.setAmount(BigDecimal.TEN);
        appliedPayment_l_code.setAdjustment(BigDecimal.ZERO);
        appliedPayment_l_code.setPrescriptionLCode(Fake.getPrescription_l_code());
        appliedPayment_l_code.getPrescriptionLCode().setPrescription(Fake.getPrescription());
        appliedPayment_l_code.getPrescriptionLCode().getPrescription().setPatient(Fake.getPatient());
        appliedPayment_l_code.getPrescriptionLCode().getPrescription().getPatient().setPrimaryBranchId(Fake.ID);
        AppliedPayment appliedPayment = getAppliedPayment();
        appliedPayment_l_code.setAppliedPayment(appliedPayment);
        appliedPayment_l_code.setAppliedPaymentId(appliedPayment.getId());
        return appliedPayment_l_code;
    }

    public static List<AppliedPayment_L_Code> getAppliedPayment_L_CodeList() {
        List<AppliedPayment_L_Code> appliedPaymentLCodeList = new ArrayList<>();
        appliedPaymentLCodeList.add(Fake.getAppliedPayment_L_Code());
        return appliedPaymentLCodeList;
    }

    public static AppliedPayment getAppliedPayment() {
        AppliedPayment appliedPayment = new AppliedPayment();
        appliedPayment.setPaymentId(Fake.getPayment().getId());
        appliedPayment.setPayment(Fake.getPayment());
        appliedPayment.setClaimId(Fake.getClaim().getId());
        appliedPayment.setClaim(Fake.getClaim());
        appliedPayment.setAmountApplied(BigDecimal.TEN);
        appliedPayment.setAppliedDate(Fake.getSqlDate("2019-05-05"));
        appliedPayment.setAdjustmentApplied(new BigDecimal("0.00"));
        return appliedPayment;
    }

    public static List<AppliedPayment> getAppliedPaymentList() {
        List<AppliedPayment> appliedPaymentList = new ArrayList<>();
        appliedPaymentList.add(Fake.getAppliedPayment());
        return appliedPaymentList;
    }

    public static Note getNote() {
        Note note = new Note();
        note.setId(Fake.ID);
        note.setUserId(Fake.ID);
        Calendar c = Calendar.getInstance();
        c.set(2020, 5, 5, 5, 5, 5);
        note.setCreatedAt(new Timestamp(c.getTimeInMillis()));
        note.setNote("Testing");
        note.setNoteType("general");
        return note;
    }

    public static List<Note> getNoteList() {
        List<Note> noteList = new ArrayList<>();
        noteList.add(Fake.getNote());
        return noteList;
    }

    public static AutoPost getAutoPost() {
        AutoPost autoPost = new AutoPost();
        autoPost.setId(Fake.ID);
        autoPost.setInsuranceCompany(getInsuranceCompany());
        autoPost.setStatus("pending");
        autoPost.setTotalUnappliedAmount(BigDecimal.TEN);
        autoPost.setX12File(getX12_File());
        return autoPost;
    }

    public static X12_File getX12_File() {
        X12_File x12File = new X12_File();
        x12File.setFilename("x12_file");
        x12File.setType("827");
        x12File.setProcessed(false);
        x12File.setContents(getPrpX12FileContent());
//        x12File.setBranchId(1L);
        return x12File;
    }

    public static AutoPostPatient getAutoPostPatient() {
        AutoPostPatient autoPostPatient = new AutoPostPatient();
        autoPostPatient.setId(Fake.ID);
        autoPostPatient.setPatient(getPatient());
        autoPostPatient.setPatientId(getPatient().getId());
        autoPostPatient.setAutoPost(getAutoPost());
        autoPostPatient.setAutoPostId(getAutoPost().getId());
        autoPostPatient.setClaim(getClaim());
        autoPostPatient.setClaimId(getClaim().getId());
        autoPostPatient.setStatus("pending");
        return autoPostPatient;
    }

    public static AutoPostPatient_L_Code getAutoPostPatient_L_Code() {
        AutoPostPatient_L_Code autoPostPatientLCode = new AutoPostPatient_L_Code();
        autoPostPatientLCode.setId(Fake.ID);
        return autoPostPatientLCode;
    }

    public static List<L_CodeDTO> getL_CodeDTOList() {
        List<L_CodeDTO> lCodeDTOList = new ArrayList<>();
        lCodeDTOList.add(getL_CodeDTO());
        return lCodeDTOList;
    }

    public static L_CodeDTO getL_CodeDTO() {
        List<L_CodeDTO> lCodeDTOList = new ArrayList<>();
        L_CodeDTO lCodeDTO = new L_CodeDTO();
        lCodeDTO.setId(4263L);
        lCodeDTO.setName("L5700");
        lCodeDTO.setPrescriptionId(getPrescription().getId());
        lCodeDTO.setPrescriptionLCodeId(331L);
        lCodeDTO.setBalance(BigDecimal.ZERO);
        lCodeDTO.setEndingBalance(BigDecimal.ZERO);
        lCodeDTO.setCovered(true);
        lCodeDTO.setBill(true);
        lCodeDTO.setQuantity(1L);
        lCodeDTO.setTotalCharge(new BigDecimal("3431.00"));
        lCodeDTO.setTotalAllowable(new BigDecimal("2771.02"));
        lCodeDTO.setPayment(BigDecimal.ZERO);
        lCodeDTO.setAdjustment(BigDecimal.ZERO);
        return lCodeDTO;
    }

    public static List<StatementDTO> getStatementDTOList() {
        List<StatementDTO> statementDTOList = new ArrayList<>();
        statementDTOList.add(getStatementDTO());
        return statementDTOList;
    }

    public static StatementDTO getStatementDTO() {
        StatementDTO statementDTO0 = new StatementDTO();
        statementDTO0.setSubmittedDate("2020-03-04");
        statementDTO0.setLastSentDate("2020-04-24");
        statementDTO0.setNextAttempt("sent_to_collections");
        //statementDTO0.setServiceLines(serviceLines0);
        statementDTO0.setClaimId(Long.parseLong("318"));
        statementDTO0.setPrescriptionId(Long.parseLong("124"));
        statementDTO0.setPatientId(Long.parseLong("206"));
        statementDTO0.setTotalAdjustments(new BigDecimal("0"));
        statementDTO0.setVisitDate("2020-03-04");
        statementDTO0.setTotalInsurancePaid(new BigDecimal("151.16"));
        statementDTO0.setGuarantorName("Robert Degraff");
        statementDTO0.setGuarantorNumberStreet("12936 Ryle Road");
        statementDTO0.setGuarantorCityStateZip("Union, KY 41091");
        statementDTO0.setCompanyName("Durretts Orthotics and Prosthetics");
        statementDTO0.setCompanyNumberStreet("20 Medical Village Dr. Suite 100");
        statementDTO0.setCompanyCityStateZip("Edgewood, KY 41017");
        statementDTO0.setCompanyPhone("**********");
        statementDTO0.setInsuranceName("UMR");
        statementDTO0.setVisitLocation("20 Medical Village Dr. Suite 100, Edgewood, KY 41017");
        statementDTO0.setVisitDescription("DIABETIC SHOES AND INSERTS");
        statementDTO0.setTotalCharges(new BigDecimal("619.8"));
        statementDTO0.setTotalGuarantorPaid(new BigDecimal("0"));
        statementDTO0.setGuarantorBalanceOwed(new BigDecimal("117.8"));
        statementDTO0.setInsuranceBalanceOwed(new BigDecimal("0"));
        statementDTO0.setStatementDate("2020-04-28");
        statementDTO0.setAgeInDays(Long.parseLong("55"));
        statementDTO0.setLastPaymentDate("2023-03-23");
        statementDTO0.setLastPayer("Insurance");
        statementDTO0.setLastPaymentAmount(new BigDecimal("151.16"));
        //statementDTO0.setLatestPatientStatement(latestPatientStatement0);
        statementDTO0.setPatientName("Robert Degraff");
        statementDTO0.setProviderName("");
        return statementDTO0;
    }

    public static List<PatientStatement> getPatientStatementList() {
        List<PatientStatement> patientStatementList = new ArrayList<>();
        PatientStatement patientStatement0 = new PatientStatement();
        patientStatement0.setId(Long.parseLong("12"));
        patientStatement0.setStatus("attempt_1");
        patientStatement0.setSentDate(new java.sql.Date(Long.parseLong("1587700800000")));
        patientStatement0.setClaim(Fake.getClaim());
        patientStatement0.setClaimId(Long.parseLong("318"));
        patientStatement0.setUser(Fake.getUserFakeID());
        patientStatement0.setUserId(Long.parseLong("1"));
        patientStatement0.setCreatedAt(new java.sql.Timestamp(1587756820000L));
        patientStatementList.add(patientStatement0);

        PatientStatement patientStatement1 = new PatientStatement();
        patientStatement1.setId(Long.parseLong("33"));
        patientStatement1.setStatus("attempt_2");
        patientStatement1.setSentDate(new java.sql.Date(Long.parseLong("1587700800000")));
        patientStatement1.setClaim(Fake.getClaim());
        patientStatement1.setClaimId(Long.parseLong("318"));
        patientStatement1.setUser(Fake.getUserFakeID());
        patientStatement1.setUserId(Long.parseLong("1"));
        patientStatement1.setCreatedAt(new java.sql.Timestamp(1587756887000L));
        patientStatementList.add(patientStatement1);

        PatientStatement patientStatement2 = getPatientStatement();
        patientStatementList.add(patientStatement2);

        return patientStatementList;
    }

    public static PatientStatement getPatientStatement() {
        PatientStatement patientStatement2 = new PatientStatement();
        patientStatement2.setId(Long.parseLong("54"));
        patientStatement2.setStatus("attempt_3");
        patientStatement2.setSentDate(DateUtil.getCurrentDateDB());
        patientStatement2.setClaim(Fake.getClaim());
        patientStatement2.setClaimId(Long.parseLong("318"));
        patientStatement2.setUser(Fake.getUserFakeID());
        patientStatement2.setUserId(Long.parseLong("1"));
        patientStatement2.setCreatedAt(new Timestamp(1587756960000L));
        return patientStatement2;
    }

    public static Object[] getPaymentDTOObjectArray() {
        Object[] objects = new Object[32];
        objects[0] = BigInteger.valueOf(10861L);
        objects[1] = BigInteger.valueOf(1L);
        objects[2] = BigInteger.valueOf(1L);
        objects[3] = BigDecimal.valueOf(Double.valueOf("0.00"));
        objects[4] = BigDecimal.valueOf(Double.valueOf("80.00"));
        objects[5] = null;
        objects[6] = null;
        objects[7] = "555";
        objects[8] = "777";
        objects[9] = BigInteger.valueOf(2L);
        objects[10] = BigDecimal.valueOf(Double.valueOf("0.00"));
        objects[11] = java.sql.Date.valueOf(LocalDate.now());
        objects[12] = "888";
        objects[13] = "999";
        objects[14] = BigInteger.valueOf(5404L);
        objects[15] = BigInteger.valueOf(3L);
        objects[16] = "patient";
        objects[17] = "patient_credit_card";
        objects[18] = BigDecimal.valueOf(Double.valueOf("0.00"));
        objects[19] = BigDecimal.valueOf(Double.valueOf("80.00"));
        objects[20] = BigInteger.valueOf(639L);
        objects[21] = java.sql.Date.valueOf(LocalDate.now());
        objects[22] = java.sql.Timestamp.valueOf(LocalDateTime.now());
        objects[23] = BigInteger.valueOf(3L);
        objects[24] = BigDecimal.valueOf(13L);
        objects[25] = java.sql.Date.valueOf(LocalDate.now());
        objects[26] = "applied_by";
        objects[27] = "patient";
        objects[28] = "Last Name";
        objects[29] = "Branch Name";
        objects[30] = BigInteger.valueOf(13L);
        objects[31] = BigInteger.valueOf(1L);
        return objects;
    }

    public static String getPrpX12FileContent() {
        String content = "ISA*00*          *00*          *ZZ*ZIRMED         *ZZ*180663         *200625*0206*^*00501*200625001*1*P*:~GS*HP*ZIRMED*180663*20200625*0206*1*X*005010X221A1~ST*835*0001~BPR*I*781.06*C*CCD*MASTERCARD*01*000000000*DA***********000*************01*000000000*DA***********000*20200625~TRN*1*283753***********~DTM*405*20200625~N1*PR*PAID BY - UMR UMR*ZZ*GV-D~N3*123 Main St~N4*Louisville*KY*40202~REF*EO*158764603~N1*PE*Prosthetic & Orthotic Group of Northern Colorado, LLC*XX***********~REF*TJ**********~LX*1~CLP*1*1*781.06*781.06**13*283753~NM1*QC*1*UMR*UMR~REF*F8*~DTM*050*20200624~SE*16*0001~GE*2*1~IEA*1*200625001~";
        return content;
    }

    public static String getBadPrpX12FileContent() {
        String content = "ISA*00*          *00*          *ZZ*ZIRMED         *ZZ*180663         *200625*0206*^*00501*200625001*1*P*:~GS*HP*ZIRMED*180663*20200625*0206*1*X*005010X221A1~ST*835*0001~BPR*I*781.06*C*CCD*MASTERCARD*01*000000000*DA***********000*************01*000000000*DA***********000*20200625~TRN*1*283753***********~DTM*405*20200625~N1*PR*PAID BY - UMR UMR*ZZ*GV-D~N3*123 Main St~N4*Louisville*KY*40202~REF*EO*158764603~N1*PE*Prosthetic & Orthotic Group of Northern Colorado, LLC*XX***********~REF*TJ**********~LX*1~CLP*URM*1*781.06*781.06**13*283753~NM1*QC*1*UMR*UMR~REF*F8*~DTM*050*20200624~SE*16*0001~ST*835*0002~BPR*I*327.87*C*CCD*DISCOVER*01*000000000*DA***********000*************01*000000000*DA***********000*20200625~TRN*1*02400R***********~DTM*405*20200625~N1*PR*PAID BY - Quinn Warner*ZZ*GV-E~N3*123 Main St~N4*Louisville*KY*40202~REF*EO*158786263~N1*PE*Prosthetic & Orthotic Group of Northern Colorado, LLC*XX***********~REF*TJ**********~LX*1~CLP*XYZ*1*327.87*327.87**13*02400R~NM1*QC*1*Warner*Quinn~REF*F8*~DTM*050*20200624~SE*16*0002~GE*2*1~IEA*1*200625001~";
        return content;
    }

    public static List<EmpirePurchaseOrder> getEmpirePurchaseOrderList() {
        JsonNode root = null;
        try {
            String inputData = getEmpirePurchaseOrderJsonResponseBodyData();

            ObjectMapper mapper = new ObjectMapper();
            root = mapper.readTree(inputData);
            JsonNode data = root.path("data");

            return mapper.readValue(String.valueOf(data), new TypeReference<List<EmpirePurchaseOrder>>() {
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getEmpirePurchaseOrderJsonResponseBodyData() {
        return "{\"data\":" +
                "[" +
                "{\"order_line_id\":8002,\"order_number\":8,\"manufacturer_name\":\"Ullrich Group\",\"quantity\":2,\"quantity_received\":0,\"price\":14.91,\"part_number\":\"SXHNIW9Y\",\"job_number\":\"Noble Littel\",\"user_ordered_timestamp\":\"2020-05-10T00:00:00+00:00\",\"date_needed\":\"2020-05-15T00:00:00+00:00\",\"date_needed_is_ground\":true,\"date_expected\":\"2020-05-14T00:00:00+00:00\",\"date_received\":\"2020-05-19T00:00:00+00:00\",\"is_for_stock\":false,\"return_requested_customer_timestamp\":null,\"description\":\"VOLUPTATE OPTIO VITAE CONSEQUUNTUR SINT TENETUR.\",\"rma_return_number\":\"7e69241a-31ba-3d69-9e83-070d97eb9c39\",\"order_status_name\":\"Received\",\"billing_status_name\":\"Billed\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Unde asperiores provident velit blanditiis quo iste minima.\",\"customer_return_note\":\"Facere quisquam porro ut illo consequuntur quas.\",\"customer_internal_memo\":null,\"serial_numbers\":[555555],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8003,\"order_number\":9,\"manufacturer_name\":\"Stanton, Dare and Rosenbaum\",\"quantity\":2,\"quantity_received\":0,\"price\":6.5,\"part_number\":\"OUPXMGL0\",\"job_number\":\"Sincere Witting\",\"user_ordered_timestamp\":\"2020-05-20T00:00:00+00:00\",\"date_needed\":\"2020-05-28T00:00:00+00:00\",\"date_needed_is_ground\":false,\"date_expected\":\"2020-05-27T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":false,\"return_requested_customer_timestamp\":null,\"description\":\"QUIA QUIA CORRUPTI A VELIT REPREHENDERIT NISI LIBERO.\",\"rma_return_number\":\"b4803bbd-b1fe-3fa0-8dd9-653f99e535a5\",\"order_status_name\":\"Backordered\",\"billing_status_name\":\"Credited\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Dolor fugit modi rerum impedit.\",\"customer_return_note\":\"Vel possimus et qui voluptates aliquam eaque.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8004,\"order_number\":10,\"manufacturer_name\":\"Ullrich Group\",\"quantity\":3,\"quantity_received\":0,\"price\":8.7,\"part_number\":\"XWUWZXVFZMM\",\"job_number\":\"Demario Zboncak\",\"user_ordered_timestamp\":\"2020-05-08T00:00:00+00:00\",\"date_needed\":\"2020-05-15T00:00:00+00:00\",\"date_needed_is_ground\":false,\"date_expected\":\"2020-05-14T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":true,\"return_requested_customer_timestamp\":null,\"description\":\"PERSPICIATIS VOLUPTATEM LABORE UT REM QUIA ASPERIORES ARCHITECTO.\",\"rma_return_number\":\"e6b7b96b-f66a-3019-ba1b-1c537dab511e\",\"order_status_name\":\"Returned\",\"billing_status_name\":\"Billed\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Totam voluptas est perspiciatis tempora vero adipisci vero enim.\",\"customer_return_note\":\"Qui ea eum nemo animi voluptas quaerat.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8005,\"order_number\":11,\"manufacturer_name\":\"Pfannerstill, Rodriguez and Gerlach\",\"quantity\":5,\"quantity_received\":0,\"price\":5.68,\"part_number\":\"BWOSOQ5Q\",\"job_number\":\"Heather Zieme\",\"user_ordered_timestamp\":\"2020-05-11T00:00:00+00:00\",\"date_needed\":\"2020-05-18T00:00:00+00:00\",\"date_needed_is_ground\":false,\"date_expected\":\"2020-05-15T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":false,\"return_requested_customer_timestamp\":null,\"description\":\"EXCEPTURI SIMILIQUE CONSEQUUNTUR VEL ET.\",\"rma_return_number\":\"a8fce1c6-d327-383f-9786-ec640c081a44\",\"order_status_name\":\"Ordered\",\"billing_status_name\":\"Billed\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Repudiandae illum ut voluptatem accusamus nesciunt voluptatem laboriosam.\",\"customer_return_note\":\"Numquam possimus sequi consequatur nihil eos culpa.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8006,\"order_number\":12,\"manufacturer_name\":\"Ullrich Group\",\"quantity\":5,\"quantity_received\":0,\"price\":10.74,\"part_number\":\"DEXYQXRLCDJ\",\"job_number\":\"Emma Kunze\",\"user_ordered_timestamp\":\"2020-05-15T00:00:00+00:00\",\"date_needed\":\"2020-05-22T00:00:00+00:00\",\"date_needed_is_ground\":true,\"date_expected\":\"2020-05-21T00:00:00+00:00\",\"date_received\":\"2020-05-27T00:00:00+00:00\",\"is_for_stock\":true,\"return_requested_customer_timestamp\":null,\"description\":\"QUI VOLUPTATES PROVIDENT PRAESENTIUM OCCAECATI UT REPREHENDERIT VOLUPTATIBUS.\",\"rma_return_number\":\"ecef874d-ded4-3d02-9119-3793331c9443\",\"order_status_name\":\"Received\",\"billing_status_name\":\"Unbilled\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Velit ipsum aut id cumque aut exercitationem maxime.\",\"customer_return_note\":\"Eveniet doloremque aut aspernatur.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8007,\"order_number\":13,\"manufacturer_name\":\"Lindgren, Pfeffer and Bechtelar\",\"quantity\":2,\"quantity_received\":0,\"price\":18.45,\"part_number\":\"RLBNEPZV52K\",\"job_number\":\"Ines Sawayn\",\"user_ordered_timestamp\":\"2020-05-13T00:00:00+00:00\",\"date_needed\":\"2020-05-20T00:00:00+00:00\",\"date_needed_is_ground\":false,\"date_expected\":\"2020-05-19T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":false,\"return_requested_customer_timestamp\":null,\"description\":\"A SAEPE LABORE IMPEDIT EXERCITATIONEM.\",\"rma_return_number\":\"388f9d3a-fbfd-3b04-9a40-f5e95026daa4\",\"order_status_name\":\"Return Rejected\",\"billing_status_name\":\"Billed\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Quia assumenda similique suscipit consequuntur porro dolor aut.\",\"customer_return_note\":\"Dolores repellat ex culpa magni.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8008,\"order_number\":14,\"manufacturer_name\":\"Bernier Group\",\"quantity\":5,\"quantity_received\":0,\"price\":16.94,\"part_number\":\"RFITMOVQ8ET\",\"job_number\":\"Mr. Dashawn Heathcote PhD\",\"user_ordered_timestamp\":\"2020-05-14T00:00:00+00:00\",\"date_needed\":\"2020-05-21T00:00:00+00:00\",\"date_needed_is_ground\":true,\"date_expected\":\"2020-05-20T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":false,\"return_requested_customer_timestamp\":null,\"description\":\"ERROR DOLOR LABORIOSAM CONSEQUATUR REPREHENDERIT REPELLAT.\",\"rma_return_number\":\"41fdc544-efec-3828-8d12-3546dfb18654\",\"order_status_name\":\"Return Rejected\",\"billing_status_name\":\"Billed\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Autem sunt nisi molestiae ipsum iure voluptate culpa.\",\"customer_return_note\":\"Odit quia occaecati deleniti.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8009,\"order_number\":15,\"manufacturer_name\":\"Bashirian-Ziemann\",\"quantity\":4,\"quantity_received\":0,\"price\":9.6,\"part_number\":\"UHCZSTOGU96\",\"job_number\":\"Ms. Valentine Hartmann PhD\",\"user_ordered_timestamp\":\"2020-05-12T00:00:00+00:00\",\"date_needed\":\"2020-05-19T00:00:00+00:00\",\"date_needed_is_ground\":true,\"date_expected\":\"2020-05-18T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":true,\"return_requested_customer_timestamp\":null,\"description\":\"EA ET QUI QUASI NIHIL QUIBUSDAM ENIM FUGIAT.\",\"rma_return_number\":\"d2cf940b-fcca-3db1-98bf-dfec045cb9bc\",\"order_status_name\":\"Returned\",\"billing_status_name\":\"Unbilled\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Qui veniam sed est minus minus laboriosam.\",\"customer_return_note\":\"Id neque eius odio quasi.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8010,\"order_number\":16,\"manufacturer_name\":\"Lindgren, Pfeffer and Bechtelar\",\"quantity\":5,\"quantity_received\":0,\"price\":6.01,\"part_number\":\"DYNDUQ0XWNN\",\"job_number\":\"Bonita Jacobs\",\"user_ordered_timestamp\":\"2020-05-15T00:00:00+00:00\",\"date_needed\":\"2020-05-22T00:00:00+00:00\",\"date_needed_is_ground\":true,\"date_expected\":\"2020-05-21T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":false,\"return_requested_customer_timestamp\":null,\"description\":\"IN DOLORUM SIT NOBIS ODIO QUAE.\",\"rma_return_number\":\"fc132836-3061-3b1c-a1fc-d81614ed7d60\",\"order_status_name\":\"Returned\",\"billing_status_name\":\"Billed\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Repudiandae consequatur quo tenetur soluta eligendi ipsum.\",\"customer_return_note\":\"Dolor nihil qui est.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8011,\"order_number\":17,\"manufacturer_name\":\"Rodriguez-Jakubowski\",\"quantity\":1,\"quantity_received\":0,\"price\":8.94,\"part_number\":\"PDNQJIO3\",\"job_number\":\"Dallas Langosh\",\"user_ordered_timestamp\":\"2020-05-11T00:00:00+00:00\",\"date_needed\":\"2020-05-18T00:00:00+00:00\",\"date_needed_is_ground\":false,\"date_expected\":\"2020-05-15T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":true,\"return_requested_customer_timestamp\":null,\"description\":\"SINT ESSE PORRO ID OFFICIA AT SIT.\",\"rma_return_number\":\"e4c67783-2a9b-3fa4-ad6b-19858d3cea21\",\"order_status_name\":\"Ordered\",\"billing_status_name\":\"Unbilled\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Sit sit a et et.\",\"customer_return_note\":\"Et et alias cumque natus est voluptas.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8012,\"order_number\":18,\"manufacturer_name\":\"Ullrich Group\",\"quantity\":2,\"quantity_received\":0,\"price\":5.32,\"part_number\":\"ZGYGANHS\",\"job_number\":\"Elisha McCullough III\",\"user_ordered_timestamp\":\"2020-05-14T00:00:00+00:00\",\"date_needed\":\"2020-05-21T00:00:00+00:00\",\"date_needed_is_ground\":true,\"date_expected\":\"2020-05-20T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":true,\"return_requested_customer_timestamp\":null,\"description\":\"MODI VOLUPTATUM DOLORES VOLUPTATEM LABORE AUTEM ENIM.\",\"rma_return_number\":\"782369d8-0063-3c36-af5a-14c7b973f9dd\",\"order_status_name\":\"Pending Review\",\"billing_status_name\":\"Unbilled\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Quia rem quae sint numquam provident corporis assumenda ipsam.\",\"customer_return_note\":\"Repudiandae et est occaecati temporibus placeat aut impedit.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8013,\"order_number\":19,\"manufacturer_name\":\"Bashirian-Ziemann\",\"quantity\":2,\"quantity_received\":0,\"price\":11.53,\"part_number\":\"OVXKFQ27LK6\",\"job_number\":\"Marco Fisher\",\"user_ordered_timestamp\":\"2020-05-10T00:00:00+00:00\",\"date_needed\":\"2020-05-15T00:00:00+00:00\",\"date_needed_is_ground\":false,\"date_expected\":\"2020-05-14T00:00:00+00:00\",\"date_received\":\"2020-05-19T00:00:00+00:00\",\"is_for_stock\":true,\"return_requested_customer_timestamp\":null,\"description\":\"ODIT REPUDIANDAE QUI QUASI ACCUSANTIUM VOLUPTATEM ACCUSANTIUM CONSEQUUNTUR.\",\"rma_return_number\":\"5e0f03ac-44ab-3fd5-bd54-2dd4b1916d83\",\"order_status_name\":\"Received\",\"billing_status_name\":\"Unbilled\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"In beatae quibusdam harum reiciendis repellat et autem ab.\",\"customer_return_note\":\"Modi totam qui quae consequuntur necessitatibus aut distinctio.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8014,\"order_number\":20,\"manufacturer_name\":\"Bashirian-Ziemann\",\"quantity\":3,\"quantity_received\":0,\"price\":13.06,\"part_number\":\"VGFNSKV9\",\"job_number\":\"Tremaine Satterfield\",\"user_ordered_timestamp\":\"2020-05-18T00:00:00+00:00\",\"date_needed\":\"2020-05-26T00:00:00+00:00\",\"date_needed_is_ground\":true,\"date_expected\":\"2020-05-22T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":false,\"return_requested_customer_timestamp\":null,\"description\":\"PRAESENTIUM ET IMPEDIT FUGA QUISQUAM ACCUSAMUS RECUSANDAE QUI.\",\"rma_return_number\":\"b7cb58c9-2606-33d0-bb35-4929346ca1a5\",\"order_status_name\":\"Backordered\",\"billing_status_name\":\"Billed\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Enim et accusamus molestiae ea saepe rem.\",\"customer_return_note\":\"Eveniet officiis mollitia sit vel commodi enim fugit.\",\"customer_internal_memo\":null,\"serial_numbers\":[],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}," +
                "{\"order_line_id\":8216,\"order_number\":21,\"manufacturer_name\":\"Lindgren, Pfeffer and Bechtelar\",\"quantity\":1,\"quantity_received\":0,\"price\":5.26,\"part_number\":\"WOOEPA8R\",\"job_number\":\"Trey Schneider\",\"user_ordered_timestamp\":\"2020-05-15T00:00:00+00:00\",\"date_needed\":\"2020-05-22T00:00:00+00:00\",\"date_needed_is_ground\":false,\"date_expected\":\"2020-05-21T00:00:00+00:00\",\"date_received\":null,\"is_for_stock\":true,\"return_requested_customer_timestamp\":null,\"description\":\"VOLUPTAS NOBIS AB FUGIT QUO.\",\"rma_return_number\":\"3d1c97b9-a33a-3f2a-84ae-d425b4be22aa\",\"order_status_name\":\"Returned\",\"billing_status_name\":\"Credited\",\"vendor_confirmation_number\":null,\"shipping_address\":{\"name\":\"Some address\",\"city\":\"Medford\",\"state\":\"OR\",\"postalcode\":\"97504\",\"street_address\":\"2000 Water Ln\"},\"cost_center\":\"Spinkaville\",\"customer_note\":\"Ut illum eum expedita quo qui.\",\"customer_return_note\":\"Quibusdam rerum mollitia dolor dicta nemo.\",\"customer_internal_memo\":null,\"serial_numbers\":[1,2,3],\"tracking_numbers\":[],\"return_tracking_numbers\":[]}" +
                "]" +
                ",\"links\":{\"first\":\"https:\\/\\/nephalim-api.empire-medical.com\\/my\\/facility\\/order-lines?page=1\",\"last\":\"https:\\/\\/nephalim-api.empire-medical.com\\/my\\/facility\\/order-lines?page=2\",\"prev\":null,\"next\":\"https:\\/\\/nephalim-api.empire-medical.com\\/my\\/facility\\/order-lines?page=2\"},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":2,\"path\":\"https:\\/\\/nephalim-api.empire-medical.com\\/my\\/facility\\/order-lines\",\"per_page\":200,\"to\":200,\"total\":200}}\n";
    }

    public static Template getTemplate() {
        Template template = new Template();
        template.setId(1L);
        template.setName("Custom AFO ORX Eval");
        template.setDetails("<p>@{pt_full_name} ,&nbsp; @{pt_date_of_birth} , was seen today at&nbsp; @{pt_branch}  @{pta_last_visit_date}&nbsp; on referral from&nbsp; @{pp_referring_physician}&nbsp; for&nbsp; @{pta_last_visit_type}&nbsp; regarding&nbsp; @{pt_his_or_her}&nbsp;&nbsp; @{pp_device_type} .&nbsp;&nbsp;&nbsp; @{pt_full_name}&nbsp; presents today with Rx, \"___________\", necessary to manage diagnosis, \"________\". Patient presents as a&nbsp; @{pt_age} , (sex),&nbsp;&nbsp; @{pt_height} ,&nbsp; @{pt_weight} . Patient was accompanied by _____________.<br><br>History:</p><p>@{pp_l_codes}&nbsp;<br></p>");
        template.setType("admin_notes");
        template.setTrack("");
        template.setFormType("");
        template.setVisitType(null);
        template.setCreatedAt(new Timestamp(0L));
        template.setUpdatedAt(new Timestamp(0L));
        template.setCreatedById(1L);
        return template;
    }

    public static Contact getContact() {
        Contact contact = new Contact();
        contact.setId(1L);
        contact.setCompany("company");
        contact.setWebPage("webPage");
        contact.setLastName("lastName");
        contact.setFirstName("firstName");
        contact.setMiddleName("middleName");
        contact.setEmail("email");
        contact.setJobTitle("jobTitle");
        contact.setAddressType("addressType");
        contact.setStreetAddress("streetAddress");
        contact.setCity("city");
        contact.setState("state");
        contact.setZipcode("11111");
        return contact;
    }

    public static CriticalMessage getCriticalMessage() {
        CriticalMessage cm = new CriticalMessage();
        cm.setMessage("This is a test");
        cm.setUserId(Fake.ID);
        cm.setPatientId(Fake.ID3);
        cm.setDate(java.sql.Date.valueOf(LocalDate.now()));
        return cm;
    }

    public static Item getItem() {
        ItemByManufacturer ibm = new ItemByManufacturer();
        ibm.setId(1L);
        ibm.setName("name");
        ibm.setPartNumber("Part321");
        ibm.setMsrp(BigDecimal.valueOf(1000.00));
        Item item = new Item();
        item.setId(1L);
        item.setSku("SKU123");
        item.setActive(true);
        item.setItemByManufacturer(ibm);
        item.setItemByManufacturerId(ibm.getId());
        return item;
    }

    public static List<Item> getItemList() {
        return Collections.singletonList(getItem());
    }

    public static Appointment getAppointment() {
        Appointment appointment = new Appointment();
        appointment.setId(Fake.ID2);
        appointment.setRecurring(false);
        appointment.setAppointmentTypeId(1003L);
        appointment.setBranchId(Fake.ID);
        appointment.setConfirmationCode("111111111");
        appointment.setConfirmationSent(false);
        appointment.setStatus("unconfirmed");
        appointment.setBranchId(getBranch().getId());
        appointment.setBranch(getBranch());
        appointment.setPatientId(getPatient().getId());
        appointment.setPatient(getPatient());
//        appointment.setRescheduledFrom(0L);
//        appointment.setRescheduledTo(0L);
        appointment.setNotes("<p>Prosthetics</p>");
        appointment.setStartDateTime(OffsetDateTime.of(2020, 01, 01, 14, 0, 0, 0, ZoneOffset.ofHours(0)));
        appointment.setEndDateTime(OffsetDateTime.of(2020, 01, 01, 15, 0, 0, 0, ZoneOffset.ofHours(0)));
        return appointment;
    }

    public static List<Appointment> getAppointmentList() {
        return Collections.singletonList(getAppointment());
    }

    public static AppointmentType getAppointmentType() {
        AppointmentType appointmentType = new AppointmentType();
        appointmentType.setName("Mine");
        appointmentType.setActive(true);
        appointmentType.setSurvey_link("Appointment Type Survey Link");
        return appointmentType;
    }

    public static ClaimAdjustmentGroupCode getClaimAdjustmentGroupCode() {
        ClaimAdjustmentGroupCode claimAdjustmentGroupCode = new ClaimAdjustmentGroupCode();
        claimAdjustmentGroupCode.setId("1");
        claimAdjustmentGroupCode.setCode("CO");
        claimAdjustmentGroupCode.setName("Contractual Obligation");
        return claimAdjustmentGroupCode;
    }

    public static List<ClaimAdjustmentGroupCode> getClaimAdjustmentGroupCodeList() {
        return Collections.singletonList(getClaimAdjustmentGroupCode());
    }

    public static EraAdjustmentReasonCode getClaimAdjustmentReasonCode() {
        EraAdjustmentReasonCode claimAdjustmentReasonCode = new EraAdjustmentReasonCode();
        claimAdjustmentReasonCode.setId(1L);
        claimAdjustmentReasonCode.setCode("1");
        claimAdjustmentReasonCode.setActive(true);
        claimAdjustmentReasonCode.setAffectsBalance(false);
        claimAdjustmentReasonCode.setDescription("Deductible Amount");
        claimAdjustmentReasonCode.setUsage("Usage: This amount will not be included in the ending balance on this payment, as it will be passed on to the the next payer in the chain.");
        return claimAdjustmentReasonCode;
    }

    public static SystemSetting getTextReminderUseDeliveryLocationSetting() {
        SystemSetting textReminderUseDeliveryLocation = new SystemSetting();
        textReminderUseDeliveryLocation.setId(0L);
        textReminderUseDeliveryLocation.setSection("scheduling");
        textReminderUseDeliveryLocation.setField("text_reminder_use_delivery_location");
        textReminderUseDeliveryLocation.setValue("Y");
        return textReminderUseDeliveryLocation;
    }

    public static List<SystemSetting> getClaimSettings() {
        List<SystemSetting> results = new ArrayList<>();
        results.add(getSystemSetting(1L, "insured_id_number", "claim", "leave_blank"));
        results.add(getSystemSetting(2L, "patient_signature", "claim", "Leave Blank"));
        results.add(getSystemSetting(3L, "insured_signature", "claim", "Leave Blank"));
        results.add(getSystemSetting(4L, "show_accident_date", "claim", "box_14"));
        results.add(getSystemSetting(5L, "physician_to_use", "claim", "referring_physician"));
        results.add(getSystemSetting(6L, "physician_qualifier", "claim", "DN"));
        results.add(getSystemSetting(7L, "physician_id_to_use", "claim", ""));
        results.add(getSystemSetting(8L, "physician_id_qualifier", "claim", ""));
        results.add(getSystemSetting(9L, "place_of_service", "claim", "12"));
        results.add(getSystemSetting(10L, "rendering_provider_npi", "claim", "DN"));
        results.add(getSystemSetting(11L, "rendering_provider_other_id", "claim", "branch_npi"));
        results.add(getSystemSetting(12L, "rendering_provider_other_id_qualifier", "claim", "1c"));
        results.add(getSystemSetting(13L, "tax_id_to_use", "claim", "ein"));
        results.add(getSystemSetting(14L, "tax_id_number", "claim", "**********"));
        results.add(getSystemSetting(32L, "facility_taxonomy", "claim", ""));
        results.add(getSystemSetting(33L, "billing_taxonomy", "claim", ""));
        results.add(getSystemSetting(34L, "provider_information", "claim", "facility_information"));
        results.add(getSystemSetting(40L, "format_837", "claim", "x12"));
        results.add(getSystemSetting(57L, "diagnosis_code_type", "claim", "icd-10"));
        results.add(getSystemSetting(62L, "use_icb_name_for_hcfa", "claim", "N"));
        results.add(getSystemSetting(76L, "use_single_claim", "claim", "Y"));
        results.add(getSystemSetting(90L, "use_rental_auto_bill", "claim", "N"));
        return results;
    }

    public static Map<String, String> getClaimDefaults() {
        Map<String, String> map = new HashMap<>();
        map.put("insured_id_number", "leave_blank");
        map.put("patient_signature", "Leave Blank");
        map.put("insured_signature", "Leave Blank");
        map.put("show_accident_date", "box_14");
        map.put("physician_to_use", "referring_physician");
        map.put("physician_qualifier", "DN");
        map.put("physician_id_to_use", "");
        map.put("physician_id_qualifier", "");
        map.put("place_of_service", "12");
        map.put("rendering_provider_npi", "DN");
        map.put("rendering_provider_other_id", "branch_npi");
        map.put("rendering_provider_other_id_qualifier", "1c");
        map.put("tax_id_to_use", "ein");
        map.put("tax_id_number", "**********");
        map.put("facility_taxonomy", "");
        map.put("billing_taxonomy", "");
        map.put("provider_information", "facility_information");
        map.put("format_837", "x12");
        map.put("diagnosis_code_type", "icd-10");
        map.put("use_icb_name_for_hcfa", "Y");
        map.put("use_single_claim", "Y");
        map.put("use_rental_auto_bill", "N");
        return map;
    }

    public Page<T> getFakePageImpl(T object) {
        Pageable pageable = PageRequest.of(0, 1000);
        List<T> l = new ArrayList<>();
        l.add(object);
        return new PageImpl<T>(l, pageable, 1L);
    }

    public Page<T> getFakePageImpl(List<T> objects) {
        Pageable pageable = PageRequest.of(0, 1000);
        List<T> l = new ArrayList<>();
        l.addAll(objects);
        return new PageImpl<T>(l, pageable, 1L);
    }

    public static Item getItem(Long id, int i) {
        Item item = new Item();
        item.setActive(true);
        item.setId(id + i);
        item.setSku("SKU123" + i);
        ItemByManufacturer ibm = Fake.getItemByManufacturer(id, i);
        item.setItemByManufacturer(ibm);
        item.setItemByManufacturerId(ibm.getId());
        item.setVendorId(ibm.getManufacturerId());
        return item;
    }

    public static ItemByManufacturer getItemByManufacturer(Long id, int i) {
        ItemByManufacturer ibm = new ItemByManufacturer();
        ibm.setId(id + i);
        ibm.setManufacturerId(Fake.ID4);
        ibm.setName("IBM_" + i);
        ibm.setPartNumber("A1B2C" + i);
        return ibm;
    }

    public static ItemPhysical getItemPhysical(Long id, int i) {
        ItemPhysical ip = new ItemPhysical();
        Branch b = getBranch();
        ip.setBranch(b);
        ip.setBranchId(b.getId());
        ip.setId(id + i);
        /*
        ip.setPatient(getPatient());
        ip.setPatientId(getPatient().getId());
        ip.setPrescription(getPrescription());
        ip.setPrescriptionId(getPrescription().getId());
        ip.setPrescriptionLCodeId(getPrescription_l_code().getId());
        ip.setPrescriptionLCode(getPrescription_l_code());
        */
        ip.setDepreciationUnitsToZero(5L);
        ip.setDepreciationUnitsCount(13L);
        ip.setInitialValue(BigDecimal.valueOf(100.00));
        Item ibv = Fake.getItem(id, i);
        ip.setItem(ibv);
        ip.setItemId(ibv.getId());
        return ip;
    }

    public static Vendor getVendor() {
        Vendor v = new Vendor();
        v.setId(1L);
        v.setName("TEST VENDOR");
        v.setVendorCustomerId("123456");
        return v;
    }

    private static SystemSetting getSystemSetting(Long id, String field, String section, String value) {
        SystemSetting ss = new SystemSetting();
        ss.setId(id);
        ss.setSection(section);
        ss.setField(field);
        ss.setValue(value);
        return ss;
    }

    public static DCPayment createDCPayment() {
        ProjectionFactory factory = new SpelAwareProxyProjectionFactory();
        DCPayment result = factory.createProjection(DCPayment.class);
        result.setPaymentId(1L);
        result.setBranchId(1L);
        result.setBranchName("Test Branch");
        result.setPatientId(1L);
        result.setPaymentDate(Date.valueOf("2019-01-01"));
        result.setDepositDate(Date.valueOf("2019-01-01"));
        result.setPatientName("F L");
        result.setPayerType("insurance_company");
        result.setPaymentType("insurance_payment_credit_card");
        result.setDescription(null);
        result.setCheckNumber("3333333");
        result.setPaymentApplied(BigDecimal.valueOf(192.22));
        result.setPaymentUnapplied(BigDecimal.ZERO);
        result.setPaymentAmount(BigDecimal.valueOf(192.22));
        result.setAdjustmentAmount(BigDecimal.ZERO);
        result.setAdjustmentApplied(BigDecimal.ZERO);
        result.setAdjustmentUnapplied(BigDecimal.ZERO);
        result.setAdjustmentTypeId(null);
        result.setWithdraw(null);
        result.setPaymentTypeOrigin(null);
        result.setPayerName("Good Healthcare");
        result.setCreatedById(1L);
        result.setCreatedByName("TEST");
        result.setAppliedDate(Date.valueOf("2019-01-01"));
        return result;
    }

    public static DCPayment createAppliedDCPayment() {
        ProjectionFactory factory = new SpelAwareProxyProjectionFactory();
        DCPayment result = factory.createProjection(DCPayment.class);
        result.setPaymentId(1L);
        result.setBranchId(1L);
        result.setBranchName("Test Branch");
        result.setPatientId(null);
        result.setPaymentDate(Date.valueOf("2019-01-01"));
        result.setDepositDate(Date.valueOf("2019-01-01"));
        result.setPatientName("F L");
        result.setPayerType("insurance_company");
        result.setPaymentType("insurance_payment_credit_card");
        result.setDescription("");
        result.setCheckNumber("3333333");
        result.setPaymentApplied(BigDecimal.TEN);
        result.setPaymentUnapplied(BigDecimal.TEN);
        result.setPaymentAmount(BigDecimal.valueOf(20.00));
        result.setAdjustmentAmount(BigDecimal.ZERO);
        result.setAdjustmentApplied(BigDecimal.ZERO);
        result.setAdjustmentUnapplied(BigDecimal.ZERO);
        result.setAdjustmentTypeId(null);
        result.setWithdraw(null);
        result.setPaymentTypeOrigin("");
        result.setPayerName("Good Healthcare");
        result.setCreatedById(1L);
        result.setCreatedByName("TEST");
        result.setAppliedDate(Date.valueOf("2019-01-01"));
        return result;
    }
}
