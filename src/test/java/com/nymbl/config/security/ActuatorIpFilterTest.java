package com.nymbl.config.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.io.PrintWriter;

import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class ActuatorIpFilterTest {

    @Mock
    private HttpServletRequest mockRequest;

    @Mock
    private HttpServletResponse mockResponse;

    @Mock
    private FilterChain mockFilterChain;

    @Mock
    private PrintWriter mockPrintWriter;

    private ActuatorIpFilter actuatorIpFilterUnderTest;

    @BeforeEach
    void setUp() throws IOException, ServletException {
        openMocks(this);
        actuatorIpFilterUnderTest = new ActuatorIpFilter();
        
        // Setup default values
        ReflectionTestUtils.setField(actuatorIpFilterUnderTest, "allowedCidr", "**********/16");
        ReflectionTestUtils.setField(actuatorIpFilterUnderTest, "ipFilterEnabled", true);
        
        // Initialize the filter to set up IP matchers
        actuatorIpFilterUnderTest.initFilterBean();
        
        when(mockResponse.getWriter()).thenReturn(mockPrintWriter);
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "/api/users", "/api/test", "/uploads/file", "/logs/app.log", 
        "/auth/login", "/test/endpoint", "/console/admin"
    })
    void testDoFilterInternal_NonActuatorPaths_ShouldAlwaysPassThrough(String requestPath) throws ServletException, IOException {
        // Setup
        when(mockRequest.getRequestURI()).thenReturn(requestPath);

        // Run the test
        actuatorIpFilterUnderTest.doFilterInternal(mockRequest, mockResponse, mockFilterChain);

        // Verify
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verifyNoInteractions(mockResponse);
    }

    @ParameterizedTest
    @CsvSource({
        "/actuator/health, *************",
        "/actuator/health/liveness, ********", 
        "/actuator/health/readiness, ***********",
        "/actuator/info, ************",
        "/actuator/health, **********00",
        "/actuator/info, **********00"
    })
    void testDoFilterInternal_HealthAndInfoEndpoints_ShouldAlwaysBeAccessible(String requestPath, String clientIp) throws ServletException, IOException {
        // Setup
        when(mockRequest.getRequestURI()).thenReturn(requestPath);
        when(mockRequest.getRemoteAddr()).thenReturn(clientIp);

        // Run the test
        actuatorIpFilterUnderTest.doFilterInternal(mockRequest, mockResponse, mockFilterChain);

        // Verify - health and info endpoints should always pass through regardless of IP
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verifyNoInteractions(mockResponse);
    }

    @ParameterizedTest
    @CsvSource({
        "/actuator/metrics, **********00",
        "/actuator/metrics, **********", 
        "/actuator/env, **************",
        "/actuator/beans, ***********"
    })
    void testDoFilterInternal_AllowedIPs_ShouldPassThrough(String requestPath, String clientIp) throws ServletException, IOException {
        // Setup
        when(mockRequest.getRequestURI()).thenReturn(requestPath);
        when(mockRequest.getRemoteAddr()).thenReturn(clientIp);

        // Run the test
        actuatorIpFilterUnderTest.doFilterInternal(mockRequest, mockResponse, mockFilterChain);

        // Verify
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verifyNoInteractions(mockResponse);
    }

    @ParameterizedTest
    @CsvSource({
        "/actuator/metrics, *************, IP not in allowed range",
        "/actuator/env, ***********00, IP not in allowed range", 
        "/actuator/metrics, *************, IP not in allowed range",
        "/actuator/beans, ********, IP not in allowed range",
        "/actuator/metrics, NULL, IP not in allowed range",
        "/actuator/metrics, **********, IP not in allowed range",
        "/actuator/metrics, ***********, IP not in allowed range"
    })
    void testDoFilterInternal_BlockedAccess_ShouldDeny(String requestPath, String clientIp, String expectedMessage) throws ServletException, IOException {
        // Setup
        when(mockRequest.getRequestURI()).thenReturn(requestPath);
        when(mockRequest.getRemoteAddr()).thenReturn("NULL".equals(clientIp) ? null : clientIp);

        // Run the test
        actuatorIpFilterUnderTest.doFilterInternal(mockRequest, mockResponse, mockFilterChain);

        // Verify
        verify(mockResponse).setStatus(HttpServletResponse.SC_FORBIDDEN);
        verify(mockPrintWriter).write("Access denied: " + expectedMessage);
        verifyNoInteractions(mockFilterChain);
    }

    @Test
    void testDoFilterInternal_FilterDisabled_ShouldAlwaysPassThrough() throws ServletException, IOException {
        // Setup
        ReflectionTestUtils.setField(actuatorIpFilterUnderTest, "ipFilterEnabled", false);
        when(mockRequest.getRequestURI()).thenReturn("/actuator/metrics");
        when(mockRequest.getRemoteAddr()).thenReturn("*************"); // Would normally be blocked

        // Run the test
        actuatorIpFilterUnderTest.doFilterInternal(mockRequest, mockResponse, mockFilterChain);

        // Verify
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verifyNoInteractions(mockResponse);
    }

    @ParameterizedTest
    @CsvSource({
        "'', No authorized IP ranges configured",
        "invalid-cidr-format, No authorized IP ranges configured"
    })
    void testDoFilterInternal_InvalidConfiguration_ShouldDenyAccess(String cidr, String expectedMessage) throws ServletException, IOException {
        // Setup
        ReflectionTestUtils.setField(actuatorIpFilterUnderTest, "allowedCidr", cidr);
        actuatorIpFilterUnderTest.initFilterBean(); // Reinitialize after changing CIDR
        when(mockRequest.getRequestURI()).thenReturn("/actuator/metrics");
        when(mockRequest.getRemoteAddr()).thenReturn("*************");

        // Run the test
        actuatorIpFilterUnderTest.doFilterInternal(mockRequest, mockResponse, mockFilterChain);

        // Verify
        verify(mockResponse).setStatus(HttpServletResponse.SC_FORBIDDEN);
        verify(mockPrintWriter).write("Access denied: " + expectedMessage);
        verifyNoInteractions(mockFilterChain);
    }

    @Test
    void testDoFilterInternal_MultipleCidrRanges_ShouldMatchAny() throws ServletException, IOException {
        // Setup
        ReflectionTestUtils.setField(actuatorIpFilterUnderTest, "allowedCidr", "10.0.0.0/8,**********/16,***********/16");
        actuatorIpFilterUnderTest.initFilterBean();
        when(mockRequest.getRequestURI()).thenReturn("/actuator/metrics");
        when(mockRequest.getRemoteAddr()).thenReturn("*************"); // Matches third range

        // Run the test
        actuatorIpFilterUnderTest.doFilterInternal(mockRequest, mockResponse, mockFilterChain);

        // Verify
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verifyNoInteractions(mockResponse);
    }

    @Test
    void testDoFilterInternal_ExactIpAddress_ShouldWork() throws ServletException, IOException {
        // Setup - test single IP address (no CIDR notation)
        ReflectionTestUtils.setField(actuatorIpFilterUnderTest, "allowedCidr", "**********00");
        actuatorIpFilterUnderTest.initFilterBean();
        when(mockRequest.getRequestURI()).thenReturn("/actuator/metrics");
        when(mockRequest.getRemoteAddr()).thenReturn("**********00");

        // Run the test
        actuatorIpFilterUnderTest.doFilterInternal(mockRequest, mockResponse, mockFilterChain);

        // Verify
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verifyNoInteractions(mockResponse);
    }

    @Test
    void testDoFilterInternal_HeadersIgnored_ShouldUseRemoteAddr() throws ServletException, IOException {
        // Setup - filter should ignore X-Forwarded-For headers for security
        when(mockRequest.getRequestURI()).thenReturn("/actuator/metrics");
        when(mockRequest.getHeader("X-Forwarded-For")).thenReturn("**********00"); // Allowed IP in header
        when(mockRequest.getRemoteAddr()).thenReturn("**********"); // Allowed IP in remote addr

        // Run the test
        actuatorIpFilterUnderTest.doFilterInternal(mockRequest, mockResponse, mockFilterChain);

        // Verify - should use remote address and allow access
        verify(mockFilterChain).doFilter(mockRequest, mockResponse);
        verifyNoInteractions(mockResponse);
    }
} 