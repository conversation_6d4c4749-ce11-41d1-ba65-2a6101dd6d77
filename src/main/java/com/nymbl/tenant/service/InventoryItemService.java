package com.nymbl.tenant.service;

import com.google.common.base.Strings;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.InventoryItemDTORepository;
import com.nymbl.tenant.repository.InventoryItemRepository;
import com.nymbl.tenant.repository.PurchaseOrder_ItemRepository;
import com.nymbl.tenant.repository.ShoppingCartRepository;
import com.nymbl.tenant.specification.InventoryItemSpecs;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
public class InventoryItemService extends AbstractTableService<InventoryItem, Long> {

    private final BranchService branchService;
    private final InventoryItemDTORepository inventoryItemDTORepository;
    private final InventoryItemRepository inventoryItemRepository;
    private final ItemPhysicalService itemPhysicalService;
    private final ItemService itemService;
    private final PurchaseOrder_ItemRepository purchaseOrder_ItemRepository;
    private final ShoppingCartRepository shoppingCartRepository;

    private final int MAX_LINES_TO_IMPORT = 1000;

    @Autowired
    public InventoryItemService(@Lazy BranchService branchService,
                                @Lazy InventoryItemDTORepository inventoryItemDTORepository,
                                @Lazy InventoryItemRepository inventoryItemRepository,
                                @Lazy ItemPhysicalService itemPhysicalService,
                                @Lazy ItemService itemService,
                                PurchaseOrder_ItemRepository purchaseOrder_ItemRepository,
                                ShoppingCartRepository shoppingCartRepository) {
        super(inventoryItemRepository);
        this.branchService = branchService;
        this.inventoryItemDTORepository = inventoryItemDTORepository;
        this.inventoryItemRepository = inventoryItemRepository;
        this.itemPhysicalService = itemPhysicalService;
        this.itemService = itemService;
        this.purchaseOrder_ItemRepository = purchaseOrder_ItemRepository;
        this.shoppingCartRepository = shoppingCartRepository;
    }

    public List<InventoryItem> addToInventory(Long branchId, Long itemId) {
        List<InventoryItem> addedInventoryItems = new ArrayList<>();
        List<Long> branchIds = new ArrayList<>();
        if (branchId != null && branchId > 0) {
            branchIds.add(branchId);
        } else {
            branchIds = branchService.findAllByActiveTrue().stream().map(Branch::getId).toList();
        }
        for (Long bId : branchIds) {
            InventoryItem existingInventoryItem = inventoryItemRepository.findByBranchIdAndItemId(bId, itemId, false);
            if (existingInventoryItem == null) {
                InventoryItem inventoryItem = populateInventoryItem(bId, itemId);
                InventoryItem newInventoryItem = save(inventoryItem);
                addedInventoryItems.add(newInventoryItem);
            }
        }
        return addedInventoryItems;
    }

    public List<InventoryItem> findByItemId(Long itemId, Boolean nonZeroOnly) {
        List<InventoryItem> inventoryItems = (nonZeroOnly != null && nonZeroOnly)
                ? inventoryItemRepository.findByItemIdNonZero(itemId)
                : inventoryItemRepository.findByItemId(null, itemId, true);
        return inventoryItems;
    }

    /**
     * Find a single InventoryItem using its branchId and itemId
     *
     * @param itemId
     * @param branchId
     * @return single InventoryItem
     */
    public InventoryItem findByItemIdAndBranchId(Long branchId, Long itemId, Boolean includeUndefined) {
        InventoryItem inventoryItem = inventoryItemRepository.findByBranchIdAndItemId(branchId, itemId, includeUndefined);
        return inventoryItem;
    }

    public List<InventoryItem> findByItemIdAndBranchName(String branchName, Boolean includeUndefined, Long itemId) {
        if (itemId == null || itemId <= 0) {
            return null;
        }
        List<Long> branchIds = null;
        if (!Strings.isNullOrEmpty(branchName)) {
            branchIds = branchService.findAllBranchIdsByName(branchName);
            if (branchIds != null && branchIds.isEmpty()) {
                branchIds = null;
            }
        }
        if (includeUndefined != null && !includeUndefined) {
            includeUndefined = null;
        }
        return inventoryItemRepository.findAllByBranchIdsAndItemId(branchIds, includeUndefined, itemId);
    }

    public String exportInventory(String keyword, Long branchId, Boolean includeUndefined, Boolean useChildBranches, Long max,
                                  Long min, String partNumber, Boolean lowStock, Boolean preferredStock, Boolean needsReStock) {
        InventorySearchParams params = getSearchParams(branchId, includeUndefined, keyword, lowStock, min, max, needsReStock,
                null, partNumber, preferredStock, useChildBranches);
        long totalCount = countInventoryItemDTO(params);
        params.pageOffset = 0;
        params.pageSize = totalCount;
        List<InventoryItemDTO> dtoList = searchInventoryItemDTO(params, totalCount);
        if (dtoList == null || dtoList.isEmpty()) {
            return null;
        }
        StringBuilder csv = new StringBuilder();
        csv.append("Item,ID,SKU#,Part Number,Vendor,Manufacturer,Branch,Category,Price,In Stock,Low Stock,Preferred Stock,Branch ID,Item ID\n");
        for (InventoryItemDTO dto : dtoList) {
            csv.append("\"" + (Strings.isNullOrEmpty(dto.getItemName()) ? "" : dto.getItemName().replaceAll("[\",]+", " ").replaceAll("\\s+", " ").trim()));
            csv.append("\"," + (dto.getId() != null ? dto.getId().toString() : ""));
            csv.append(",\"" + (dto.getItemSku() != null ? dto.getItemSku() : ""));
            csv.append("\",\"" + (dto.getItemPartNumber() != null ? dto.getItemPartNumber() : ""));
            csv.append("\",\"" + (dto.getVendorName() != null ? dto.getVendorName() : ""));
            csv.append("\",\"" + (dto.getManufacturerName() != null ? dto.getManufacturerName() : ""));
            csv.append("\",\"" + (dto.getBranchName() != null ? dto.getBranchName() : ""));
            csv.append("\",\"" + (dto.getItemCategoryName() != null ? dto.getItemCategoryName() : ""));
            csv.append("\",\"" + (dto.getItemPrice() != null ? dto.getItemPrice().toString() : ""));
            csv.append("\"," + (dto.getQuantity() != null ? dto.getQuantity().toString() : ""));
            csv.append("," + (dto.getLowStock() != null ? dto.getLowStock().toString() : ""));
            csv.append("," + (dto.getPreferredStock() != null ? dto.getPreferredStock().toString() : ""));
            csv.append("," + (dto.getBranchId() != null ? dto.getBranchId().toString() : ""));
            csv.append("," + (dto.getItemId() != null ? dto.getItemId().toString() : ""));
            csv.append("\n");
        }
        return csv.toString();
    }

    /**
     * Find InventoryItems corresponding to the entries from Excel file.
     * They may be either "defined" (with a valid id) or "undefined" (with a negative id).
     *
     * @param excelValues
     * @return
     */
    private List<InventoryItem> findInventoryItemsToUpdate(List<Long[]> excelValues) {
        List<InventoryItem> inventoryItems = new ArrayList<>();
        // find all "undefined" InventoryItems by their branchId and itemId
        for (Long[] rowValues : excelValues) {
            InventoryItem inventoryItem = findByItemIdAndBranchId(rowValues[1], rowValues[2], true);
            if (inventoryItem != null) {
                inventoryItems.add(inventoryItem);
            }
        }
        if (inventoryItems.size() <= MAX_LINES_TO_IMPORT) {
            // sort InventoryItems alphabetically by name
            return inventoryItems.stream().sorted(Comparator.comparing(ii -> ii.getItem().getName())).toList();
        } else {
            return inventoryItems;
        }
    }

    public Integer getInventoryCount(Long branchId, Long itemId) {
        return inventoryItemRepository.getInventoryCount(branchId, itemId);
    }

    public List<InventoryItem> getLowInventoryStock(Long branchId) {
        InventoryItemSpecs spec = new InventoryItemSpecs(null, branchId, null, null, null, true, false);
        return inventoryItemRepository.findAll(spec);
    }

    public InventoryItem populateInventoryItem(Long branchId, Long itemId) {
        InventoryItem inventoryItem = new InventoryItem();
        inventoryItem.setBranchId(branchId);
        inventoryItem.setItemId(itemId);
        inventoryItem.setQuantity(0L);
        inventoryItem.setLowStock(0L);
        return inventoryItem;
    }

    public void populateNewBranchInventory(Long branchId) {
        List<Item> items = itemService.findAll();
        for (Item item : items) {
            addToInventory(branchId, item.getId());
        }
    }

    private Long readExcelCellAsLong(Cell cell) {
        Long result = null;
        if (cell != null) {
            if (cell.getCellType().equals(CellType.NUMERIC)) {
                result = (long) cell.getNumericCellValue();
            } else {
                String value = cell.getStringCellValue();
                if (!Strings.isNullOrEmpty(value)) {
                    try {
                        result = Long.parseLong(value);
                    } catch (NumberFormatException e) {
                        result = null;
                    }
                }
            }
        }
        return result;
    }

    public List<InventoryItem> readInventoryImport(File file) throws Exception {
        List<InventoryItem> inventoryItems;
        // Read new values from Excel file
        FileInputStream fis = new FileInputStream(file);
        XSSFWorkbook myWorkBook = new XSSFWorkbook(fis);
        XSSFSheet mySheet = myWorkBook.getSheetAt(0);
        int rowCount = mySheet.getLastRowNum();
        if (rowCount > MAX_LINES_TO_IMPORT) {
            InventoryItem ii = new InventoryItem();
            ii.setId(-1L);
            ii.setItemId(-1L);
            Item item = new Item();
            item.setId(-1L);
            item.setItemByManufacturerId(-1L);
            ItemByManufacturer ibm = new ItemByManufacturer();
            ibm.setId(-1L);
            ibm.setName("The file is too large to import interactively, it contains " + rowCount + " rows, while the limit is " + MAX_LINES_TO_IMPORT);
            item.setItemByManufacturer(ibm);
            ii.setItem(item);
            ii.setPreferredStock(Long.valueOf(MAX_LINES_TO_IMPORT));
            ii.setQuantity(Long.valueOf(rowCount));
            inventoryItems = new ArrayList<>();
            inventoryItems.add(ii);
        } else {
            List<Long[]> excelValues = new ArrayList<>();
            for (Row row : mySheet) {
                // branchId and itemId are two most important identifiers, they must be valid
                if (row.getCell(12) == null || row.getCell(12).getCellType().equals(CellType.BLANK) ||
                        readExcelCellAsLong(row.getCell(12)) == null || readExcelCellAsLong(row.getCell(12)) <= 0) {
                    continue;
                }
                // to avoid updating the InventoryItems in the wrong branch, I don't use inventoryItemId
                Long inventoryItemId = null; // readExcelCellAsLong(row.getCell(1));
                Long branchId = readExcelCellAsLong(row.getCell(12));
                Long itemId = readExcelCellAsLong(row.getCell(13));
                Long newLowStock = readExcelCellAsLong(row.getCell(10));
                Long newPreferredStock = readExcelCellAsLong(row.getCell(11));
                Long newQuantity = readExcelCellAsLong(row.getCell(9));
                Long[] rowValues = {inventoryItemId, branchId, itemId, newLowStock, newPreferredStock, newQuantity};
                excelValues.add(rowValues);
            }
            inventoryItems = findInventoryItemsToUpdate(excelValues);
            inventoryItems = updateInventoryItemsFromExcel(inventoryItems, excelValues);
        }
        return inventoryItems;
    }

    public void reloadInventoryOnHand() {
        inventoryItemDTORepository.truncateInventoryOnHand();
        inventoryItemDTORepository.reloadInventoryOnHand();
    }

    /**
     * This method uses various InventoryItemDTORepository methods depending on search scenarios.
     * All of those methods require that the temp table `item_physical_temp` be created first.
     * Therefore, the method itemPhysicalService.createItemPhysicalTempTable() MUST be run at some point before search() is called.
     * @param keyword
     * @param branchId
     * @param includeUndefined
     * @param useChildBranches
     * @param max
     * @param min
     * @param partNumber
     * @param lowStock
     * @param preferredStock
     * @param needsReStock
     * @param pageable
     * @param totalCount
     * @return
     */
    public Page<InventoryItem> search(Long branchId, Boolean includeUndefined, String keyword, Boolean lowStock,
                                      Long max, Long min, Boolean needsReStock, Pageable pageable, String partNumber,
                                      Boolean preferredStock, Long totalCount, Boolean useChildBranches) {
        InventorySearchParams params = getSearchParams(branchId, includeUndefined, keyword, lowStock, min, max,
                needsReStock, pageable, partNumber, preferredStock, useChildBranches);

        if (totalCount == null || totalCount <= 0) {
            totalCount = countInventoryItemDTO(params);
        }
        //System.out.println("Total count: " + totalCount);
        List<InventoryItemDTO> dtoList = searchInventoryItemDTO(params, totalCount);
        List<InventoryItem> inventoryItems = null;
        if (dtoList != null && dtoList.size() > 0) {
            inventoryItems = new ArrayList<>(dtoList.size());
            for (InventoryItemDTO dto : dtoList) {
                inventoryItems.add(convertDtoToInventoryItem(dto));
            }
        }
        return (inventoryItems != null && inventoryItems.size() > 0)
                ? new PageImpl<>(inventoryItems, pageable, totalCount)
                : Page.empty();
    }

    public List<InventoryItem> searchByName(Long branchId, String keyword, Boolean stockOnly, Boolean useChildBranches) {
        Long min = (stockOnly != null && stockOnly) ? 1L : null;
        InventorySearchParams params = getSearchParams(branchId, true, keyword, null, min, null,
                null, null, null, null, useChildBranches);
        params.pageSize = 100;
        List<InventoryItemDTO> dtoList = null;
        if (stockOnly != null && stockOnly) {
            dtoList = inventoryItemDTORepository.searchByNameInStockWithUndefined(params.branchIds, params.branchIdsExclude,
                    params.keywords, params.pageSize, params.pageOffset, params.sku);
        } else {
            long[] itemLimitOffset = getItemLimitOffset(params);
            dtoList = inventoryItemDTORepository.searchByNameWithUndefined(params.branchIds, params.branchIdsExclude,
                    itemLimitOffset[0], itemLimitOffset[1], params.keywords, params.pageSize, itemLimitOffset[2], null, params.sku);
        }
        if (dtoList != null) {
            List<InventoryItem> inventoryItems = new ArrayList<>(dtoList.size());
            for (InventoryItemDTO dto : dtoList) {
                inventoryItems.add(convertDtoToInventoryItem(dto));
            }
            return inventoryItems;
        } else {
            return null;
        }
    }

    // REGION: Overridden methods
    @Override
    public InventoryItem save(InventoryItem inventoryItem) {
        InventoryItem result = null;
        if (inventoryItem == null) return result;
        // Adjusting InventoryItem's quantity means creating new physical items
        Long inventoryItemId = inventoryItem.getId();
        InventoryItem originalInventoryItem = (inventoryItemId != null && inventoryItemId > 0)
                ? inventoryItemRepository.getOne(inventoryItemId)
                : inventoryItemRepository.findByBranchIdAndItemId(inventoryItem.getBranchId(), inventoryItem.getItemId(), true);
        Long tempLong;
        int originalQuantity = 0;

        /**
         * InventoryItem that doesn't exist in the database has a negative id (like -123004506)
         * To save it as a new record, I replace the negative id with null
         */
        if (originalInventoryItem != null) {
            tempLong = originalInventoryItem.getQuantity();
            originalQuantity = tempLong != null ? tempLong.intValue() : 0;
            if (inventoryItemId == null || inventoryItemId <= 0) {
                Long originalInventoryItemId = originalInventoryItem.getId();
                if (originalInventoryItemId != null && originalInventoryItemId > 0) {
                    inventoryItem.setId(originalInventoryItemId);
                } else {
                    inventoryItem.setId(null);
                }
            }
        }
        tempLong = inventoryItem.getQuantity();
        int newQuantity = tempLong != null ? tempLong.intValue() : 0;

        // Quantity difference can be negative
        int inventoryIncrease = newQuantity - originalQuantity;
        if (inventoryIncrease != 0) {
            Long itemId = inventoryItem.getItemId();
            Item item = inventoryIncrease > 0 && itemId != null && itemId > 0
                    ? itemService.getOne(itemId)
                    : null;
            itemPhysicalService.adjustInventoryQuantity(inventoryItem.getBranchId(), item, itemId,
                    inventoryIncrease, "inventory_item_modal");
        }

        // Save InventoryItem's attributes lowStock and preferredStock (and maybe set its id)
        tempLong = inventoryItem.getLowStock();
        int lowStock = tempLong != null ? tempLong.intValue() : -1;
        tempLong = inventoryItem.getPreferredStock();
        int preferredStock = tempLong != null ? tempLong.intValue() : -1;
        if (lowStock >= 0 || preferredStock >= 0) {
            result = super.save(inventoryItem);
        } else {
            result = inventoryItem;
        }

        inventoryItem.setNeedNotification(originalQuantity > lowStock && newQuantity <= lowStock);
        return result;
    }

    public List<InventoryItem> saveAll(InventoryItem[] inventoryItems) {
        List<InventoryItem> result = new ArrayList<>();
        if (inventoryItems != null) {
            for (InventoryItem inventoryItem : inventoryItems) {
                InventoryItem res = save(inventoryItem);
                result.add(res);
            }
        }
        return result;
    }

    @Override
    public void loadForeignKeys(InventoryItem ii) {
        // Load createdBy (if necessary)
    }
    // ENDREGION

    // REGION: Private methods
    private InventoryItem convertDtoToInventoryItem(InventoryItemDTO dto) {
        InventoryItem ii = new InventoryItem();
        ii.setId(dto.getId());
        ii.setBranchId(dto.getBranchId());
        //
        Branch branch = new Branch();
        branch.setId(dto.getBranchId());
        branch.setName(dto.getBranchName());
        //
        ii.setBranch(branch);
        ii.setInCart(dto.getInCart());
        ii.setItemId(dto.getItemId());
        //
        Item item = new Item();
        item.setId(dto.getItemId());
        item.setItemByManufacturerId(dto.getItemByManufacturerId());
        //
        ItemByManufacturer ibm = new ItemByManufacturer();
        ibm.setId(dto.getItemByManufacturerId());
        ibm.setDescription(dto.getItemDescription());
        ibm.setLCodeCategoryId(dto.getItemCategoryId());
        L_CodeCategory lcc = new L_CodeCategory();
        lcc.setId(dto.getItemCategoryId());
        lcc.setCategory(dto.getItemCategoryName());
        ibm.setLCodeCategory(lcc);
        ibm.setManufacturerId(dto.getManufacturerId());
        Vendor manufacturer = new Vendor();
        manufacturer.setId(dto.getManufacturerId());
        manufacturer.setName(dto.getManufacturerName());
        ibm.setManufacturer(manufacturer);
        ibm.setName(dto.getItemName());
        ibm.setPartNumber(dto.getItemPartNumber());
        item.setItemByManufacturer(ibm);
        item.setPrice(dto.getItemPrice());
        item.setSku(dto.getItemSku());
        item.setVendorId(dto.getVendorId());
        Vendor vendor = new Vendor();
        vendor.setId(dto.getVendorId());
        vendor.setName(dto.getVendorName());
        item.setVendor(vendor);
        //
        ii.setItem(item);
        ii.setLowStock(dto.getLowStock());
        ii.setOrdered(dto.getOrdered());
        ii.setBackordered(dto.getBackordered());
        ii.setPreferredStock(dto.getPreferredStock());
        ii.setQuantity(dto.getQuantity());
        //
        return ii;
    }

    /**
     * totalCount will be "cached" by sending it to the UI as page.totalElements, and later receiving it as a search parameter.
     * First time around it is 0, but for successive pages we won't need to query the database again.
     *
     * @param params
     * @return
     */
    private long countInventoryItemDTO(InventoryItemService.InventorySearchParams params) {
        long totalCount;
        if ((params.lowStock != null && params.lowStock) || (params.needsReStock != null && params.needsReStock) ||
                (params.preferredStock != null && params.preferredStock)) {
            // this scenario refers to fields in inventory_item and therefore requires that includeUndefined be false, and min/max be null
            if (!Strings.isNullOrEmpty(params.keywords) || !Strings.isNullOrEmpty(params.partNumber) || !Strings.isNullOrEmpty(params.sku)) {
                totalCount = inventoryItemDTORepository.countByStockValuesFilter(params.branchIds, params.branchIdsExclude,
                        params.keywords, params.lowStock, params.max, params.min, params.needsReStock, params.partNumber,
                        params.preferredStock, params.sku);
            } else {
                totalCount = inventoryItemDTORepository.countByStockValues(params.branchIds, params.branchIdsExclude,
                        params.lowStock, params.max, params.min, params.needsReStock, params.preferredStock);
            }
        } else if (params.min != null && params.min > 0) {
            if (params.includeUndefined != null && params.includeUndefined) {
                // this scenario requires that at least one physical_item record exist
                totalCount = inventoryItemDTORepository.countByMinMaxWithUndefined(params.branchIds, params.branchIdsExclude,
                        params.keywords, params.max, params.min, params.partNumber, params.sku);
            } else {
                // this scenario requires that at least one physical_item record exist
                totalCount = inventoryItemDTORepository.countByMinMax(params.branchIds, params.branchIdsExclude, params.keywords,
                        params.max, params.min, params.partNumber, params.sku);
            }
        } else if (params.max != null && params.max == 0) {
            // because this scenario requires that there are no physical_item records, all other numeric parameters are irrelevant
            if (params.includeUndefined != null && params.includeUndefined) {
                totalCount = inventoryItemDTORepository.countByOutOfStockWithUndefined(params.branchIds, params.branchIdsExclude,
                        params.keywords, params.partNumber, params.sku);
            } else {
                totalCount = inventoryItemDTORepository.countByOutOfStock(params.branchIds, params.branchIdsExclude, params.keywords,
                        params.partNumber, params.sku);
            }
        } else if (params.includeUndefined != null && params.includeUndefined) {
            // this scenario makes no assumptions about the existence of inventory_item or physical_item records
            totalCount = inventoryItemDTORepository.countByCrossJoin(params.branchCount, params.keywords, params.partNumber, params.sku);
        } else {
            // this scenario requires that includeUndefined be false and there be no numeric parameters
            if ((params.keywords != null && params.keywords.length() > 0) ||
                    (params.partNumber != null && params.partNumber.length() > 0) ||
                    (params.sku != null && params.sku.length() > 0)) {
                totalCount = inventoryItemDTORepository.countByInventoryItem(params.branchIds, params.branchIdsExclude,
                        params.keywords, params.partNumber, params.sku);
            } else {
                totalCount = inventoryItemDTORepository.countByInventoryItemBranch(params.branchIds, params.branchIdsExclude);
            }
        }
        return totalCount;
    }

    /**
     * Calculate itemLimit and itemOffset for cross-join search
     *
     * @param params
     * @return long[] {itemLimit, itemOffset, pageOffset}
     */
    private long[] getItemLimitOffset(InventorySearchParams params) {
        // this scenario makes no assumptions about the existence of inventory_item or physical_item records
        // pagination inside CTEs, the explanations are based on branchCount = 3 and pageSize = 10
        long[] result = new long[3];
        // actual itemLimit (long) will be the ceiling of the double itemLimit (use 4 instead of 3.333)
        float itemLimit = ((float) params.pageSize) / ((float) params.branchCount);
        // for itemOffset, we want to reprocess the last item from the previous page (skip 3 and reprocess the 4th item)
        result[1] = (long) (itemLimit * params.pageNumber);
        // pageOffset is the difference between the number of previously processed rows (10) and the number of "full sets" of items * branches (3*3)
        // e.g.: if the first page contained 10 total rows from 4 items, it included 3 "full sets" (3*3=9) and 1 row from item #4 which will be skipped
        result[2] = params.pageSize * params.pageNumber % params.branchCount;
        // when all previously processed rows are "full sets", we need to skip reprocessing the last previously processed item
        if (result[2] == 0 && params.pageNumber > 0) {
            result[1]++;
        }
        // limit
        result[0] = (long) Math.ceil(itemLimit);
        return result;
    }

    /**
     * Some search params need to be calculated, others - to be properly reformatted.
     *
     * @param branchId
     * @param includeUndefined
     * @param keyword
     * @param lowStock
     * @param min
     * @param max
     * @param needsReStock
     * @param pageable
     * @param partNumber
     * @param preferredStock
     * @param useChildBranches
     * @return
     */
    private InventorySearchParams getSearchParams(Long branchId, Boolean includeUndefined, String keyword, Boolean lowStock, Long min, Long max,
                                                  Boolean needsReStock, Pageable pageable, String partNumber,
                                                  Boolean preferredStock, Boolean useChildBranches) {
        InventorySearchParams params = new InventorySearchParams();
        // branch
        if (branchId != null && branchId > 0) {
            params.branchIds = new ArrayList<>();
            params.branchIds.add(branchId);
            if (useChildBranches) {
                params.branchIds = branchService.getChildrenIdsByParentIds(params.branchIds, true);
            }
            params.branchCount = params.branchIds.size();
        } else {
            params.branchIds = null;
            params.branchIdsExclude = branchService.findAllIdsByActiveFalse();
            params.branchCount = branchService.getCountByActiveTrue();
        }

        // pageNumber coming from the UI is 1-based, but the actual page number is 0-based, it will not be sent back
        params.pageNumber = pageable != null ? pageable.getPageNumber() : 0;
        params.pageSize = pageable != null ? pageable.getPageSize() : 10000;
        params.pageOffset = params.pageSize * params.pageNumber;

        // if keyword consists of only spaces, set it to null
        if (!Strings.isNullOrEmpty(keyword)) {
            keyword = keyword.trim();
        } else {
            keyword = null;
        }
        params.sku = Strings.isNullOrEmpty(keyword) ? null : keyword + "%";
        // Format keywords for proper full-text search
        params.keywords = Strings.isNullOrEmpty(keyword) ? null : StringUtil.formatKeywordsForFullStringSearch(keyword);
        if (!Strings.isNullOrEmpty(partNumber)) {
            partNumber = partNumber.trim();
        } else {
            partNumber = null;
        }
        params.partNumber = Strings.isNullOrEmpty(partNumber) ? null : partNumber + "%";
        // numeric parameters
        params.min = (min != null && min > 0) ? min : null;
        // max is an exception, it is used even if it is 0 (e.g.: to find out-of-stock items)
        params.max = max;
        params.includeUndefined = (includeUndefined != null && includeUndefined) ? true : null;
        params.lowStock = (lowStock != null && lowStock) ? true : null;
        params.preferredStock = (preferredStock != null && preferredStock) ? true : null;
        params.needsReStock = (needsReStock != null && needsReStock) ? true : null;
        return params;
    }

    private List<InventoryItemDTO> searchInventoryItemDTO(InventorySearchParams params, long totalCount) {
        List<InventoryItemDTO> dtoList = null;
        if (totalCount > 0 && params.pageSize * params.pageNumber < totalCount) {
            if ((params.lowStock != null && params.lowStock) || (params.needsReStock != null && params.needsReStock) ||
                    (params.preferredStock != null && params.preferredStock)) {
                // this scenario referres to fields in inventory_item and therefore requires that includeUndefined be false
                if (!Strings.isNullOrEmpty(params.keywords) || !Strings.isNullOrEmpty(params.partNumber) || !Strings.isNullOrEmpty(params.sku)) {
                    dtoList = inventoryItemDTORepository.searchByStockValuesFilter(params.branchIds, params.branchIdsExclude,
                            params.keywords, params.lowStock, params.max, params.min, params.needsReStock, params.pageSize,
                            params.pageOffset, params.partNumber, params.preferredStock, params.sku);
                } else {
                    dtoList = inventoryItemDTORepository.searchByStockValues(params.branchIds, params.branchIdsExclude, params.lowStock,
                            params.max, params.min, params.needsReStock, params.pageSize, params.pageOffset, params.preferredStock);
                }
            } else if (params.min != null && params.min > 0) {
                // this scenario requires that at least one physical_item record exist
                if (params.includeUndefined != null && params.includeUndefined) {
                    dtoList = inventoryItemDTORepository.searchByMinMaxWithUndefined(params.branchIds, params.branchIdsExclude,
                            params.keywords, params.max, params.min, params.pageSize, params.pageOffset, params.partNumber, params.sku);
                } else {
                    dtoList = inventoryItemDTORepository.searchByMinMax(params.branchIds, params.branchIdsExclude, params.keywords,
                            params.max, params.min, params.pageSize, params.pageOffset, params.partNumber, params.sku);
                }
            } else if (params.max != null && params.max == 0) {
                // because this scenario requires that there are no physical_item records, all other numeric parameters are irrelevant
                if (params.includeUndefined != null && params.includeUndefined) {
                    dtoList = inventoryItemDTORepository.searchByOutOfStockWithUndefined(params.branchIds, params.branchIdsExclude,
                            params.keywords, params.pageSize, params.pageOffset, params.partNumber, params.sku);
                } else {
                    dtoList = inventoryItemDTORepository.searchByOutOfStock(params.branchIds, params.branchIdsExclude, params.keywords,
                            params.pageSize, params.pageOffset, params.partNumber, params.sku);
                }
            } else if (params.includeUndefined != null && params.includeUndefined) {
                long[] itemLimitOffset = getItemLimitOffset(params);
                dtoList = inventoryItemDTORepository.searchByCrossJoin(params.branchIds, params.branchIdsExclude, itemLimitOffset[0],
                        itemLimitOffset[1], params.keywords, params.pageSize, itemLimitOffset[2], params.partNumber, params.sku);
            } else {
                if (!Strings.isNullOrEmpty(params.keywords) || !Strings.isNullOrEmpty(params.partNumber) || !Strings.isNullOrEmpty(params.sku)) {
                    dtoList = inventoryItemDTORepository.searchByInventoryItem(params.branchIds, params.branchIdsExclude,
                            params.keywords, params.pageSize, params.pageOffset, params.partNumber, params.sku);
                } else {
                    // when the only filter is Branch - preselect, sort and paginate inventory_item
                    dtoList = inventoryItemDTORepository.searchByInventoryItemBranch(params.branchIds, params.branchIdsExclude,
                            params.pageSize, params.pageOffset);
                }
            }
        }
        return dtoList;
    }

    /**
     * Update InventoryItems with values from Excel file
     * @param inventoryItems
     * @param excelValues
     * @return
     */
    private List<InventoryItem> updateInventoryItemsFromExcel(List<InventoryItem> inventoryItems, List<Long[]> excelValues) {
        List<InventoryItem> inventoryItemsToSave = new ArrayList<>();
        if (inventoryItems == null || inventoryItems.isEmpty() || excelValues == null || excelValues.isEmpty()) {
            return inventoryItemsToSave;
        }
        for (InventoryItem inventoryItem : inventoryItems) {
            // "undefined" InventoryItems have negative ids, replace them with null
            if (inventoryItem.getId() != null && inventoryItem.getId() <= 0) {
                inventoryItem.setId(null);
            }
            boolean isModified = false;
            // find the corresponding value (column) in excelValues
            for (Long[] rowValues : excelValues) {
                if (rowValues[1] != null && rowValues[1] > 0 && rowValues[1].equals(inventoryItem.getBranchId()) &&
                        rowValues[2] != null && rowValues[2] > 0 && rowValues[2].equals(inventoryItem.getItemId())) {
                    // update InventoryItem with new values if they are valid and different from old ones
                    Long newLowStock = rowValues[3];
                    // newLowStock cannot be negative, it is either undefined or a number >= 0
                    if (newLowStock != null && newLowStock < 0) {
                        newLowStock = null;
                    }
                    Long oldLowStock = inventoryItem.getLowStock();
                    inventoryItem.setOldLowStock(oldLowStock);
                    if ((newLowStock == null && oldLowStock != null) ||
                            (newLowStock != null && newLowStock >= 0 &&
                                    (oldLowStock == null || !newLowStock.equals(oldLowStock)))) {
                        isModified = true;
                        inventoryItem.setLowStock(newLowStock);
                    }

                    Long newPreferredStock = rowValues[4];
                    // newPreferredStock cannot be negative, it is either undefined or a number >= 0
                    if (newPreferredStock != null && newPreferredStock < 0) {
                        newPreferredStock = null;
                    }
                    Long oldPreferredStock = inventoryItem.getPreferredStock();
                    inventoryItem.setOldPreferredStock(oldPreferredStock);
                    if ((newPreferredStock == null && oldPreferredStock != null) ||
                            (newPreferredStock != null && newPreferredStock >= 0 &&
                                    (oldPreferredStock == null || !newPreferredStock.equals(oldPreferredStock)))) {
                        isModified = true;
                        inventoryItem.setPreferredStock(newPreferredStock);
                    }

                    Long newQuantity = rowValues[5];
                    Long oldQuantity = inventoryItem.getQuantity();
                    inventoryItem.setOldQuantity(oldQuantity);
                    // Null or negative newQuantity means "don't change the quantity"
                    if (newQuantity != null && newQuantity >= 0 &&
                            (oldQuantity == null || !newQuantity.equals(oldQuantity))) {
                        isModified = true;
                        inventoryItem.setQuantity(newQuantity);
                    }
                    break;
                }
            }
            // only update the InventoryItem if at least one value has changed
            if (isModified) {
                inventoryItemsToSave.add(inventoryItem);
            }
        }
        return inventoryItemsToSave;
    }
    // ENDREGION

    public class InventorySearchParams {
        public int branchCount;
        public List<Long> branchIds;
        public List<Long> branchIdsExclude;
        public Boolean includeUndefined;
        public String keywords;
        public Boolean lowStock;
        public Long max;
        public Long min;
        public long pageNumber;
        public long pageOffset;
        public long pageSize;
        public String partNumber;
        public Boolean preferredStock;
        public Boolean needsReStock;
        public String sku;
    }
}
