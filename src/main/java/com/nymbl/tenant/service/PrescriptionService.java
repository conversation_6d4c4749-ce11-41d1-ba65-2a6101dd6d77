package com.nymbl.tenant.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nymbl.config.clearingHouse.WaystarAPI;
import com.nymbl.config.comparator.SubmissionDateAsc;
import com.nymbl.config.dto.*;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.NumberUtil;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.Auditable;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.interfaces.LockedEntity;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.*;
import com.nymbl.tenant.specification.PrescriptionSpecs;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.nymbl.config.Constants.DF_YYYY_MM_DD_HH_MM;
import static com.nymbl.config.utils.OptimisticLockingUtil.*;
import static org.codehaus.groovy.runtime.DefaultGroovyMethods.contains;

@Slf4j
@Service
public class PrescriptionService extends AbstractTableService<Prescription, Long> {

    private final PrescriptionRepository prescriptionRepository;
    private final Prescription_L_CodeRepository prescriptionLCodeRepository;
    private final UserService userService;
    private final PrescriptionSectionService prescriptionSectionService;
    private final ServiceEstimateService serviceEstimateService;
    private final ClaimService claimService;
    private final ClaimSubmissionService claimSubmissionService;
    private final AppliedPaymentService appliedPaymentService;
    private final InsuranceVerificationService insuranceVerificationService;
    private final InsuranceVerificationLCodeService insuranceVerificationLCodeService;
    private final NoteService noteService;
    private final ClaimRepository claimRepository;
    private final DetailedWrittenOrderService detailedWrittenOrderService;
    private final FinancialResponsibilityService financialResponsibilityService;
    private final PrescriptionDiagnosisCodeService prescriptionDiagnosisCodeService;
    private final L_CodeFeeService lCodeFeeService;
    private final SystemSettingService systemSettingService;
    private final DeviceTypeFileTypeService deviceTypeFileTypeService;
    private final FileService fileService;
    private final ChecklistService checklistService;
    private final DeviceTypeRepository deviceTypeRepository;
    private final PaymentService paymentService;
    private final CriticalMessageService criticalMessageService;
    private final WaystarAPI waystarAPI;
    private final OptimisticLockingUtil optimisticLockingUtil;
    private final NotificationService notificationService;
    private final ItemPhysicalRepository itemPhysicalRepository;

    @Autowired
    public PrescriptionService(PrescriptionRepository prescriptionRepository,
                               Prescription_L_CodeRepository prescriptionLCodeRepository,
                               UserService userService,
                               PrescriptionSectionService prescriptionSectionService,
                               @Lazy ServiceEstimateService serviceEstimateService,
                               @Lazy ClaimService claimService,
                               @Lazy ClaimSubmissionService claimSubmissionService,
                               AppliedPaymentService appliedPaymentService,
                               InsuranceVerificationService insuranceVerificationService,
                               InsuranceVerificationLCodeService insuranceVerificationLCodeService,
                               NoteService noteService,
                               ClaimRepository claimRepository,
                               DetailedWrittenOrderService detailedWrittenOrderService,
                               FinancialResponsibilityService financialResponsibilityService,
                               PrescriptionDiagnosisCodeService prescriptionDiagnosisCodeService,
                               L_CodeFeeService lCodeFeeService,
                               SystemSettingService systemSettingService,
                               DeviceTypeFileTypeService deviceTypeFileTypeService,
                               FileService fileService,
                               @Lazy ChecklistService checklistService,
                               DeviceTypeRepository deviceTypeRepository,
                               PaymentService paymentService,
                               CriticalMessageService criticalMessageService,
                               WaystarAPI clearingHouseAPI,
                               OptimisticLockingUtil optimisticLockingUtil,
                               @Lazy NotificationService notificationService,
                               ItemPhysicalRepository itemPhysicalRepository) {
        super(prescriptionRepository);
        this.prescriptionRepository = prescriptionRepository;
        this.prescriptionLCodeRepository = prescriptionLCodeRepository;
        this.userService = userService;
        this.prescriptionSectionService = prescriptionSectionService;
        this.serviceEstimateService = serviceEstimateService;
        this.claimService = claimService;
        this.claimSubmissionService = claimSubmissionService;
        this.appliedPaymentService = appliedPaymentService;
        this.insuranceVerificationService = insuranceVerificationService;
        this.insuranceVerificationLCodeService = insuranceVerificationLCodeService;
        this.noteService = noteService;
        this.claimRepository = claimRepository;
        this.detailedWrittenOrderService = detailedWrittenOrderService;
        this.financialResponsibilityService = financialResponsibilityService;
        this.prescriptionDiagnosisCodeService = prescriptionDiagnosisCodeService;
        this.lCodeFeeService = lCodeFeeService;
        this.systemSettingService = systemSettingService;
        this.deviceTypeFileTypeService = deviceTypeFileTypeService;
        this.fileService = fileService;
        this.checklistService = checklistService;
        this.deviceTypeRepository = deviceTypeRepository;
        this.paymentService = paymentService;
        this.criticalMessageService = criticalMessageService;
        this.waystarAPI = clearingHouseAPI;
        this.optimisticLockingUtil = optimisticLockingUtil;
        this.notificationService = notificationService;
        this.itemPhysicalRepository = itemPhysicalRepository;
    }

    public List<Prescription> incompletePrescriptionsByBranchId(Long branchId) {
        List<Prescription> prescriptions;
        if (branchId != null) {
            prescriptions = prescriptionRepository.findAllByIncompleteSectionsWithBranchId(branchId);
        } else {
            prescriptions = prescriptionRepository.findAllByIncompleteSections();
        }
        return prescriptions;
    }

    public WipBillingsReportDTO wipBillingsCollected(Long branchId) {
        WipBillingsReportDTO dto = new WipBillingsReportDTO();
        List<Map<String, Object>> results = new ArrayList<>();
        List<Prescription> prescriptions = incompletePrescriptionsByBranchId(branchId);
        BigDecimal totalBillable = BigDecimal.ZERO;
        BigDecimal totalAllowable = BigDecimal.ZERO;
        if (prescriptions.get(0).getId() == 0) {
            prescriptions.remove(0);
        }
        for (Prescription prescription : prescriptions) {
            Map<String, Object> wipDTO = new HashMap<>();
            wipDTO.put("prescription", prescription);
//            List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdWithCalculatedFirstCarrier(prescription.getId());
            List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "primary");
            if (ivlcs.isEmpty()) {
                ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "secondary");
            }
            if (ivlcs.isEmpty()) {
                ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "tertiary");
            }
            if (ivlcs.isEmpty()) {
                ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "quaternary");
            }
            if (ivlcs.isEmpty()) {
                ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "quinary");
            }
            if (ivlcs.isEmpty()) {
                ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "senary");
            }
            if (ivlcs.isEmpty()) {
                ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "septenary");
            }
            if (ivlcs.isEmpty()) {
                ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "octonary");
            }
            if (ivlcs.isEmpty()) {
                ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "nonary");
            }
            if (ivlcs.isEmpty()) {
                ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "denary");
            }
            BigDecimal billable = BigDecimal.ZERO;
            BigDecimal allowable = BigDecimal.ZERO;
            for (InsuranceVerification_L_Code ivlc : ivlcs) {
                billable = billable.add(ivlc.getTotalCharge());
                totalBillable = totalBillable.add(ivlc.getTotalCharge());
                allowable = allowable.add(ivlc.getTotalAllowable());
                totalAllowable = totalAllowable.add(ivlc.getTotalAllowable());
            }
            wipDTO.put("billable", billable);
            wipDTO.put("allowable", allowable);
            results.add(wipDTO);
        }
        dto.setResults(results);
        dto.setTotalBillable(totalBillable);
        dto.setTotalAllowable(totalAllowable);
        return dto;
    }

    public List<PatientSummaryDTO> search(Long branchId, Boolean active, Long treatingPractitionerId, Long clericalUserId, Boolean complete, String[] sections, String aging, Date startDate, Date endDate, Pageable pageable) {
        pageable = PageRequest.of(0, 1000, pageable.getSort());
        List<PatientSummaryDTO> result = null;
        Page<Prescription> prescriptions;
        PrescriptionSpecs spec = new PrescriptionSpecs(branchId, active, treatingPractitionerId, clericalUserId, aging, startDate, endDate);
        if (spec.getHasSpec()) {
            prescriptions = prescriptionRepository.findAll(spec, pageable);
        } else {
            prescriptions = prescriptionRepository.findAll(pageable);
        }
        log.info("prescription summary search Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        List<Prescription> toRemove = getPrescriptionsToRemove(complete, sections, prescriptions);
        List<Prescription> finalList = removeUnwantedPrescriptions(prescriptions, toRemove);
        for (Prescription p : finalList) {
            List<Claim> claims = claimRepository.findByPrescriptionId(p.getId());
            List<ClaimSubmission> cs = new ArrayList<>();
            List<AppliedPayment> ap = new ArrayList<>();
            populateClaimSubmissionAndAppliedPayment(claims, cs, ap);
            if (result == null) {
                result = new ArrayList<>();
            }
            PatientSummaryDTO dto = new PatientSummaryDTO();
            dto.setPrescription(p);
            List<InsuranceVerification> insuranceVerifications = insuranceVerificationService.findByPrescriptionId(p.getId());
            dto.setInsuranceVerifications(insuranceVerifications);
            BigDecimal estimatedValue = populateTotalAllowableForInsuranceVerificationAndCoveredTrue(insuranceVerifications);
            dto.setEstimatedValue(estimatedValue);
            dto.setOutstandingBalance(getClaimTotalBalance(p.getId()));
            List<Prescription_L_Code> prescriptionLCodes = prescriptionLCodeRepository.findByPrescriptionId(p.getId());
            dto.setPrescriptionLCodes(prescriptionLCodes);
            if (!cs.isEmpty()) {
                dto.setSubmissionDate(cs.get(0).getSubmissionDate());
            }
            if (!ap.isEmpty()) {
                dto.setAppliedPaymentDate(ap.get(0).getAppliedDate());
            }
            List<Note> patientSummaryNotes = noteService.getByPrescriptionIdAndNoteType(p.getId(), "patient_summary");
            dto.setPatientSummaryNotes(patientSummaryNotes);
            populateListEntryForUnlockedPrescriptionSections(p, dto);
            result.add(dto);
        }
        log.info("prescription sumamry search End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        return result;
    }

    public void populateListEntryForUnlockedPrescriptionSections(Prescription p, PatientSummaryDTO dto) {
        List<PrescriptionSection> unLockedSections = prescriptionSectionService.findByPrescriptionIdAndLockedIsFalse(p.getId());
        ObjectMapper mapper = new ObjectMapper();
        try {
            @SuppressWarnings("unchecked")
            List<LinkedHashMap<String, String>> sectionMap = mapper.readValue(ResourceUtils.getURL("classpath:static/scripts/jsons/sections.json"), List.class);
            for (PrescriptionSection section : unLockedSections) {
                for (LinkedHashMap<String, String> entry : sectionMap) {
                    if (entry.get("key").equals(section.getSection())) {
                        dto.addListEntry(entry.get("name"));
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
    }

    public BigDecimal populateTotalAllowableForInsuranceVerificationAndCoveredTrue(List<InsuranceVerification> insuranceVerifications) {
        BigDecimal estimatedValue = BigDecimal.ZERO;
        for (InsuranceVerification iv : insuranceVerifications) {
            List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByInsuranceVerificationIdAndCoveredTrue(iv.getId());
            for (InsuranceVerification_L_Code ivlc : ivlcs) {
                estimatedValue = estimatedValue.add(ivlc.getTotalAllowable());
            }
        }
        return estimatedValue;
    }

    public void populateClaimSubmissionAndAppliedPayment(List<Claim> claims, List<ClaimSubmission> cs, List<AppliedPayment> ap) {
        for (Claim c : claims) {
            if (c != null) {
                cs.addAll(claimSubmissionService.findByClaimId(c.getId()));
                cs.sort(new SubmissionDateAsc());
                ap.addAll(appliedPaymentService.getByClaimId(c.getId()));
                ap.sort((o1, o2) ->
                        o1.getAppliedDate() == null && o2.getAppliedDate() != null ? 1 :
                                o1.getAppliedDate() != null && o2.getAppliedDate() == null ? -1 :
                                        o1.getAppliedDate() == null && o2.getAppliedDate() == null ? 0 :
                                                o2.getAppliedDate().compareTo(o1.getAppliedDate()));
            }
        }
    }

    public List<Prescription> removeUnwantedPrescriptions(Page<Prescription> prescriptions, List<Prescription> toRemove) {
        List<Prescription> finalList = new ArrayList<>(prescriptions.getContent());
        if (toRemove.size() > 0) {
            finalList.removeAll(toRemove);
        } else {
            finalList = prescriptions.getContent();
        }
        return finalList;
    }

    public List<Prescription> getPrescriptionsToRemove(Boolean complete, String[] sections, Page<Prescription> prescriptions) {
        List<Prescription> toRemove = new ArrayList<>();
        for (Prescription o : prescriptions.getContent()) {
            loadForeignKeys(o);
            if (o.getPatient() == null || (o.getPatient().getActive() != null && !o.getPatient().getActive())) {
                toRemove.add(o);
            }
            List<PrescriptionSection> uncompleted = prescriptionSectionService.findByPrescriptionIdAndLockedIsFalse(o.getId());
            if (complete != null) {
                if ((complete && uncompleted.size() > 0) || (!complete && uncompleted.size() == 0)) {
                    toRemove.add(o);
                }
            }
            if (sections != null && sections.length > 0) {
                List<PrescriptionSection> sectionList = prescriptionSectionService.findByPrescriptionIdAndSectionIn(o.getId(), sections);
                if (sectionList.size() == 0) {
                    toRemove.add(o);
                }
            }
        }
        return toRemove;
    }

    public Long getChecklistTemplateId(Prescription prescription) {
        Long checklistTemplateId = null;
        if (prescription.getDeviceType() != null) {
            if (prescription.getDeviceType().getOrthoticOrProsthetic() != null) {
                //System.out.println("prescription.getDeviceType().getOrthoticOrProsthetic() = "+prescription.getDeviceType().getOrthoticOrProsthetic());
                if ("orthotic".equals(prescription.getDeviceType().getOrthoticOrProsthetic()) || "pedorthic".equals(prescription.getDeviceType().getOrthoticOrProsthetic())
                        || "misc".equals(prescription.getDeviceType().getOrthoticOrProsthetic())) {
                    checklistTemplateId = 1000L;
                } else if ("prosthetic".equals(prescription.getDeviceType().getOrthoticOrProsthetic())) {
                    checklistTemplateId = 1001L;
                }
            }
        }
        return checklistTemplateId;
    }

    // This method takes a prescription and returns a list of file types of missing required documents
    public List<FileType> populateMissingRequiredDocuments(Prescription prescription) {
        List<FileType> result = new ArrayList<>();
        List<DeviceTypeFileType> deviceTypeFileTypes = deviceTypeFileTypeService.findByDeviceTypeId(prescription.getDeviceTypeId());
        List<Long> uploadedFileTypeIds = fileService.getUploadedFileTypesForPrescription(prescription.getId());
        for (DeviceTypeFileType deviceTypeFileType : deviceTypeFileTypes) {
            FileType fileType = deviceTypeFileType.getFileType();
            if (deviceTypeFileType.getFileTypeId() == 19L) { //19L is "primary_authorization"
                InsuranceVerification primaryInsuranceVerification = insuranceVerificationService.
                        findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(prescription.getId(), "primary");
                if (primaryInsuranceVerification == null || primaryInsuranceVerification.getReferralNumber() == null)
                    result.add(fileType);
            } else if (deviceTypeFileType.getFileTypeId() == 20L) { // 20L is "secondary_authorization"
                InsuranceVerification secondaryInsuranceVerification = insuranceVerificationService.
                        findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(prescription.getId(), "secondary");
                if (secondaryInsuranceVerification != null && secondaryInsuranceVerification.getReferralNumber() == null)
                    result.add(fileType);
            } else if (!uploadedFileTypeIds.contains(deviceTypeFileType.getFileTypeId())) {
                result.add(fileType);
            }
        }
        return result;
    }

//    public List<File> populatePhysicianDocuments(Long prescriptionId) {
//        List<FileType> physicianDocumentFileTypes = fileTypeService.findAllByIsPhysicianDocumentationTrue();
//        List<Long> fileTypeIds = physicianDocumentFileTypes.stream().map(FileType::getId).collect(Collectors.toList());
//        List<File> physicianDocuments = fileService.findByPrescriptionIdAndFileTypeIdIn(prescriptionId, fileTypeIds);
//        return physicianDocuments;
//    }

    public BigDecimal populateEstimatedTotalAllowableFromPrimaryInsuranceVerification(List<InsuranceVerification> insuranceVerifications) {
        BigDecimal estimatedValue = BigDecimal.ZERO;
        for (InsuranceVerification insuranceVerification : insuranceVerifications) {
            if (insuranceVerification != null && insuranceVerification.getCarrierType().equals("primary")) {
                List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByInsuranceVerificationIdAndCoveredTrue(insuranceVerification.getId());
                for (InsuranceVerification_L_Code ivlc : ivlcs) {
                    estimatedValue = estimatedValue.add(ivlc.getTotalAllowable() != null ? ivlc.getTotalAllowable() : BigDecimal.ZERO);
                }
            }
        }
        return estimatedValue;
    }

    public void populateListEntryForUnLockedPrescriptionSectionExcludingEvaluationForms(Prescription prescription, PatientSummaryDTO dto) {
        List<PrescriptionSection> unLockedSections = prescriptionSectionService.findByPrescriptionIdAndLockedIsFalse(prescription.getId());
        ObjectMapper mapper = new ObjectMapper();
        try {
            @SuppressWarnings("unchecked")
            List<LinkedHashMap<String, String>> sectionMap = mapper.readValue(ResourceUtils.getURL("classpath:static/scripts/jsons/sections.json"), List.class);
            for (PrescriptionSection section : unLockedSections) {
                for (LinkedHashMap<String, String> entry : sectionMap) {
                    if (entry.get("key").equals(section.getSection()) && !entry.get("key").equals("evaluation_forms")) {
                        dto.addListEntry(entry.get("name"));
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
    }

    public static java.sql.Date[] populateAgingStartDateAndAgingEndDate(String aging, Calendar cal1, Calendar cal2, java.util.Date date) {
        java.sql.Date[] agingStartDateAndAgingEndDate = new java.sql.Date[]{null, null};
        if (!StringUtil.isBlank(aging)) {
            cal2.setTime(date);
            if (aging.equals("1")) {
                cal2.add(Calendar.DAY_OF_MONTH, -30);
                agingStartDateAndAgingEndDate[1] = new java.sql.Date(cal1.getTime().getTime());
                agingStartDateAndAgingEndDate[0] = new java.sql.Date(cal2.getTime().getTime());
            } else if (aging.equals("2")) {
                cal2.add(Calendar.DAY_OF_MONTH, -31);
                cal1.add(Calendar.DAY_OF_MONTH, -60);
                agingStartDateAndAgingEndDate[0] = new java.sql.Date(cal1.getTimeInMillis());
                agingStartDateAndAgingEndDate[1] = new java.sql.Date(cal2.getTimeInMillis());
            } else if (aging.equals("3")) {
                cal2.add(Calendar.DAY_OF_MONTH, -61);
                cal1.add(Calendar.DAY_OF_MONTH, -90);
                agingStartDateAndAgingEndDate[0] = new java.sql.Date(cal1.getTime().getTime());
                agingStartDateAndAgingEndDate[1] = new java.sql.Date(cal2.getTime().getTime());
            } else {
                cal2.add(Calendar.DAY_OF_MONTH, -91);
                agingStartDateAndAgingEndDate[0] = null;
                agingStartDateAndAgingEndDate[1] = new java.sql.Date(cal2.getTime().getTime());
            }
        }
        return agingStartDateAndAgingEndDate;
    }

    public String[] updateFieldNameAndDirection(Pageable pageable) {
        String[] fieldNameAndDirection = new String[]{"p.id", "DESC"};
        Sort sort = pageable.getSort();
        List<String> sortProperties = Arrays.asList("last_name", "rx.created_at", "rx.prescription_date", "branch_name", "rx_name", "estimated_value", "outstanding_balance", "incomplete_sections", "insurance_name", "rx_status", "projected_delivery_date", "surgery_date", "orthotic_or_prosthetic", "note_last_date");
//        List<String> sortProperties = Arrays.asList("last_name", "rx.created_at", "rx.prescription_date", "branch_name", "rx_name", "estimated_value", "outstanding_balance", "incomplete_sections", "insurance_name", "rx_status", "projected_delivery_date", "orthotic_or_prosthetic");
        for (String property : sortProperties) {
            if (sort.getOrderFor(property) != null) {
                fieldNameAndDirection[0] = sort.getOrderFor(property).getProperty();
                fieldNameAndDirection[1] = sort.getOrderFor(property).getDirection().isAscending() ? "ASC" : "DESC";
                break;
            }
        }
        return fieldNameAndDirection;
    }

    public static String constructQuerySegment(Long prescriptionId, Long patientId, Long branchId, String branchType, Boolean active, Long insuranceCompanyId, Long treatingPractitionerId, Long residentId, Long clericalUserId, Long referringPhysicianId, Long primaryCarePhysicianId, Boolean complete, String[] sections, String[] completedSections, Date agingStartDate, Date agingEndDate, Date startDate, Date endDate, Boolean showOnHold, Boolean showSentToBilling, Long nymblStatusId, Long physicianDocumentationStatusId, String type, String fabricationStep, String fabricationStepStatus) {

        String selectBranchType = " LEFT JOIN branch b ON p.primary_branch_id = b.id";
        if ("prescription".equals(branchType)) {
            selectBranchType = " LEFT JOIN branch b ON rx.branch_id = b.id";
        }
        String baseSegment = " FROM prescription rx" +
                " JOIN patient p ON rx.patient_id = p.id" +
                selectBranchType +
                " JOIN device_type dt  ON rx.device_type_id = dt.id" +
                " LEFT JOIN ( SELECT insurance_verification.prescription_id, SUM(total_allowable) AS estimated_value FROM insurance_verification JOIN insurance_verification_l_code ivlc ON insurance_verification.id = ivlc.insurance_verification_id AND ivlc.covered = 1 WHERE insurance_verification.carrier_type = 'primary' GROUP BY insurance_verification.prescription_id) AS iv ON rx.id = iv.prescription_id" +
                " JOIN prescription_section ps ON rx.id = ps.prescription_id" +
                " LEFT JOIN patient_insurance pi ON rx.patient_insurance_id = pi.id" +
                " LEFT JOIN insurance_company ic ON pi.insurance_company_id = ic.id" +
                " LEFT JOIN nymbl_status ns ON rx.nymbl_status_id = ns.id" +
                " LEFT JOIN nymbl_status pds ON rx.physician_documentation_status_id = pds.id" +
                " LEFT JOIN checklist_step cls ON cls.prescription_id = rx.id and cls.type='wip_fabrication'";
        String prescriptionSegment = " PLACEHOLDER rx.id = " + prescriptionId;
        String patientSegment = " PLACEHOLDER p.id = " + patientId;
        String branchSegment = " PLACEHOLDER b.id = " + branchId;
        String statusSegment = " PLACEHOLDER rx.active = " + active;
        String insuranceCompanyIdSegment = " PLACEHOLDER pi.insurance_company_id = " + insuranceCompanyId;
        String treatingPractitionerSegment = " PLACEHOLDER rx.treating_practitioner_id = " + treatingPractitionerId;
        String residentSegment = " PLACEHOLDER rx.resident_id = " + residentId;
        String clericalUserSegment = " PLACEHOLDER rx.clerical_user_id = " + clericalUserId;
        String referringPhysicianSegment = " PLACEHOLDER rx.referring_physician_id = " + referringPhysicianId;
        String primaryCarePhysicianSegment = " PLACEHOLDER rx.primary_care_physician_id = " + primaryCarePhysicianId;
        String agingSegment = " PLACEHOLDER rx.prescription_date BETWEEN " + ((agingStartDate != null) ? String.format("'%s'", agingStartDate) : "'0000-01-01'") + " AND " + ((agingEndDate != null) ? String.format("'%s'", agingEndDate) : "'0000-01-01'");
        String completedSegment = " PLACEHOLDER (SELECT COUNT(*) FROM prescription_section ps WHERE rx.id = ps.prescription_id AND ps.locked = 1) " + ((complete != null && complete) ? "=" : "<") + " (SELECT COUNT(*) FROM prescription_section ps WHERE rx.id = ps.prescription_id)";
        String prescriptionSectionsSegment = " PLACEHOLDER ps.locked = 0 AND (";
        String holdUntilSegment = " PLACEHOLDER (rx.hold_until_date IS NULL OR rx.hold_until_date <= CURRENT_DATE())";
        // Per Dev Meeting on 7/23/2020, it was determined the Show Sent To Billing checkbox should include ALL RXs that
        // have a claim created, not just those that don't have a claim submission
        String sentToBillingSegment = "";
        String nymblStatusSegment = " PLACEHOLDER rx.nymbl_status_id = " + nymblStatusId;
        String physicianDocumentationSegment = " PLACEHOLDER rx.physician_documentation_status_id " + ((physicianDocumentationStatusId != null && physicianDocumentationStatusId == 0) ? " IS NULL" : " = " + physicianDocumentationStatusId);
        String typeSegment = " PLACEHOLDER dt.orthotic_or_prosthetic = " + String.format("'%s'", type);
        String fabricationStepSegment = " PLACEHOLDER (cls.name = " + String.format("'%s'", fabricationStep) + " OR '' = " + String.format("'%s'", fabricationStep) + ")";
        String fabricationStepStatusSegment = " PLACEHOLDER (cls.status = " + String.format("'%s'", fabricationStepStatus) + " OR '' = " + String.format("'%s'", fabricationStepStatus) + ")";

        if (sections != null) {
            int sectionsLength = sections.length;
            int current = 1;
            StringBuilder prescriptionSectionsSegmentBuilder = new StringBuilder(" PLACEHOLDER ps.locked = 0 AND (");
            for (String section : sections) {
                prescriptionSectionsSegmentBuilder.append(" ps.section = '").append(section).append("'");
                if (current < sectionsLength) {
                    prescriptionSectionsSegmentBuilder.append(" OR ");
                }
                current++;
            }
            prescriptionSectionsSegment = prescriptionSectionsSegmentBuilder.append(")").toString();
        }

        String replaceableQuery = populateReplaceableQuery(prescriptionId, patientId, branchId, active, insuranceCompanyId, treatingPractitionerId, residentId, clericalUserId, referringPhysicianId, primaryCarePhysicianId, complete, sections, completedSections, agingStartDate, agingEndDate, showOnHold, showSentToBilling, nymblStatusId, physicianDocumentationStatusId, type, fabricationStep, fabricationStepStatus,
                baseSegment, prescriptionSegment, patientSegment, branchSegment, statusSegment, insuranceCompanyIdSegment, treatingPractitionerSegment, residentSegment, clericalUserSegment, referringPhysicianSegment, primaryCarePhysicianSegment, agingSegment, completedSegment, prescriptionSectionsSegment, holdUntilSegment, sentToBillingSegment, nymblStatusSegment, physicianDocumentationSegment, typeSegment, fabricationStepSegment, fabricationStepStatusSegment);
        return replaceableQuery.replaceFirst("PLACEHOLDER", "WHERE").replaceAll("PLACEHOLDER", "AND");
    }

    public static String populateReplaceableQuery(Long prescriptionId, Long patientId, Long branchId, Boolean active, Long insuranceCompanyId, Long treatingPractitionerId, Long residentId, Long clericalUserId, Long referringPhysicianId, Long primaryCarePhysicianId, Boolean complete, String[] sections, String[] completedSections, Date agingStartDate, Date agingEndDate, Boolean showOnHold, Boolean showSentToBilling, Long nymblStatusId, Long physicianDocumentationStatusId, String type, String fabricationStep, String fabricationStepStatus,
                                                  String baseSegment, String prescriptionSegment, String patientSegment, String branchSegment, String statusSegment, String insuranceCompanyIdSegment, String treatingPractitionerSegment, String residentSegment, String clericalUserSegment, String referringPhysicianSegment, String primaryCarePhysicianSegment, String agingSegment, String completedSegment, String prescriptionSectionsSegment, String holdUntilSegment, String sentToBillingSegment, String nymblStatusSegment, String physicianDocumentationStatusSegment, String typeSegment, String fabricationStepSegment, String fabricationStepStatusSegment) {
        return baseSegment +
                (prescriptionId != null ? prescriptionSegment : "") +
                (patientId != null ? patientSegment : "") +
                (branchId != null ? branchSegment : "") +
                (active != null ? statusSegment : "") +
                (insuranceCompanyId != null ? insuranceCompanyIdSegment : "") +
                (treatingPractitionerId != null ? treatingPractitionerSegment : "") +
                (residentId != null ? residentSegment : "") +
                (clericalUserId != null ? clericalUserSegment : "") +
                (referringPhysicianId != null ? referringPhysicianSegment : "") +
                (primaryCarePhysicianId != null ? primaryCarePhysicianSegment : "") +
                (complete != null ? completedSegment : "") +
                (sections != null ? prescriptionSectionsSegment : "") +
                (agingStartDate != null || agingEndDate != null ? agingSegment : "") +
                (showSentToBilling != null && showSentToBilling ? sentToBillingSegment : " PLACEHOLDER rx.id not in (SELECT c.prescription_id as id FROM claim c WHERE c.prescription_id is not null) ") +
                (showOnHold != null && showOnHold ? "" : holdUntilSegment) +
                (nymblStatusId != null ? nymblStatusSegment : "") +
                (physicianDocumentationStatusId != null ? physicianDocumentationStatusSegment : "") +
                (type != null ? typeSegment : "") +
                (fabricationStep != null ? fabricationStepSegment : "") +
                (fabricationStepStatus != null ? fabricationStepStatusSegment : "") +
                " PLACEHOLDER rx.id != 0 ";
    }

    public static String findPrescriptionsQuerySegmentDm(String querySegment, String fieldName, String direction, String offset, Boolean export) {
        String selectSegment = "SELECT distinct rx.*," +
                "(SELECT COUNT(*) FROM prescription_section ps WHERE rx.id = ps.prescription_id AND ps.locked = 0) as incomplete_sections," +
                " ic.name as insurance_name, b.name as branch_name, estimated_value, p.last_name, dt.name as rx_name, ns.name as rx_status,dt.orthotic_or_prosthetic";
        String sortSegment = " GROUP BY rx.id, estimated_value";

        return selectSegment + querySegment + sortSegment;
    }

    private Note sanitizeRxSummaryNote(Note note) {

        String sanitizedString = StringEscapeUtils.unescapeHtml(note.getNote());
        note.setNote(sanitizedString != null ? Jsoup.parse(sanitizedString).text() : "");
        return note;
    }

    public BigInteger findPrescriptionsLength(String querySegment, EntityManager entityManager) {
        String selectSegment = "SELECT COUNT(DISTINCT rx.id)";
        String query = selectSegment + querySegment;
        return (BigInteger) entityManager.createNativeQuery(query).getSingleResult();
    }

    /**
     * Returns count of archived prescriptions for patient
     *
     * @param patientId
     * @return
     */
    public Long getInactivePrescriptionCount(Long patientId) {
        return prescriptionRepository.getPrescriptionCountByPatientIdAndActiveIsFalse(patientId);
    }

    public Long getArchivedPrescriptionCount(Long patientId) {
        return prescriptionRepository.getPrescriptionCountByPatientIdAndArchivedIsTrue(patientId);
    }

    public List<Prescription> findByPatientId(Long patientId, Boolean getArchived) {
        List<Prescription> results = prescriptionRepository.findByPatientId(patientId);
        results = getPrescriptions(getArchived, results);
        for (Prescription o : results) {

            // NC-98: hack because the items under device are not fetching
            DeviceType deviceType = o.getDeviceType();
            if (deviceType != null) {
                Long devTypeId = deviceType.getId();
                if (devTypeId != null && devTypeId > 0) {
                    Optional<DeviceType> optionalD = deviceTypeRepository.findById(devTypeId);
                    if (optionalD.isPresent()) {
                        o.setDeviceType(optionalD.get());
                    }
                }
            }

            loadForeignKeys(o);
        }
        return results;
    }

    private static List<Prescription> getPrescriptions(Boolean getArchived, List<Prescription> results) {
        HashMap<Long, Prescription> prescriptionHashMap = new HashMap<>();


        if (null == getArchived || Boolean.FALSE.equals(getArchived)) {
            getSubPrescriptions(results).forEach(subPrescription -> {
                if (null == subPrescription.getArchived() || Boolean.FALSE.equals(subPrescription.getArchived())) {
                    prescriptionHashMap.put(subPrescription.getId(), subPrescription);
                }
            });
            results.forEach(prescription -> {
                if (null == prescription.getArchived() || Boolean.FALSE.equals(prescription.getArchived())) {
                    prescriptionHashMap.put(prescription.getId(), prescription);
                }
            });
        } else {
            return results;
        }
        return prescriptionHashMap.values().stream()
                .sorted(Comparator.comparing(Prescription::getId)).collect(Collectors.toList());
    }

    public List<Prescription> findByPatientIdAndActiveIsTrue(Long patientId, Boolean getArchived) {
        List<Prescription> results = prescriptionRepository.findByPatientIdAndActiveIsTrue(patientId);
        autoArchivePrescriptions(results);
        results = getPrescriptions(getArchived, results);
        for (Prescription o : results) {

            // NC-98: hack because the items under device are not fetching
            Optional<DeviceType> optionalD = deviceTypeRepository.findById(o.getDeviceTypeId());
            optionalD.ifPresent(o::setDeviceType);
            loadForeignKeys(o);
        }
        return results;
    }

    public List<Prescription> findAllByPatientId(Long patientId) {
    	return prescriptionRepository.findByPatientId(patientId);
    }

    public List<Prescription> findByParentId(Long parentId) {
        List<Prescription> results = prescriptionRepository.findByParentId(parentId);
        return results;
    }

    public List<Prescription> findByPatientIdOrderByActiveDescPrescriptionDateDesc(Long patientId, boolean getArchived) {
        List<Prescription> results = prescriptionRepository.findByPatientIdOrderByActiveDescPrescriptionDateDesc(patientId);
        if (!getArchived) {
            results = results.stream()
                    .filter(prescription -> null == prescription.getArchived() || Boolean.FALSE.equals(prescription.getArchived()))
                    .collect(Collectors.toList());
        }
        for (Prescription o : results) {
            loadForeignKeys(o);
        }
        return results;
    }

    public Prescription findTop1ByPatientIdAndActiveTrueOrderByPrescriptionDateDesc(Long patientId) {
        Prescription result = prescriptionRepository.findTop1ByPatientIdAndActiveTrueOrderByPrescriptionDateDesc(patientId);
        loadForeignKeys(result);
        return result;
    }

    public List<IPrescriptionWithClaimInformation> findWithClaimInformationByPatientId(Long patientId) {
        return prescriptionRepository.findWithClaimInformationByPatientId(patientId);
    }


    public Map<String, List<Object>> countNewPrescriptionsByMonth(Long numberMonths) {
        Map<String, List<Object>> map = new HashMap<>();

        // init our arrays to return to the graph
        List<Object> months = new ArrayList<>();
        List<Object> positions = new ArrayList<>();
        List<Object> newPrescriptions = new ArrayList<>();

        // initialize the calendar, we'll start from last month and go until we've gone numberMonths
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();

        // use a position counter to keep the months in order (across years)
        int pos = 0;

        for (int i = 1; i <= numberMonths; i++) {

            calendar.setTime(date);
            calendar.add(Calendar.MONTH, -1);
            // hold this for the next loop
            date = calendar.getTime();

            int month = calendar.get(Calendar.MONTH) + 1; // Calendar uses a zero-based index for MONTH, SQL uses 1-based ie Jan = 1, Feb = 2
            int year = calendar.get(Calendar.YEAR);
            Long count = prescriptionRepository.countAllByCreatedAtMonth(month, year);

            months.add(calendar.get(Calendar.MONTH)); // zero-based index same as on the angular side
            positions.add(pos++);
            newPrescriptions.add(count);
        }
        map.put("months", months);
        map.put("positions", positions);
        map.put("newPrescriptions", newPrescriptions);
        return map;
    }

    public List<PrescriptionReportDTO> getUnClaimedPrescriptionsForReport(Long branchId, String completeStatus, Date startDate) {
        List<PrescriptionReportDTO> results = new ArrayList<>();
        List<Prescription> prescriptionList = prescriptionRepository.getAllUnclaimedPrescriptions(startDate);
        for (Prescription p : prescriptionList) {
            if (p.getPatientId() != null) {
                String status = "Complete";
                Boolean prescriptionIsComplete = true;
                if (prescriptionSectionService.findByPrescriptionIdAndLockedIsFalse(p.getId()).size() > 0) {
                    prescriptionIsComplete = false;
                    status = "Incomplete";
                }

                if ((completeStatus.equals("incomplete") && !prescriptionIsComplete) || (completeStatus.equals("complete") && prescriptionIsComplete) || completeStatus.equals("all")) {
                    if (branchId == null || (p.getPatient() != null && p.getPatient().getPrimaryBranchId() != null && Objects.equals(p.getPatient().getPrimaryBranchId(), branchId))) {
                        loadForeignKeys(p);
                        results.add(buildReportDTO(p, status));
                    }
                }
            }
        }
        return results;
    }

    public List<PrescriptionDeviceDTO> getAverageDaysToDeliver(Date startDate, Date endDate, Long branchId) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        List<PrescriptionDeviceDTO> results = new ArrayList<>();
        List<Object[]> deviceTypeObj = prescriptionRepository.findAverageDaysToDeliveryBetween(dateStart, dateEnd, branchId);
        for (Object[] dt : deviceTypeObj) {
            String name = dt[0].toString();
            String daysToDeliver = dt[1].toString();
            String beenUsed = dt[2].toString();

            // Next line is doing a lot of conversions to get the object from the query to a string so it can be parsed
            // to a double so it can be rounded to one decimal and returned back to a string.
            String avg = Double.toString((double) Math.round(Double.parseDouble(dt[3].toString()) * 10d) / 10d);

            String maxDay = dt[4].toString().equals("0") ? "1" : dt[4].toString();
            String minDay = dt[5].toString().equals("0") ? "1" : dt[5].toString();
            results.add(new PrescriptionDeviceDTO(name, beenUsed, daysToDeliver, avg, maxDay, minDay));
        }
        return results;
    }

    private PrescriptionReportDTO buildReportDTO(Prescription prescription, String status) {
        PrescriptionReportDTO result = new PrescriptionReportDTO();
        result.setId(prescription.getId());
        result.setPatient(prescription.getPatient());
        result.setCreatedAt(prescription.getCreatedAt());
        result.setDays(DateUtil.getAgeInDaysFromSQLTimeStamp(prescription.getCreatedAt()));
        result.setPrescriptionDate(prescription.getPrescriptionDate());
        result.setDeviceName(prescription.getDeviceType() != null ? prescription.getDeviceType().getName() : "UNKNOWN");
        result.setPractitioner(prescription.getTreatingPractitioner());
        result.setStatus(status);
        result.setDeviceType(prescription.getDeviceType() != null ? prescription.getDeviceType().getOrthoticOrProsthetic() : "UNKNOWN");

//        List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdWithCalculatedFirstCarrier(prescription.getId());
        List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "primary");
        if (ivlcs.isEmpty()) {
            ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "secondary");
        }
        if (ivlcs.isEmpty()) {
            ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "tertiary");
        }
        if (ivlcs.isEmpty()) {
            ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "quaternary");
        }
        if (ivlcs.isEmpty()) {
            ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "quinary");
        }
        if (ivlcs.isEmpty()) {
            ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "senary");
        }
        if (ivlcs.isEmpty()) {
            ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "septenary");
        }
        if (ivlcs.isEmpty()) {
            ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "octonary");
        }
        if (ivlcs.isEmpty()) {
            ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "nonary");
        }
        if (ivlcs.isEmpty()) {
            ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(prescription.getId(), "denary");
        }
        BigDecimal totalAllowable = BigDecimal.ZERO;
        for (InsuranceVerification_L_Code ivlc : ivlcs) {
            if (ivlc.getTotalAllowable() != null) {
                totalAllowable = totalAllowable.add(ivlc.getTotalAllowable());
            }
        }
        result.setEstimate(totalAllowable);
        return result;

    }

    public List<Prescription> findByPatientRecallIsTrue() {
        List<Prescription> results = prescriptionRepository.findByPatientRecallIsTrue();
        for (Prescription o : results)
            loadForeignKeys(o);
        return results;
    }

    public List<Prescription> findByInsuranceCompanyWithNoClaimSubmissionsAndTimelyFilingToday(Long insuranceCompanyId) {
//        System.out.println("findByInsuranceCompanyWithNoClaimSubmissionsAndTimelyFilingTodayQuery = " + findByInsuranceCompanyWithNoClaimSubmissionsAndTimelyFilingTodayQuery.replaceAll(":insuranceCompanyId", insuranceCompanyId + ""));
        List<Prescription> results = prescriptionRepository.findByInsuranceCompanyWithNoClaimSubmissionsAndTimelyFilingToday(insuranceCompanyId);
        for (Prescription o : results) {
            loadForeignKeys(o);
        }
        return results;
    }

    public List<Map<String, Object>> findByCategory(Long branchId, String category, java.sql.Date startDate, java.sql.Date endDate) {

        List<Map<String, Object>> results = new ArrayList<>();
        List<Prescription> prescriptions;
        if (branchId != null) {
            prescriptions = prescriptionRepository.findByPatientPrimaryBranchId(branchId.intValue(), startDate, endDate);
        } else {
            prescriptions = prescriptionRepository.findByPrescriptionDateIsBetween(startDate, endDate);
        }

        for (Prescription prescription : prescriptions) {
            if (!StringUtil.isBlank(category)) {
                if (prescription.getDeviceType() != null &&
                        prescription.getDeviceType().getOrthoticOrProsthetic() != null &&
                        prescription.getDeviceType().getOrthoticOrProsthetic().equals(category) &&
                        prescription.getPatientId() != null) {

                    Map<String, Object> map = new HashMap<>();
                    Date dateOfService = new Date(0);
                    ServiceEstimate serviceEstimate = serviceEstimateService.findByPrescriptionId(prescription.getId());
                    List<Claim> claims = claimRepository.findByPrescriptionId(prescription.getId());
                    for (Claim claim : claims) {
                        if (claim.getDateOfService() != null && claim.getDateOfService().after(dateOfService)) {
                            dateOfService = claim.getDateOfService();
                        }
                    }
                    map.put("prescription", prescription);
                    map.put("serviceEstimate", serviceEstimate);
                    if (dateOfService.after(new Date(0))) {
                        map.put("dateOfService", dateOfService);
                    }
                    results.add(map);
                }
            }
        }

        return results;

    }

    private boolean isUpdating(PrescriptionDiagnosisCode c, List<PrescriptionDiagnosisCode> list) {
        return contains(list, c);
    }

    private BigDecimal getClaimTotalBalance(Long prescriptionId) {
        BigDecimal result = BigDecimal.ZERO;
        List<Claim> claims = claimRepository.findByPrescriptionId(prescriptionId);
        for (Claim c : claims)
            result = result.add(c.getTotalClaimBalance());
        return result;
    }

    public List<PrescriptionWithoutSurveyTextDTO> prescriptionsWithoutSurveyText(Long branchId, Date startDate, Date endDate) {
        List<Prescription> prescriptions;
        if (branchId != null) {
            prescriptions = prescriptionRepository.findByBranchIdAndSurveySentDateIsNullAndClaimDOSBetween(branchId, startDate, endDate);
        } else {
            prescriptions = prescriptionRepository.findBySurveySentDateIsNullAndClaimDOSBetween(startDate, endDate);
        }
        prescriptions = prescriptions.stream().filter(c -> c.getPatient().getCellPhone() != null && c.getPatient().getCellPhone().length() > 9).collect(Collectors.toList());
        SystemSetting setting = systemSettingService.findBySectionAndField("general", "survey_link_type");
        List<PrescriptionWithoutSurveyTextDTO> results = new ArrayList<>();
        for (Prescription o : prescriptions) {
            loadForeignKeys(o);
            PrescriptionWithoutSurveyTextDTO dto = createPrescriptionWithoutSurveyLinkDTO(o, setting.getValue());
            results.add(dto);
        }
        return results;
    }

    private PrescriptionWithoutSurveyTextDTO createPrescriptionWithoutSurveyLinkDTO(Prescription rx, String setting) {
        PrescriptionWithoutSurveyTextDTO dto = new PrescriptionWithoutSurveyTextDTO();
        dto.setPrescriptionId(rx.getId());
        dto.setSurveyCompleted(rx.getSurveyCompleted());
        dto.setPatient(rx.getPatient());
        dto.setDeviceTypeName(rx.getDeviceType().getName());
        dto.setCellPhone(rx.getPatient().getCellPhone());
        dto.setPrimaryBranchName(rx.getPatient().getPrimaryBranch().getName());
        dto.setDeliveredOn(rx.getDeliveredOn());
        if (setting.equals("l_code")) {
            List<Prescription_L_Code> plcs = prescriptionLCodeRepository.findByPrescriptionIdOrderByOrderNum(rx.getId());
            Boolean spanishText = rx.getPatient().getSpanishText() != null && rx.getPatient().getSpanishText();
            dto.setLanguage(spanishText ? "Spanish" : "English");
            dto.setSurveyName(spanishText ? plcs.get(0).getLCode().getSpanishSurveyLinkName() : plcs.get(0).getLCode().getSurveyLinkName());
            dto.setSurveyLink(spanishText ? plcs.get(0).getLCode().getSpanishSurveyLink() : plcs.get(0).getLCode().getSurveyLink());
        } else {
            dto.setLanguage(null);
            dto.setSurveyName(null);
            dto.setSurveyLink(rx.getDeviceType().getSurvey_link());
        }
        return dto;
    }

    public List<Prescription> prescriptionsOnHold(Date startDate, Date endDate) {
        List<Prescription> results = prescriptionRepository.findByHoldUntilDateBetween(startDate, endDate);
        for (Prescription o : results) loadForeignKeys(o);
        return results;
    }

    public List<Map<String, Object>> deliveredPrescriptionsList(Long branchId, Date startDate, Date endDate) {
        boolean requiresClaimSubmission = false;
        List<Map<String, Object>> results = new ArrayList<>();
        List<Claim> claims;
        if (branchId == null) {
            claims = claimRepository.findByDateOfServiceBetween(startDate, endDate);
        } else {
            claims = claimRepository.findByPrescriptionClaimBranchIdAndDateOfServiceBetween(branchId, startDate, endDate, false, requiresClaimSubmission);
        }

        for (Claim c : claims) {
            Prescription p = c.getPrescription();
            loadForeignKeys(p);
            Map<String, Object> m = new HashMap<>();
            m.put("claim", c);
            m.put("prescription", p);
            m.put("open", false);
            List<Prescription_L_Code> plcs = prescriptionLCodeRepository.findByPrescriptionId(p.getId());
            StringBuilder sb = new StringBuilder();
            for (Prescription_L_Code plc : plcs) {
//                BigDecimal totalAllowable = (plc.getAllowableFee() == null ? BigDecimal.ZERO : plc.getAllowableFee()).multiply(new BigDecimal(plc.getQuantity() == null ? 0L : plc.getQuantity()));
                if (sb.length() > 0) {
                    sb.append(", ");
                }
                sb.append("<strong>");
                sb.append(plc.getLCode().getName());
                sb.append("</strong>");
//                sb.append((" (").concat("$").concat(totalAllowable.toPlainString()).concat(")"));
            }
            m.put("prescriptionLCodes", sb.toString());
            results.add(m);
        }
        return results;
    }

    public List<Prescription> findByProjectedDeliveryDateBetween(Date startDate, Date endDate, Long branchId) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        List<Prescription> prescriptions = prescriptionRepository.findByProjectedDeliveryDateBetweenAndBranchId(dateStart, dateEnd, branchId);
        for (Prescription p : prescriptions) {
            loadForeignKeys(p);
        }
        return prescriptions;
    }

    public List<Prescription> findByProjectedDeliveryDateBetweenWithNoClaimSubmission(Date startDate, Date endDate, Long branchId) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        List<Prescription> prescriptions = prescriptionRepository.findByProjectedDeliveryDateBetweenWithNoClaimSubmission(dateStart, dateEnd, branchId);
        for (Prescription p : prescriptions) {
            loadForeignKeys(p);
        }
        return prescriptions;
    }

    public Prescription duplicatePrescription(Long parentId, Integer currentPeriod, List<PatientInsuranceDTO> dtoList) {
        Prescription parentPrescription = findOne(parentId);
        if (parentPrescription == null) {
            return null;
        }
        // here parent prescription IS the previous prescription
        return duplicatePrescription(parentPrescription, parentPrescription, currentPeriod, dtoList);
    }

    /**
     * In some cases, (chronologically) previous prescription is not the same as parent prescription
     * which is (chronologically) the first in chain
     *
     * @param parentPrescription
     * @param previousPrescription
     * @param currentPeriod
     * @param dtoList
     * @return
     */
    public Prescription duplicatePrescription(Prescription parentPrescription, Prescription previousPrescription,
                                              Integer currentPeriod, List<PatientInsuranceDTO> dtoList) /*throws Exception*/ {
        Long parentPrescriptionId = parentPrescription.getId();
        // Find prescription to duplicate & save duplication
        Prescription newPrescription = parentPrescription.shallowCopy();
        if (currentPeriod == null) {
            newPrescription.setDuplicatedFromId(parentPrescriptionId);
            newPrescription.setCreatedById(userService.getCurrentUser().getId());
            newPrescription.setSignedDate(null);
            newPrescription.setSignature(null);
            newPrescription.setManualPodSignedDate(null);
            newPrescription.setManualFrSignedDate(null);
            newPrescription.setManualDwoSignedDate(null);
            newPrescription.setSignedUserId(null);
            newPrescription.setUserSignedDate(null);
            newPrescription.setSignedUser(null);
            newPrescription.setDeliveredOn(null);
            if (!dtoList.isEmpty() && newPrescription.getParentId() == null) {
                newPrescription.setParentId(parentPrescriptionId);
            }
        } else {
            newPrescription.setParentId(parentPrescriptionId);
            newPrescription.setPrescriptionDate(parentPrescription.getNextPrescriptionDate());
            newPrescription.setNextPrescriptionDate(null);
            newPrescription.setRentalBillingPeriods(null);
            newPrescription.setRentalStartPeriod(null);
            newPrescription.setIsRentalPaused(false);
        }
        newPrescription.setId(null);
        newPrescription.setVersion(1);
        newPrescription = save(newPrescription);

        // Prescription diagnosis codes
        prescriptionDiagnosisCodeService.duplicateForPrescription(parentPrescriptionId, newPrescription.getId());

        // Create Prescription Sections
        prescriptionSectionService.createPrescriptionSections(newPrescription.getId());

        List<InsuranceVerification_L_Code> ivlcs = duplicateHCPCSList(parentPrescription,
                previousPrescription.getId(), newPrescription.getId(), currentPeriod);

        duplicateInsuranceVerifications(parentPrescriptionId, newPrescription.getId(), ivlcs, dtoList);

//        detailedWrittenOrderService.duplicateForPrescription(prescriptionId, newPrescription.getId(), currentPeriod);
        DetailedWrittenOrder dwo = detailedWrittenOrderService.findByPrescriptionId(parentPrescriptionId);
        if (dwo != null) {
            dwo.setId(null);
            dwo.setPrescriptionId(newPrescription.getId());
            if (currentPeriod == null) {
                dwo.setSignature(null);
                dwo.setSignedDate(null);
            }
            detailedWrittenOrderService.save(dwo);
        }

        // Service Estimate
        ServiceEstimate serviceEstimate = serviceEstimateService.findByPrescriptionId(parentPrescriptionId);
        if (serviceEstimate != null) {
            serviceEstimate.setId(null);
            serviceEstimate.setPrescriptionId(newPrescription.getId());
            serviceEstimateService.save(serviceEstimate);
        }

        // Financial Responsibility
        FinancialResponsibility financialResponsibility = financialResponsibilityService.findByPrescriptionId(parentPrescriptionId);
        if (financialResponsibility != null) {
            financialResponsibility.setId(null);
            financialResponsibility.setPrescriptionId(newPrescription.getId());
            if (currentPeriod == null) {
                financialResponsibility.setSignedBy(null);
                financialResponsibility.setSignerId(null);
                financialResponsibility.setSignature(null);
                financialResponsibility.setSigner(null);
                financialResponsibility.setSignedDate(null);
            }
            financialResponsibilityService.save(financialResponsibility);
        }

        // WIP Fabrication Checklist Steps
        List<Checklist> checklists = checklistService.findByPrescriptionId(parentPrescriptionId);
        for (Checklist checklist : checklists) {
            checklistService.createChecklistAndChecklistSteps(checklist.getChecklistTemplateId(), newPrescription.getId(), checklist.getType(), checklist.getUserId());
        }

        notificationService.createPrescriptionMessage(newPrescription.getId(), "A prescription has been assigned to you");

        // Return duplicated prescription
        return newPrescription;
    }

    public void duplicateInsuranceVerifications(Long prescriptionId,
                                                Long newPrescriptionId,
                                                List<InsuranceVerification_L_Code> ivlcs,
                                                List<PatientInsuranceDTO> dtoList) {
        // Insurance Verification & finalize save of IVLCs
        List<InsuranceVerification> insuranceVerifications = insuranceVerificationService.findByPrescriptionId(prescriptionId);
        boolean primaryChangingOnRxWithClaimSubmission = !dtoList.isEmpty();
        for (InsuranceVerification insuranceVerification : insuranceVerifications) {
            Long originalInsuranceVerificationId = insuranceVerification.getId();
            insuranceVerification.setId(null);
            insuranceVerification.setPrescriptionId(newPrescriptionId);
            if (primaryChangingOnRxWithClaimSubmission) {
                PatientInsuranceDTO dto = dtoList.stream().filter(e -> e.getInsuranceVerification().getId().equals(originalInsuranceVerificationId)).findFirst().orElse(null);
                if (dto != null) {
                    insuranceVerification.setCarrierType(dto.getInsuranceVerification().getCarrierType());
                }
            }
            insuranceVerification = insuranceVerificationService.save(insuranceVerification);
            for (InsuranceVerification_L_Code ivlc : ivlcs) {
                if (ivlc.getInsuranceVerificationId().equals(originalInsuranceVerificationId)) {
                    ivlc.setInsuranceVerificationId(insuranceVerification.getId());
                }
                insuranceVerificationLCodeService.save(ivlc);
            }
        }
    }

    public List<InsuranceVerification_L_Code> duplicateHCPCSList(Prescription parentPrescription,
                                                                 Long previousPrescriptionId,
                                                                 Long newPrescriptionId,
                                                                 Integer currentPeriod) {
        Boolean useRentalAutoBill = "Y".equals(systemSettingService.findBySectionAndField("claim", "use_rental_auto_bill").getValue());
    /* IVLCs are tracked outside the next two for-loops to prevent nested for-loops while making it easy to change their
       prescriptionLCode and insuranceVerification ids
     */
        List<InsuranceVerification_L_Code> ivlcs = new ArrayList<>();
        // Create prescriptionLCodes
        java.sql.Date dateOfServiceStart = parentPrescription.getNextPrescriptionDate();

        List<Prescription_L_Code> plcs = prescriptionLCodeRepository.findByPrescriptionId(parentPrescription.getId());
        List<Prescription_L_Code> previousPlcs = prescriptionLCodeRepository.findByPrescriptionId(previousPrescriptionId);
        if (plcs == null || plcs.isEmpty()) {
            throw new RuntimeException("There are no prescription L Codes for prescriptionId: " + parentPrescription.getId());
        }
        for (Prescription_L_Code plc : plcs) {
//            Long plcOldId = plc.getId();
            final Prescription_L_Code fplc = plc;
            List<Prescription_L_Code> filteredPreviousPLCs = previousPlcs.stream().filter(
                    previous -> previous.getLCodeId().equals(fplc.getLCodeId()) && fplc.getOrderNum().equals(previous.getOrderNum())
            ).toList();
            List<InsuranceVerification_L_Code> plcIvlcs = insuranceVerificationLCodeService.findByPrescriptionLCodeId(plc.getId());
            plc.setId(null);
            plc.setPrescriptionId(newPrescriptionId);
            if (currentPeriod != null && "RR".equals(plc.getModifier1())) {
                if (currentPeriod == 2 || currentPeriod == 3) {
                    plc.setModifier2("KI");
                } else if (currentPeriod >= 4) {
                    plc.setModifier2("KJ");
                }
            }
            if (currentPeriod != null &&
                    currentPeriod > 1 &&
                    ("NU".equals(plc.getModifier1()) ||
                            (StringUtils.isBlank(plc.getModifier1()) &&
                                    StringUtils.isBlank(plc.getModifier2()) &&
                                    StringUtils.isBlank(plc.getModifier3()) &&
                                    StringUtils.isBlank(plc.getModifier4()))) &&
                    useRentalAutoBill)
                continue;

            LocalDate dateOfServiceEnd = dateOfServiceStart == null ? null : dateOfServiceStart.toLocalDate().plusMonths(1L).minusDays(1L);

            // TODO: this is a "placeholder" for the date of service logic
            //plcNew.setDateOfService(dateOfServiceStart);
            //plcNew.setDateOfServiceEnd(java.sql.Date.valueOf(dateOfServiceEnd));

            // TODO: this is a "placeholder" for the rentalStatus logic
            //plcOld.setRentalStatus("IN");
            //prescriptionLCodeRepository.saveAndFlush(plcOld);

            plc = prescriptionLCodeRepository.save(plc);
            // Physical Items
            if (!filteredPreviousPLCs.isEmpty()) {
                List<ItemPhysical> physicalItems = itemPhysicalRepository.findByPrescription(previousPrescriptionId, filteredPreviousPLCs.get(0).getId());
                if (physicalItems != null && !physicalItems.isEmpty()) {
                    for (ItemPhysical itemPhysical : physicalItems) {
                        itemPhysical.setDateOfServiceStart(dateOfServiceStart);
                        if (dateOfServiceEnd != null) {
                            itemPhysical.setDateOfServiceEnd(java.sql.Date.valueOf(dateOfServiceEnd));
                        }
                        itemPhysical.setPrescriptionId(newPrescriptionId);
                        itemPhysical.setPrescriptionLCodeId(plc.getId());
                        itemPhysicalRepository.save(itemPhysical);
                    }
                }
            } else {
                log.warn("There should be 1 HCPCS in the filtered list. Please check the HCPCS on the parent prescription #{} for `{}`",
                        parentPrescription.getId(), TenantContext.getCurrentTenant());
            }
            // IVLCs
            for (InsuranceVerification_L_Code plcIvlc : plcIvlcs) {
                plcIvlc.setId(null);
                plcIvlc.setPrescriptionLCodeId(plc.getId());
                if (currentPeriod != null && "RR".equals(plc.getModifier1())) {
                    if (currentPeriod >= 4) {
                        Long rentalPercentage = plc.getLCode().getRentalPercentage();
                        BigDecimal allowable = plcIvlc.getAllowableFee();
                        BigDecimal totalAllowable = plcIvlc.getTotalAllowable();
                        if (rentalPercentage != null && rentalPercentage > 0L) {
                            float v = rentalPercentage / 100f;
                            allowable = allowable.multiply(new BigDecimal(v));
                            totalAllowable = totalAllowable.subtract(totalAllowable.multiply(new BigDecimal(v)));
                        }
                        plcIvlc.setAllowableFee(allowable);
                        plcIvlc.setTotalAllowable(totalAllowable);
                    }
                }
                ivlcs.add(plcIvlc);
            }
        }
        return ivlcs;
    }

    public EstimatedMonthlyDeliveryDTO estimatedMonthlyDeliveryWidget(String range, String todayString, List<Long> branchIds) {
        EstimatedMonthlyDeliveryDTO result = new EstimatedMonthlyDeliveryDTO();
        LocalDate today = LocalDate.parse(todayString);
        LocalDate startDate;
        LocalDate endDate;

        if (range.equals("month")) {
            startDate = LocalDate.of(today.getYear(), today.getMonthValue(), 1);
            endDate = LocalDate.of(today.getYear(), today.getMonthValue(), 1).plusMonths(1).minusDays(1);
        } else {
            startDate = LocalDate.of(today.getYear(), today.getMonthValue(), 1).plusMonths(1);
            endDate = LocalDate.of(today.getYear(), today.getMonthValue(), 1).plusYears(1000);
        }
        Date utilStartDate = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date utilEndDate = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        String dateStart = DateUtil.getStringDate(utilStartDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(utilEndDate), DF_YYYY_MM_DD_HH_MM);
//        List<Prescription> prescriptions = prescriptionRepository.findByProjectedDeliveryDateBetween(utilStartDate, utilEndDate);
        List<Prescription> prescriptions = prescriptionRepository.findByProjectedDeliveryDateBetweenAndBranchIdIn(dateStart, dateEnd, branchIds);
        BigDecimal totalAllowableSum = BigDecimal.ZERO;
        List<EstimatedMonthlyDeliveryDTO.EstimatedMonthlyPractitionerDeliveryDTO> practitionerDeliveryDTOs = new ArrayList<>();
        Map<Long, BigDecimal> practitionerHashMap = new HashMap<>();
        for (Prescription rx : prescriptions) {
            if (rx.getTreatingPractitionerId() != null) {
                InsuranceVerification iv = insuranceVerificationService.findFirstByPrescriptionIdAndCarrierTypeOrderByIdDesc(rx.getId(), "primary");
                BigDecimal totalAllowable = BigDecimal.ZERO;
                if (iv != null) {
                    List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByInsuranceVerificationId(iv.getId());
                    for (InsuranceVerification_L_Code ivlc : ivlcs) {
                        totalAllowable = totalAllowable.add(ivlc.getTotalAllowable() != null ? ivlc.getTotalAllowable() : BigDecimal.ZERO);
                    }
                }
                if (practitionerHashMap.get(rx.getTreatingPractitionerId()) != null) {
                    practitionerHashMap.replace(rx.getTreatingPractitionerId(), practitionerHashMap.get(rx.getTreatingPractitionerId()).add(totalAllowable));
                } else {
                    practitionerHashMap.put(rx.getTreatingPractitionerId(), totalAllowable);
                }
                totalAllowableSum = totalAllowableSum.add(totalAllowable);
            }
        }
        for (Long userId : practitionerHashMap.keySet()) {
            User treatingPractitioner = userService.findOne(userId);
            EstimatedMonthlyDeliveryDTO.EstimatedMonthlyPractitionerDeliveryDTO dto = new EstimatedMonthlyDeliveryDTO.EstimatedMonthlyPractitionerDeliveryDTO();
            dto.setPractitioner(treatingPractitioner);
            dto.setTotalAllowable(practitionerHashMap.get(userId));
            practitionerDeliveryDTOs.add(dto);
        }
        result.setTotalAllowableSum(totalAllowableSum);
        result.setEstimatedMonthlyPractitionerDeliveryDTOList(practitionerDeliveryDTOs);
        return result;
    }

    public void updatePrescriptionHCPCFees(Prescription prescription, Boolean bForceRural) throws Exception {
        List<Claim> prescriptionClaims = claimService.findByPrescriptionId(prescription.getId());
        if (prescriptionClaims != null && !prescriptionClaims.isEmpty()) {
            for (Claim claim : prescriptionClaims) {
                if (claim.getClaimSubmissions() != null && !claim.getClaimSubmissions().isEmpty()) {
                    return;
                }
            }
        }

        List<InsuranceVerification> insuranceVerifications = insuranceVerificationService.findByPrescriptionId(prescription.getId());
        BigDecimal claimGrandTotal = BigDecimal.ZERO;

        boolean isRuralEnabled = "Y".equals(systemSettingService.findBySectionAndField("billing", "rural_non_rural_on").getValue());
        boolean crtEnabled = "Y".equals(systemSettingService.findBySectionAndField("general", "hide_device_type").getValue());

        for (InsuranceVerification iv : insuranceVerifications) {
            List<InsuranceVerification_L_Code> insuranceVerificationLCodes = insuranceVerificationLCodeService.findByInsuranceVerificationId(iv.getId());
            for (InsuranceVerification_L_Code ivlc : insuranceVerificationLCodes) {
                FeeSchedule billableFeeSchedule = ivlc.getInsuranceVerification().getPatientInsurance().getInsuranceCompany().getDefaultBillingAmount();
                FeeSchedule allowableFeeSchedule = ivlc.getInsuranceVerification().getPatientInsurance().getInsuranceCompany().getDefaultAllowable();
                Prescription_L_Code plc = ivlc.getPrescriptionLCode();
                L_CodeFee billableFee = lCodeFeeService.getByLCodeIdAndFeeScheduleIdOrderByLCodeIdAsc(plc.getLCodeId(), billableFeeSchedule.getId(), false, crtEnabled, plc.getModifier1(), plc.getModifier2(), plc.getModifier3(), plc.getModifier4());
                L_CodeFee allowableFee = lCodeFeeService.getByLCodeIdAndFeeScheduleIdOrderByLCodeIdAsc(plc.getLCodeId(), allowableFeeSchedule.getId(), false, crtEnabled, plc.getModifier1(), plc.getModifier2(), plc.getModifier3(), plc.getModifier4());
                billableFee = lCodeFeeService.applyPriceOption(billableFee, plc);
                allowableFee = lCodeFeeService.applyPriceOption(allowableFee, plc);
                if (billableFee != null && allowableFee != null) {
                    Prescription_L_Code temp = prescriptionLCodeRepository.findById(ivlc.getPrescriptionLCodeId()).get();
                    BigDecimal quantity = new BigDecimal(temp.getQuantity().toString());

                    BigDecimal allowable = allowableFee.getFee();
                    BigDecimal billable = billableFee.getFee();
                    BigDecimal allowableRural = allowableFee.getRuralFee() != null ? allowableFee.getRuralFee() : BigDecimal.ZERO;
                    BigDecimal billableRural = billableFee.getRuralFee() != null ? billableFee.getRuralFee() : BigDecimal.ZERO;
                    if (Boolean.TRUE.equals(isRuralEnabled) && Boolean.TRUE.equals(bForceRural)) {
                        allowable = allowableRural;
                        billable = billableRural;
                    }
                    if (billable.signum() != 0) {
                        ivlc.setBillingFee(billable);
                        ivlc.setTotalCharge(billable.multiply(quantity));
                    }
                    if (allowable.signum() != 0) {
                        ivlc.setAllowableFee(allowable);
                        ivlc.setTotalAllowable(allowable.multiply(quantity));
                    }
                    ivlc.setSalesTax(insuranceVerificationLCodeService.calculateIVLCSalesTax(ivlc));
                    insuranceVerificationLCodeService.save(ivlc);

                    if (iv.getCarrierType().equals("primary")) {
                        claimGrandTotal = claimGrandTotal.add(ivlc.getTotalCharge());
                    }
                }
            }
        }

        FinancialResponsibility financialResponsibility = financialResponsibilityService.findByPrescriptionId(prescription.getId());
        List<Claim> claims = claimRepository.findByPrescriptionId(prescription.getId());
        for (Claim claim : claims) { // SCRUM-1682
            boolean isSelfPay = financialResponsibility != null && financialResponsibility.getSelfPay() != null && financialResponsibility.getSelfPay();
            claim.setTotalClaimAmount(claimGrandTotal);
            claim.setTotalClaimBalance(isSelfPay ? BigDecimal.ZERO : claimGrandTotal); // SCRUM-1768 (follow-up)
            claim.setTotalPtResponsibilityAmount(isSelfPay ? claimGrandTotal : claim.getTotalPtResponsibilityAmount());
            claim.setTotalPtResponsibilityBalance(claim.getTotalPtResponsibilityAmount().subtract(claim.getTotalPtResponsibilityPaid())); // SCRUM-2017
            claimService.save(claim);
        }
    }

    public Prescription updatePrescriptionHCPCFeesFromId(Long prescriptionId, Boolean isRural, Boolean willSaveRx) throws Exception {
        Prescription prescription = findOne(prescriptionId);
        updatePrescriptionHCPCFees(prescription, isRural);
        if (willSaveRx) {
            prescription = save(prescription);
        }
        return prescription;
    }

    public Boolean updateFeesForPrescriptionsWithNoClaimNumber() throws Exception {
        List<Prescription> results = prescriptionRepository.getAllActivePrescriptionsWithNoClaimSubmissions();
        for (Prescription prescription : results) {
            updatePrescriptionHCPCFees(prescription, null);
        }
        return true;
    }

    public Map<Long, PrescriptionClaimsDTO> findAllActiveRxAndPtClaimsGroupedByPrescriptionId(java.sql.Date startDate, java.sql.Date endDate, Long branchId, boolean useSubmission) {
        Map<Long, PrescriptionClaimsDTO> result = new HashMap<>();
        List<ClaimLightWeightDTO> includedList = buildLightWeightClaimDTO(startDate, endDate, branchId, useSubmission);

        for (ClaimLightWeightDTO c : includedList) {
            PrescriptionClaimsDTO rxClaim = new PrescriptionClaimsDTO();
            if (result.containsKey(c.getPrescriptionId())) {
                rxClaim = result.get(c.getPrescriptionId());
            } else {
                rxClaim.setPrescriptionId(c.getPrescriptionId());
            }
            if (null != c.getCarrier_type()) {
                switch (c.getCarrier_type()) {
                    case "primary":
                        rxClaim.setPrimaryClaim(c);
                        break;
                    case "secondary":
                        rxClaim.setSecondaryClaim(c);
                        break;
                    case "tertiary":
                        rxClaim.setTertiaryClaim(c);
                        break;
                    case "quaternary":
                        rxClaim.setQuaternaryClaim(c);
                        break;
                    case "quinary":
                        rxClaim.setQuinaryClaim(c);
                        break;
                    case "senary":
                        rxClaim.setSenaryClaim(c);
                        break;
                    case "septenary":
                        rxClaim.setSeptenaryClaim(c);
                        break;
                    case "octonary":
                        rxClaim.setOctonaryClaim(c);
                        break;
                    case "nonary":
                        rxClaim.setNonaryClaim(c);
                        break;
                    case "denary":
                        rxClaim.setDenaryClaim(c);
                        break;
                    case "other":
                        rxClaim.setOther(c);
                        break;
                    default:
                        break;
                }
            }
            //Taking care of patient Balance Data for new data point.
            if (c.getPatientBalance().compareTo(BigDecimal.ZERO) != 0) {
                rxClaim.setPatientBalance(c);
            }
            result.put(c.getPrescriptionId(), rxClaim);
        }
        return result;
    }

    public List<ClaimLightWeightDTO> buildLightWeightClaimDTO(java.sql.Date startDate, java.sql.Date endDate, Long branchId, boolean useSubmission) {
        //System.out.println("claimRepository.findAllLightWeightClaimByBranchQuery = \n" + claimRepository.findAllLightWeightClaimByBranchQuery);

        List<Object[]> data = claimRepository.findAllLightWeightClaimByBranch(startDate, endDate, branchId, useSubmission);
        List<Claim> claimList = claimRepository.findFirstClaimSubmissionOnly(startDate, endDate, branchId);
        Set<Long> claimIdSet = new HashSet<>();
        for (Claim claim : claimList) {
            claimIdSet.add(claim.getId());
        }

        List<ClaimLightWeightDTO> result = new ArrayList<>();
        for (Object[] o : data) {
            ClaimLightWeightDTO c = new ClaimLightWeightDTO();
            c.setPatientId(Long.valueOf(o[0].toString()));
            c.setPrescriptionId(Long.valueOf(o[1].toString()));
            c.setClaimId(Long.valueOf(o[2].toString()));
            c.setPatientInsuranceId(Long.valueOf(o[3].toString()));
            c.setInsuranceId(Long.valueOf(o[4].toString()));
            c.setDateOfService(o[5] != null ? java.sql.Date.valueOf(o[5].toString()) : null);
            c.setClaimBalance(NumberUtil.getBigDecimalValue(o[6]));
            c.setPatientBalance(NumberUtil.getBigDecimalValue(o[7]));
            c.setPatientName(o[8].toString() + " " + o[9].toString());
            if (Boolean.parseBoolean(o[10].toString())) {
                c.setClaimBalance(c.getPatientBalance());
                c.setPatientBalance(BigDecimal.ZERO);
            }
            if (c.getPatientBalance().toString().equals("0.00")) {
                c.setPatientBalance(BigDecimal.ZERO);
            }
            if (c.getClaimBalance().toString().equals("0.00")) {
                c.setClaimBalance(BigDecimal.ZERO);
            }
            c.setCarrier_type(o[11].toString());
            c.setTotalAllowable(new BigDecimal(o[12].toString()));
            c.setTotalBillable(new BigDecimal(o[13].toString()));
            if (useSubmission) {
                ClaimSubmission cs = claimSubmissionService.findByClaimIdFirstSubmission(c.getClaimId());
                if (cs != null) {
                    c.setPrimaryDate(cs.getSubmissionDate()); //Done to use in calculation
                    c.setAging(cs.getSubmissionDate() != null ? cs.getSubmissionDate().toLocalDate().until(endDate.toLocalDate(), ChronoUnit.DAYS) : -1);
                    c.setSubmissionDate(cs.getSubmissionDate());
                } else {
                    c.setAging(-1L);
                }
            } else {
                c.setAging(c.getDateOfService().toLocalDate().until(endDate.toLocalDate(), ChronoUnit.DAYS));
            }
            if (!useSubmission || claimIdSet.contains(c.getClaimId())) {
                result.add(c);
            } else {
                System.out.println("Skipped claim id = " + c.getClaimId());
            }
        }
        return result;
    }

    public List<Prescription> getPatientByReferralSource(Date startDate, Date endDate, Long branchId) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        List<Prescription> results = prescriptionRepository.findAllByReferralSource(dateStart, dateEnd, branchId);
        for (Prescription rx : results) {
            loadForeignKeys(rx);
        }
        return results;
    }

    public List<ClaimSubmission> getClaimSubmissionsForPrescription(Long prescriptionId) {
        List<ClaimSubmission> results = prescriptionRepository.getClaimSubmissionsForPrescription(prescriptionId);
        return results;
    }

    public ClaimSubmission getFirstClaimSubmissionForPrescription(Long prescriptionId) {
//        ClaimSubmission result = prescriptionRepository.getFirstClaimSubmissionForPrescription(prescriptionId);
        List<ClaimSubmission> submissions = getClaimSubmissionsForPrescription(prescriptionId);
        ClaimSubmission result = submissions.size() > 0 ? submissions.get(0) : null;
        return result;
    }

    public Boolean checkForClaimSubmissionOrPayment(Long prescriptionId) {
        ClaimSubmission cs = getFirstClaimSubmissionForPrescription(prescriptionId);
        List<Payment> payments = paymentService.findByPrescriptionId(prescriptionId);
        return cs != null || payments.size() > 0;
    }

    public List<Prescription> findAllByNextPrescriptionDate(Date nextPrescriptionDate) {
        return prescriptionRepository.findAllByNextPrescriptionDate(nextPrescriptionDate);
    }

    public List<Prescription> findAllByParentId(Long parentId) {
        return prescriptionRepository.findAllByParentId(parentId);
    }

    public List<Prescription> getRentalChildPrescriptionsByClaimId(Long claimId) {
        return prescriptionRepository.getRentalChildPrescriptionsByClaimId(claimId);
    }

    public void resetRentalByClaimId(Long claimId) {
        Prescription rx = prescriptionRepository.findByClaimId(claimId);
        if (rx.getNextPrescriptionDate() == null) return;
        rx.setNextPrescriptionDate(null);
        rx.setRentalBillingPeriods(null);
        rx.setRentalStartPeriod(null);
        save(rx);
    }

    public void loadForeignKeys(Prescription o) {
        if (o != null) {
            o.setCreatedBy(userService.getUserById(o.getCreatedById()));
            o.setTreatingPractitioner(userService.getUserById(o.getTreatingPractitionerId()));
            o.setResident(userService.getUserById(o.getResidentId()));
            o.setClericalUser(userService.getUserById(o.getClericalUserId()));
            o.setSignedUser(userService.getUserById(o.getSignedUserId()));
            o.setViewUser(userService.getUserById(o.getViewUserId()));
        }
    }

    /**
     * Annotate Audit History with "POD Signature Changed"
     *
     * @param POD
     */
    @Auditable(entry = "POD Signature Changed")
    public void auditSignatureSave(String POD) {
    }

    /**
     * Annotate Audit History with "POD Signature Removed"
     *
     * @param POD
     */
    @Auditable(entry = "POD Signature Removed")
    public void auditSignatureRemove(String POD) {
    }

    public void billDmeRentals(Date date) {
        String useRentalAutoBill = systemSettingService.findBySectionAndField("claim", "use_rental_auto_bill").getValue();
        if ("Y".equals(useRentalAutoBill)) {
            List<Prescription> prescriptions = findAllByNextPrescriptionDate(date);
            for (Prescription rx : prescriptions) {
                if (!rx.getIsRentalPaused()) billDmeRental(rx, true);
            }
        }
    }

    public void billDmeRental(Prescription parentPrescription, Boolean submitRental) {
        List<Prescription> children = findAllByParentId(parentPrescription.getId());
        int totalPeriodsBilled = parentPrescription.getRentalStartPeriod() + children.size();
        Prescription previousPrescription = null;
        if (children != null && !children.isEmpty()) {
            if (children.size() == 1) {
                previousPrescription = children.get(0);
            } else {
                // sorting by id is the same as sorting by dateOfService
                children.sort(Comparator.comparing(Prescription::getId).reversed());
                previousPrescription = children.get(0);
            }
        }
        try {
            Prescription prescription = duplicatePrescription(parentPrescription,
                    (previousPrescription != null ? previousPrescription : parentPrescription),
                    totalPeriodsBilled + 1, Collections.emptyList());
            totalPeriodsBilled++;
            if (totalPeriodsBilled == parentPrescription.getRentalBillingPeriods()) {
                parentPrescription.setNextPrescriptionDate(null);
            } else {
                LocalDate mostRecentChildPrescriptionDate = previousPrescription != null
                        ? previousPrescription.getPrescriptionDate().toLocalDate()
                        : claimService.findTopByPrescriptionIdOrderByIdAsc(parentPrescription.getId()).getDateOfService().toLocalDate();
                LocalDate nextPrescriptionDate = calculateNextPrescriptionDate(parentPrescription.getNextPrescriptionDate().toLocalDate(), mostRecentChildPrescriptionDate);
                parentPrescription.setNextPrescriptionDate(java.sql.Date.valueOf(nextPrescriptionDate));
            }
            save(parentPrescription);

            List<Claim> claims = claimService.findByPrescriptionId(parentPrescription.getId());
            Claim claim = claimService.addClaim(prescription.getId(), prescription.getClericalUserId(), claims.get(0).getBillingBranchId(), prescription.getPrescriptionDate(), false);
            if (submitRental) {
                claimService.sendClaimFiles(Collections.singletonList(claim.getId()), claim.getBillingBranchId());
            }
        } catch (Exception e) {
            log.error("Prescription " + parentPrescription.getId() + " rental not processed.");
            log.error(StringUtil.getExceptionAsString(e));
        }
    }

    public LocalDate calculateNextPrescriptionDate(LocalDate currentNextPrescriptionDate, LocalDate mostRecentChildPrescriptionDate) {

        if (useMostRecentChildPrescriptionDate(currentNextPrescriptionDate, mostRecentChildPrescriptionDate)) {
            return currentNextPrescriptionDate
                    .plusMonths(1)
                    .withDayOfMonth(Math.min(mostRecentChildPrescriptionDate.getDayOfMonth(),
                            currentNextPrescriptionDate.plusMonths(1).lengthOfMonth()));
        }

        return currentNextPrescriptionDate.plusMonths(1);

    }

    public boolean useMostRecentChildPrescriptionDate(LocalDate currentNextPrescriptionDate, LocalDate mostRecentChildPrescriptionDate) {

        if (mostRecentChildPrescriptionDate == null) return false;

        boolean mostRecentChildPrescriptionDateMoreThanAMonthOld = mostRecentChildPrescriptionDate
                .isBefore(currentNextPrescriptionDate.minusMonths(1))
                && currentNextPrescriptionDate.getDayOfMonth() == currentNextPrescriptionDate.lengthOfMonth();

        boolean isLastDayofFebruary = currentNextPrescriptionDate.getMonth() == Month.FEBRUARY
                && (currentNextPrescriptionDate.getDayOfMonth() == 28 || currentNextPrescriptionDate.getDayOfMonth() == 29)
                && (mostRecentChildPrescriptionDate.getDayOfMonth() == 29
                || mostRecentChildPrescriptionDate.getDayOfMonth() == 30
                || mostRecentChildPrescriptionDate.getDayOfMonth() == 31);

        boolean isLastDayOfThirtyDayMonth = currentNextPrescriptionDate.getDayOfMonth() == 30
                && Arrays.asList(Month.APRIL, Month.JUNE, Month.SEPTEMBER, Month.NOVEMBER).contains(currentNextPrescriptionDate.getMonth())
                && mostRecentChildPrescriptionDate.getDayOfMonth() == 31;

        return mostRecentChildPrescriptionDateMoreThanAMonthOld || isLastDayofFebruary || isLastDayOfThirtyDayMonth;

    }

    /**
     * Check insurance eligibility the day before
     * prescription auto-renewal via waystarAPI
     *
     * @param date tomorrow
     */
    public int checkEligibility(Date date) {
        int failedPrescriptions = 0;
        String useRentalAutoBill = systemSettingService.findBySectionAndField("claim", "use_rental_auto_bill").getValue();
        String autoEligibilitySubmission = systemSettingService.findBySectionAndField("general", "auto_eligibility_submission").getValue();
        if ("Y".equals(useRentalAutoBill) && "Y".equals(autoEligibilitySubmission)) {
            List<Prescription> prescriptions = findAllByNextPrescriptionDate(date);
            for (Prescription rx : prescriptions) {
                if (!rx.getIsRentalPaused()) {
                    PatientInsurance pi = null;
                    String error = "";
                    String exception = "";
                    try {
                        pi = waystarAPI.eligibility(rx.getPatientInsuranceId());
                    } catch (Exception ex) {
                        exception = ex.getMessage();
                    }
                    if (pi == null || pi.getEligibilityDate() == null) {
                        error = String.format("Set flag is_rental_paused because prescription %d failed insurance eligibility check", rx.getId());
                        if (!"".equals(exception)) error += String.format(" with exception %s", exception);
                    }
                    if (!"".equals(error)) {
                        failedPrescriptions++;
                        // Pause Rental
                        rx.setIsRentalPaused(true);
                        rx = save(rx);
                        // Create a CriticalMessage
                        CriticalMessage c = new CriticalMessage();
                        c.setDate(java.sql.Date.valueOf(LocalDate.now()));
                        c.setUserId(1L);
                        c.setPatientId(rx.getPatientId());
                        c.setMessage(error);
                        criticalMessageService.save(c);
                    }
                }
            }
        }
        return failedPrescriptions;
    }


    private void autoArchivePrescriptions(List<Prescription> prescriptions) {
        SystemSetting setting = systemSettingService.findBySectionAndField("general", "auto_archive_prescriptions");
        if (setting.getValue().equals("1")) {
            List<Prescription> subPrescriptions = getSubPrescriptions(prescriptions);

            for (Prescription prescription : prescriptions) {
                boolean canArchiveSubPrescriptions = true;
                List<Claim> c = claimService.findByPrescriptionId(prescription.getId());
                if (canArchivePrescription(prescription, c)) {
                    List<Prescription> subPrescriptionsToArchive = new ArrayList<>();
                    for (Prescription subPrescription : subPrescriptions) {
                        if (prescription.getId().equals(subPrescription.getParentId())) {
                            List<Claim> subClaim = claimService.findByPrescriptionId(subPrescription.getId());
                            if (!canArchivePrescription(prescription, subClaim)) {
                                canArchiveSubPrescriptions = false;
                                break;
                            } else {
                                subPrescriptionsToArchive.add(prescription);
                            }
                        }
                    }
                    if (canArchiveSubPrescriptions) {
                        prescription.setArchived(true);
                        for (Prescription subToArchive : subPrescriptionsToArchive) {
                            subToArchive.setArchived(true);
                        }
                    }
                }
                prescription.setAutoArchived(true);
            }
        }
        save(prescriptions);
    }

    private static List<Prescription> getSubPrescriptions(List<Prescription> prescriptions) {
        return prescriptions.stream()
                .filter(prescription -> null != prescription.getParentId())
                .collect(Collectors.toList());
    }

    public Prescription processPrescriptionArchival(Prescription prescription, boolean archiveFlag) {
        List<Prescription> prescriptions = prescriptionRepository.findByPatientIdAndActiveIsTrue(prescription.getPatientId());
        if (null != prescription.getParentId()) {
            prescriptions.forEach(p -> {
                if (Objects.equals(p.getParentId(), prescription.getParentId())
                        || Objects.equals(prescription.getParentId(), p.getId())) {
                    p.setArchived(archiveFlag);
                    save(p);
                }
            });
            return prescription;
        } else {
            prescription.setArchived(archiveFlag);
            List<Prescription> subPrescriptions = prescriptions.stream()
                    .filter(p -> null != p.getParentId())
                    .collect(Collectors.toList());
            subPrescriptions.forEach(subPrescription -> {
                if (Objects.equals(subPrescription.getParentId(), prescription.getId())) {
                    subPrescription.setArchived(archiveFlag);
                    save(subPrescription);
                }
            });
            return save(prescription);
        }
    }

    private boolean canArchivePrescription(Prescription prescription, List<Claim> claim) {
        if (null == prescription.getAutoArchived() || !prescription.getAutoArchived()) {
            return LocalDateTime.ofInstant(prescription.getCreatedAt().toInstant(), ZoneId.systemDefault())
                    .isBefore(LocalDateTime.now().minusYears(1))
                    && (claim.isEmpty() || null == claim.get(0).getTotalPtResponsibilityBalance()
                    || claim.get(0).getTotalPtResponsibilityBalance().intValue() == 0)
                    && (claim.isEmpty() || null == claim.get(0).getTotalClaimBalance()
                    || claim.get(0).getTotalClaimBalance().intValue() == 0);
        }
        return false;
    }

    public Map<String, Object> saveForVersion(Prescription prescription) {
        Long prescriptionId = prescription.getId();
        if (prescriptionId == null && prescription.getCreatedById() == null) {
            prescription.setCreatedById(userService != null && userService.getCurrentUser() != null ? userService.getCurrentUser().getId() : 1L);
        }

        if(null != prescriptionId) {
            Optional<Prescription> existingPrescription =
                    prescriptionRepository.findById(prescriptionId);

            existingPrescription.ifPresent(p -> {
                prescription.setCreatedAt(p.getCreatedAt());
                prescription.setCreatedById(p.getCreatedById());
            });
        }

        if (prescription.getSignatureAdded()) {
            auditSignatureSave(prescription.toString());
        } else if (prescription.getSignatureRemoved()) {
            auditSignatureRemove(prescription.toString());
        }

        Map<String, Object> respMap = new HashMap<>();
        Prescription currentPrescription = null;
        try {
            currentPrescription = super.save(prescription);
        } catch (Throwable ex) {
            String exceptionMessage = ex.getMessage();
            if (exceptionMessage.contains("Row was updated or deleted by another transaction")) {
                currentPrescription = super.findOne(prescriptionId);
                String strLockingMessage = "This prescription has been edited by another user.  Please reload/re-open to get the latest version and re-save with your changes.";
                respMap = optimisticLockingUtil.checkVersionLock(prescription, currentPrescription, strLockingMessage, Prescription.class, prescriptionId);
                respMap = getAuditDetails(prescriptionId, respMap);
                List<LockedEntity> diffs = (List<LockedEntity>) respMap.get(DIFFS);
                // Compare whether the two objects differ only in their version number
                if (diffs.isEmpty() || (diffs.size() == 1 && diffs.get(0).getPropertyName().equals("version"))) {
                    // Two threads tried saving the same object simultaneously
                    // I don't care which one of them won and will return the result
                    respMap.clear();
                } else {
                    // Another user updated this object, OPTIMISTIC_MESSAGE logged
                    currentPrescription = null;
                }
            } else {
                // Database operation failed for an unrelated reason
                currentPrescription = null;
                respMap.put(OPTIMISTIC_MESSAGE, exceptionMessage);
            }
        }

        if (currentPrescription != null) {
            if (currentPrescription.getVersion().equals(0)) {
                try {
                    prescriptionSectionService.createPrescriptionSections(prescriptionId);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            respMap.put(SAVED, currentPrescription);
        }

        return respMap;
    }

}
