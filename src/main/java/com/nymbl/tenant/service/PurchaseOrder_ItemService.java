package com.nymbl.tenant.service;

import com.google.common.base.Strings;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.master.model.ThirdPartyShippingMethod;
import com.nymbl.master.model.User;
import com.nymbl.master.service.ThirdPartyShippingMethodService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.PurchaseOrder_ItemRepository;
import io.sentry.Sentry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class PurchaseOrder_ItemService extends AbstractTableService<PurchaseOrder_Item, Long> {

//    @PersistenceContext(unitName = "tenant")
//    private EntityManager tenantEntityManager;

    private final PurchaseOrder_ItemRepository purchaseOrderItemRepository;
    private final UserService userService;
    private final ThirdPartyShippingMethodService thirdPartyShippingMethodService;

    @Autowired
    public PurchaseOrder_ItemService(PurchaseOrder_ItemRepository purchaseOrderItemRepository,
                                     UserService userService,
                                     ThirdPartyShippingMethodService thirdPartyShippingMethodService) {
        super(purchaseOrderItemRepository);
        this.purchaseOrderItemRepository = purchaseOrderItemRepository;
        this.userService = userService;
        this.thirdPartyShippingMethodService = thirdPartyShippingMethodService;
    }

    public void deletePurchaseOrderItems(List<Long> poiIdsToDelete) {
        if (poiIdsToDelete != null && !poiIdsToDelete.isEmpty()) {
            for (Long poiId : poiIdsToDelete) {
                try {
                    delete(poiId);
                } catch (Exception ex) {
                    Sentry.captureException(ex);
                }
            }
        }
    }

    public List<PurchaseOrder_Item> findByPurchaseOrderId(Long purchaseOrderId) {
        List<PurchaseOrder_Item> results = purchaseOrderItemRepository.findByPurchaseOrderId(purchaseOrderId);
        for (PurchaseOrder_Item o : results) {
            loadForeignKeys(o);
        }
        return results;
    }

    public PurchaseOrder_Item findOneByPurchaseOrderId(Long purchaseOrderId) {
        List<PurchaseOrder_Item> results = purchaseOrderItemRepository.findOneByPurchaseOrderId(purchaseOrderId);
        if (results.size() > 0 && results.get(0) != null) {
            loadForeignKeys(results.get(0));
        }
        return results.size() > 0 ? results.get(0) : null;
    }

    public List<PurchaseOrder_Item> findByPrescriptionId(Long prescriptionId) {
        List<PurchaseOrder_Item> results = purchaseOrderItemRepository.findByPrescriptionId(prescriptionId);
        for (PurchaseOrder_Item o : results) {
            loadForeignKeys(o);
        }
        return results;
    }

    public List<PurchaseOrder_Item> findByPrescriptionLCodeId(Long prescriptionLCodeId) {
        List<PurchaseOrder_Item> results = purchaseOrderItemRepository.findByPrescriptionLCodeId(prescriptionLCodeId);
        for (PurchaseOrder_Item o : results) {
            loadForeignKeys(o);
        }
        return results;
    }

    public List<PurchaseOrder_Item> findByItemId(Long itemId) {
        List<PurchaseOrder_Item> results = purchaseOrderItemRepository.findByItemId(itemId);
        for (PurchaseOrder_Item o : results) {
            loadForeignKeys(o);
        }
        return results;
    }

    public List<PurchaseOrder_Item> findAllByPatientId(Long patientId) {
        return purchaseOrderItemRepository.findAllByPatientId(patientId);
    }

    /**
     * PurchaseOrderService.splitPurchaseOrderItem() happens before the POIs are saved to the DB.
     * Therefore, the split POIs may have negative parentId.
     * It needs to be replaced with the real id according to poiOriginalMap after the POIs are saved.
     * @param poiOriginalMap
     * @return
     */
    public Map<Long, PurchaseOrder_Item> updateParentIds(Map<Long, PurchaseOrder_Item> poiOriginalMap) {
        if (poiOriginalMap == null || poiOriginalMap.isEmpty()) {
            return null;
        }
        Map<Long, PurchaseOrder_Item> poiUpdatedMap = new HashMap<>(poiOriginalMap.size());
        for (Map.Entry<Long, PurchaseOrder_Item> kvp : poiOriginalMap.entrySet()) {
            // originalId may be negative and thus different from the updated id of poi that have already been saved
            Long originalId = kvp.getKey();
            PurchaseOrder_Item poi = kvp.getValue();
            // parentId may be still negative, because it doesn't get automatically updated
            Long parentId = poi.getParentId();
            if (parentId != null && parentId.compareTo(0L) < 0 && poiOriginalMap.containsKey(parentId)) {
                // find the parent poi and use its id
                poi.setParentId(poiOriginalMap.get(parentId).getId());
                purchaseOrderItemRepository.saveAndFlush(poi);
            }
            poiUpdatedMap.put(originalId, poi);
        }
        return poiUpdatedMap;
    }

    @Override
    public void loadForeignKeys(PurchaseOrder_Item o) {
        if (o != null && o.getPractitionerId() != null) {
            o.setPractitioner(userService.getUserById(o.getPractitionerId()));
        }
        if (o != null && o.getThirdPartyShippingMethodId() != null) {
            o.setThirdPartyShippingMethod(thirdPartyShippingMethodService.findOne(o.getThirdPartyShippingMethodId()));
        }
    }
}
