package com.nymbl.tenant.service;

import com.fasterxml.jackson.databind.*;
import com.nylas.*;
import com.nylas.models.Calendar;
import com.nylas.models.Event;
import com.nylas.models.EventStatus;
import com.nylas.models.EventVisibility;
import com.nylas.models.When;
import com.nylas.models.WhenType;
import com.nylas.models.*;
import com.nymbl.master.model.*;
import com.nymbl.master.repository.*;
import com.nymbl.master.service.*;
import com.nymbl.tenant.*;
import com.nymbl.tenant.mapper.*;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.model.nylas.*;
import com.nymbl.tenant.repository.*;
import jakarta.servlet.http.*;
import lombok.extern.slf4j.*;
import org.apache.coyote.*;
import org.jetbrains.annotations.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.thymeleaf.util.*;

import java.sql.Date;
import java.time.*;
import java.time.temporal.*;
import java.util.*;


@Slf4j
@Service
public class NylasService {

    @Value("${nylas.client-id}")
    String nylasClientId;

    @Value("${nylas.api-key}")
    String nylasApiKey;

    @Value("${nylas.client-secret}")
    String nylasClientSecret;

    private final UserService userService;

    private final UserRepository userRepository;
    private final AppointmentTypeService appointmentTypeService;
    private final CompanyService companyService;
    private final SystemSettingService systemSettingService;
    private final AppointmentRepository appointmentRepository;

    private final NylasEventMapper nylasEventMapper;

    @Autowired
    public NylasService(UserService userService,
                        UserRepository userRepository, AppointmentTypeService appointmentTypeService,
                        CompanyService companyService,
                        SystemSettingService systemSettingService,
                        AppointmentRepository appointmentRepository, NylasEventMapper nylasEventMapper) {

        this.userService = userService;
        this.userRepository = userRepository;
        this.appointmentTypeService = appointmentTypeService;
        this.companyService = companyService;
        this.systemSettingService = systemSettingService;
        this.appointmentRepository = appointmentRepository;
        this.nylasEventMapper = nylasEventMapper;
    }

    public NylasClient getNylasClient() {
        // Create client object
        return new NylasClient.Builder(nylasClientSecret)
                .apiUri("https://api.us.nylas.com")
                .build();
    }

    public String getHostedAuthUrlForNylasSignIn(String subdomain) {
        NylasClient nylasClient = new NylasClient.Builder(nylasApiKey)
                .apiUri("https://api.us.nylas.com")
                .build();
        List<String> scopes = new ArrayList<>();
        scopes.add("https://graph.microsoft.com/Calendars.ReadWrite");
        scopes.add("https://www.googleapis.com/auth/calendar");
        scopes.add("openid");

        String host = subdomain.contains("localhost") ? "http://" : "https://";

        String redirectUrl = host + subdomain + "/nylas_redirect";

        UrlForAuthenticationConfig config = new UrlForAuthenticationConfig.Builder(nylasClientId, redirectUrl)
                .prompt(Prompt.DETECT_SELECT_PROVIDER)
                .scope(scopes)
                .accessType(AccessType.OFFLINE)
                .build();

        return nylasClient.auth().urlForOAuth2(config);
    }

    public void getAccessTokenWithCode(String code, String host) throws Exception {
        String protocol = host.contains("localhost") ? "http://" : "https://";

        String redirectUrl = protocol + host + "/nylas_redirect";
        SystemSetting systemSetting = systemSettingService.findBySectionAndField("general", "nylas_enabled");
        if (!"Y".equals(systemSetting.getValue())) {
            return;
        }
        NylasClient nylasClient = getNylasClient();

        CodeExchangeRequest exchangeRequest = new CodeExchangeRequest.Builder(
                redirectUrl,
                code,
                nylasClientId
        )
                .clientSecret(nylasClientSecret)
                .build();

        CodeExchangeResponse exchangeResponse = nylasClient.auth().exchangeCodeForToken(exchangeRequest);

        String userEmail = exchangeResponse.getEmail();

        if(null != userEmail) {
            // Only Whitelisted domains are allowed
            String whitelistedDomains = systemSettingService.findBySectionAndField("general", "nylas_whitelisted_domains").getValue();
            boolean whitelisted = false;
            if (!Objects.equals(whitelistedDomains, "")) {
                String userDomain = userEmail.substring(userEmail.indexOf("@"), userEmail.length());
                List<String> domains = Arrays.asList(whitelistedDomains.split("\\s*,\\s*"));
                for (String d: domains) {
                    if (userDomain.equalsIgnoreCase(d)) {
                        whitelisted = true;
                    }
                }
                if (!whitelisted) {
                    throw new Exception();
                }
            }

            User currentUser = userService.getCurrentUser();
            currentUser.setNylasAccessToken(exchangeResponse.getAccessToken());
            currentUser.setNylasGrantId(exchangeResponse.getGrantId());
            userService.save(currentUser);
        } else {
            throw new Exception("Invalid user");
        }
    }


    public Calendar createNymblCalendar(User user) throws Exception {
        NylasClient nylasClient = getNylasClient();

        Calendar calendar;
        calendar = nylasClient.calendars().list(user.getNylasGrantId()).getData()
                .stream()
                .filter(cal -> "Nymbl Systems".equals(cal.getName()))
                .findFirst()
                .orElse(null);
        if(null == calendar) {
            //If caledar doesn't exist, create nymbl calendar for user
            CreateCalendarRequest calendarRequest = new CreateCalendarRequest.Builder(
                    "Nymbl Systems"
            ).build();
            calendar = nylasClient.calendars().create(user.getNylasGrantId(), calendarRequest).getData();
        }
        if(null != calendar) {
            user.setNylasNymblSystemsCalendarId(calendar.getId());
            userService.save(user);
            return calendar;
        } else {
            throw new BadRequestException("Unable to get or create calendar");
        }
    }

    public List<com.nymbl.tenant.model.nylas.Event> getEvents(Long[] userIds, Date startDate, Date endDate) throws Exception {
        SystemSetting systemSetting = systemSettingService.findBySectionAndField("general", "nylas_enabled");
        if (!"Y".equals(systemSetting.getValue())) {
            return null;
        }

        if(null == userIds) {
            return null;
        }

        NylasClient nylasClient = getNylasClient();

        List<com.nymbl.tenant.model.nylas.Event> events = new ArrayList<>();
        LocalDateTime startLocalDateTime = startDate.toLocalDate().atStartOfDay().minusSeconds(1);
        LocalDateTime endLocalDateTime = endDate.toLocalDate().plusDays(1).atStartOfDay().plusSeconds(1);

        Instant startInstant = startLocalDateTime.toInstant(ZoneOffset.UTC);
        Instant endInstant = endLocalDateTime.toInstant(ZoneOffset.UTC);

        for (Long id : userIds) {
            User user = userService.getUserById(id);
            try {
            if (user.getNylasActive() != null) {
                if (user.getNylasGrantId() != null) {
                    List<Calendar> calendars = nylasClient.calendars().list(user.getNylasGrantId()).getData();
                    for (Calendar calendar : calendars) {
                        try {
                            if (!Objects.equals(calendar.getId(), user.getNylasNymblSystemsCalendarId())) { // Don't return 'Nymbl Systems' Calendar
                                List<Event> fetchedEvents = nylasClient.events().list(user.getNylasGrantId(),
                                        new ListEventQueryParams.Builder(calendar.getId())
                                                .expandRecurring(true)
                                                .start(Long.toString(startInstant.getEpochSecond()))
                                                .end(Long.toString(endInstant.getEpochSecond()))
                                                .build()).getData();
                                fetchedEvents.forEach((Event event) -> {
                                    com.nymbl.tenant.model.nylas.Event nymblNylasEvent = nylasEventMapper.nylasEventToNymblNylasEvent(event);
                                    if(WhenType.TIMESPAN.equals(event.getWhen().getObject())) {
                                        nymblNylasEvent.setWhen(getTimespan(event));
                                    } else if (WhenType.DATE.equals(event.getWhen().getObject())) {
                                        nymblNylasEvent.setWhen(getNymblNylasDate(event));
                                    } else if (WhenType.DATESPAN.equals(event.getWhen().getObject())) {
                                        nymblNylasEvent.setWhen(getNymblNylasDatespan(event));
                                    }
                                    Map<String, String> map = new HashMap<>();
                                    map.put("nymblUserId", user.getId().toString());
                                    map.put("userName", user.getFirstName() + " " + user.getLastName());

                                    nymblNylasEvent.getMetadata().putAll(map);

                                    if(EventVisibility.PRIVATE.equals(event.getVisibility())){
                                        nymblNylasEvent.setTitle("Private");
                                        nymblNylasEvent.setDescription("Private");
                                    }
                                    if(!EventStatus.CANCELLED.equals(event.getStatus())) {
                                        events.add(nymblNylasEvent);
                                    }
                                });
                            }
                        } catch (Exception e1) {
                            log.error(e1.getMessage());
                        }
                    }
                }
            }
            } catch (Exception e) {
                System.out.println("Nylas user " + user.getEmail() + " is not signed into Nylas.");
                // no longer log these (just errors when account is invalid)
                // log.error(e.getMessage());
            }
        }
        return events;
    }

    @NotNull
    private static Timespan getTimespan(Event event) {
        Timespan timespan = new Timespan();
        timespan.setObject(com.nymbl.tenant.model.nylas.WhenType.TIMESPAN);
        timespan.setStartTime(((When.Timespan) event.getWhen()).getStartTime());
        timespan.setStartTimezone(((When.Timespan) event.getWhen()).getStartTimezone());
        timespan.setEndTime(((When.Timespan) event.getWhen()).getEndTime());
        timespan.setEndTimezone(((When.Timespan) event.getWhen()).getEndTimezone());
        return timespan;
    }

    private static com.nymbl.tenant.model.nylas.Date getNymblNylasDate(Event event) {
        com.nymbl.tenant.model.nylas.Date date = new com.nymbl.tenant.model.nylas.Date();
        date.setObject(com.nymbl.tenant.model.nylas.WhenType.DATE);
        date.setDate(((When.Date) event.getWhen()).getDate());
        return date;
    }

    private static com.nymbl.tenant.model.nylas.Datespan getNymblNylasDatespan(Event event) {
        com.nymbl.tenant.model.nylas.Datespan datespan = new com.nymbl.tenant.model.nylas.Datespan();
        datespan.setObject(com.nymbl.tenant.model.nylas.WhenType.DATESPAN);
        datespan.setStartDate(((When.Datespan) event.getWhen()).getStartDate());
        datespan.setEndDate(((When.Datespan) event.getWhen()).getEndDate());
        return datespan;
    }


    public void updateNylasEvent(Appointment a, String nylasNymblSystemsCalendarId) throws Exception {

        SystemSetting systemSetting = systemSettingService.findBySectionAndField("general", "nylas_enabled");
        if (!"Y".equals(systemSetting.getValue())) {
            return;
        }

        NylasClient nylasClient = getNylasClient();

        User user = userService.getUserById(a.getUserId());

        if(null == user.getNylasGrantId()) {
            return;
        }

        if (StringUtils.isEmpty(nylasNymblSystemsCalendarId)) {
            nylasNymblSystemsCalendarId =  !StringUtils.isEmpty(user.getNylasNymblSystemsCalendarId()) ? user.getNylasNymblSystemsCalendarId() : createNymblCalendar(user).getId();
        }
        Instant startTime = a.getStartDateTime().toInstant();
        Instant endTime = a.getEndDateTime().toInstant();

        if("cancelled".equals(a.getStatus())) {
            try {
                deleteNylasEvent(a, nylasNymblSystemsCalendarId);
                a.setNylasEventId(null);
                appointmentRepository.save(a);
            } catch (Exception e) {
                throw new RuntimeException("Unable to delete nylas appointment: " + e.getMessage());
            }
        } else {
            // Create a new event object
            if (a.getNylasEventId() != null) {
                //Build update event request
                UpdateEventRequest.When.Timespan timespan =
                        new UpdateEventRequest
                                .When
                                .Timespan(Math.toIntExact(startTime.getEpochSecond()), Math.toIntExact(endTime.getEpochSecond()), "UTC", "UTC");

                UpdateEventQueryParams updateEventQueryParams = new UpdateEventQueryParams(nylasNymblSystemsCalendarId, Boolean.TRUE);

                UpdateEventRequest updateEventRequest = new UpdateEventRequest.Builder()
                        .calendarId(nylasNymblSystemsCalendarId)
                        .setWhen(timespan)
                        .title(getEventTitle(a))
                        .busy(true)
                        .description(getEventDescription(a))
                        .build();
                //update event
                nylasClient.events().update(
                        user.getNylasGrantId(),
                        a.getNylasEventId(),
                        updateEventRequest,
                        updateEventQueryParams
                );
            } else {
                // Create the timespan for the event
                CreateEventRequest.When.Timespan timespan = new CreateEventRequest.
                        When.Timespan.
                        Builder(Math.toIntExact(startTime.getEpochSecond()), Math.toIntExact(endTime.getEpochSecond())).
                        build();

                CreateEventQueryParams createEventQueryParams = new CreateEventQueryParams.Builder(nylasNymblSystemsCalendarId).build();
                CreateEventRequest createEventRequest = new CreateEventRequest.Builder(timespan)
                        .title(getEventTitle(a))
                        .busy(true)
                        .description(getEventDescription(a))
                        .build();
                Event event = nylasClient.events().create(
                        user.getNylasGrantId(),
                        createEventRequest,
                        createEventQueryParams
                ).getData();
                a.setNylasEventId(event.getId());
                appointmentRepository.save(a);
            }
        }
    }

    private String getEventTitle(Appointment a) {
        if(null != a.getAppointmentType()) {
            return a.getAppointmentType().getName();
        } else {
            return appointmentTypeService.findOne(a.getAppointmentTypeId()).getName();
        }
    }

    private String getEventDescription(Appointment a) {
        if(a.getPatient() == null){
            return "No Patient: " + getEventTitle(a);
        } else {
            String secondLine = "";
            if(a.getPatient().getStreetAddress_line2() != null && !a.getPatient().getStreetAddress_line2().isEmpty()){
                secondLine = " " + a.getPatient().getStreetAddress_line2();
            }
            return "Patient: " + a.getPatient().getFirstName() + " " + a.getPatient().getLastName() + " " +
                    a.getPatient().getStreetAddress() + secondLine + " " + a.getPatient().getCity() + " " + a.getPatient().getState() + " " + a.getPatient().getZipcode();
        }
    }

    public void deleteNylasEvent(Appointment a, String nylasNymblSystemsCalendarId) throws Exception {

        SystemSetting systemSetting = systemSettingService.findBySectionAndField("general", "nylas_enabled");
        if (!"Y".equals(systemSetting.getValue()) || a.getNylasEventId() == null) {
            return;
        }

        User user = userService.getUserById(a.getUserId());

        NylasClient nylasClient = getNylasClient();

        if(null == nylasClient || null == user.getNylasGrantId()) {
            return;
        }

        if (nylasNymblSystemsCalendarId == null) {
            nylasNymblSystemsCalendarId = user.getNylasNymblSystemsCalendarId() != null ? user.getNylasNymblSystemsCalendarId() : createNymblCalendar(user).getId();
        }

        DestroyEventQueryParams queryParams = new DestroyEventQueryParams(nylasNymblSystemsCalendarId, Boolean.TRUE);

        nylasClient.events().destroy(
                user.getNylasGrantId(),
                a.getNylasEventId(),
                queryParams);
    }

    public void updateNymblEventFromWebhook(JsonNode incomingWebHook) throws Exception {

        NylasClient nylasClient = getNylasClient();
        User user;

        if(null != incomingWebHook.get("data").get("object")) {
            //If it's a grant expired webhook, update user and return
            if(incomingWebHook.get("type").textValue().equals("grant.expired")) {
                user = userService.getByNylasGrantId(incomingWebHook.get("data").get("object").get("grant_id").textValue());
                user.setNylasGrantId(null);
                userService.save(user);
                return;
            }

            user = userRepository.findByNylasGrantId(incomingWebHook.get("data").get("object").get("grant_id").textValue());

            if(null == user || null == user.getNylasGrantId() || null == user.getNylasNymblSystemsCalendarId()) {
                return;
            }

            TenantContext.setCurrentTenant(user.getCompany().getKey());

            SystemSetting systemSetting = systemSettingService.findBySectionAndField("general", "nylas_webhook_updates_enabled");
            if (!"Y".equals(systemSetting.getValue())) {
                return;
            }


            FindEventQueryParams eventQuery = new FindEventQueryParams(user.getNylasNymblSystemsCalendarId());

            Event event;
            try {
                event = nylasClient.events().find(user.getNylasGrantId(), incomingWebHook.get("data").get("object").get("id").textValue(), eventQuery).getData();
            } catch (Exception e) {
                log.info("Nylas get event error - Tenant: " + TenantContext.getCurrentTenant());
                log.info("Nylas get event error: " + e.getMessage());
                log.info("Nylas get event error - event id: " + incomingWebHook.get("data").get("object").get("id").textValue());
                return;
            }
            setCompany(user);
            Appointment a = appointmentRepository.findByNylasEventId(event.getId());
            if(null != a) {
                if(EventStatus.CANCELLED.equals(event.getStatus())) {
                    a.setStatus("cancelled");
                    appointmentRepository.save(a);
                } else {
                    if(null != event.getWhen().getObject()) {
                        switch(event.getWhen().getObject().name()) {
                            case "DATE":
                                java.sql.Date date = java.sql.Date.valueOf(event.getWhen().getObject().getValue());
                                break;
                            case "DATESPAN":
                                When.Datespan datespan = (When.Datespan) event.getWhen();
                                break;
                            case "TIME":
                                When.Time time = (When.Time) event.getWhen();
                                break;
                            case "TIMESPAN":
                                When.Timespan timespan = (When.Timespan) event.getWhen();
                                Instant startInstant = Instant.ofEpochSecond(timespan.getStartTime());
                                Instant endInstant = Instant.ofEpochSecond(timespan.getEndTime());

                                OffsetDateTime start = startInstant.atOffset(ZoneOffset.UTC);
                                OffsetDateTime end = endInstant.atOffset(ZoneOffset.UTC);

                                a.setStartDateTime(start);
                                a.setEndDateTime(end);
                                appointmentRepository.save(a);
                                break;
                            default:
                                log.info("unknown Nylas Event update received" + event);
                                break;
                        }
                    }
                }
            }
            TenantContext.clear();
        }
    }

    public void handleNylasWebhook(HttpServletRequest request, HttpServletResponse response) throws Exception {

        ObjectMapper mapper = new ObjectMapper();

        JsonNode incoming_webhook = mapper.readValue(request.getInputStream(), JsonNode.class);
        if (request.getHeader("X-Nylas-Signature") == null) {
            log.info("Callback missing X-Nylas-Signature header.  Ignoring.");
        } else {
            updateNymblEventFromWebhook(incoming_webhook);
        }
    }

    public void initialNylasSync() throws Exception {
        SystemSetting systemSetting = systemSettingService.findBySectionAndField("general", "nylas_enabled");
        if (!"Y".equals(systemSetting.getValue())) {
            return;
        }

        User user = userService.getCurrentUser();

        if (user.getNylasGrantId() != null) {

            String nymblSystemsCalendarId = createNymblCalendar(user).getId();

            // grab all nymbl appointments starting from 1st of previous month to last day of next year.
            OffsetDateTime now = OffsetDateTime.now();
            OffsetDateTime firstDayOfLastMonth = now.minusMonths(1).withDayOfMonth(1);
            OffsetDateTime lastDayOfNextYear = now.plusYears(1).with(TemporalAdjusters.lastDayOfYear());

            List<Appointment> appointments;

            if(Boolean.FALSE.equals(user.getNylasSynced())) {
                appointments = appointmentRepository.findByStartDateTimeBetweenAndUserIdAndNylasEventId(firstDayOfLastMonth, lastDayOfNextYear, user.getId(), null);
                for (Appointment a : appointments) {
                    if (!Objects.equals(a.getStatus(), "cancelled")) {
                        updateNylasEvent(a, nymblSystemsCalendarId);
                    }
                }
                user.setNylasSynced(true);
                userService.save(user);
            } else {
                appointments = appointmentRepository.findByStartDateTimeBetweenAndUserIdAndNylasEventIdIsNotNull(firstDayOfLastMonth, lastDayOfNextYear, user.getId());
                for (Appointment a : appointments) {
                    if (!Objects.equals(a.getStatus(), "cancelled")) {
                        updateNylasEvent(a, nymblSystemsCalendarId);
                    }
                }
            }
        }
    }

    public void signOut() throws Exception {

        User user = userService.getCurrentUser();

        List<Appointment> appointmentsToBeUnSynced = appointmentRepository.findByUserIdAndNylasEventIdNotNull(user.getId());
        for (Appointment a : appointmentsToBeUnSynced) {
            try {
                deleteNylasEvent(a, user.getNylasNymblSystemsCalendarId());
            } catch (Exception e) {
                log.info("Unable to delete nylas appointment");
            }
            a.setNylasEventId(null);
            appointmentRepository.save(a);
        }

        user.setNylasAccountId(null);
        user.setNylasAccessToken(null);
        user.setNylasNymblSystemsCalendarId(null);
        user.setNylasGrantId(null);
        user.setNylasSynced(false);
        userService.save(user);
    }

    public void setCompany(User user) {
        Company company = companyService.findOne(user.getCompanyId());
        TenantContext.setCurrentTenant(company.getKey());
    }
}

