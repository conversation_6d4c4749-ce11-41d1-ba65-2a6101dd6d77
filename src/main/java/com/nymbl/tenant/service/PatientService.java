package com.nymbl.tenant.service;

import com.nymbl.config.dto.PatientPrescriptionCount;
import com.nymbl.config.dto.PatientZipcodeCount;
import com.nymbl.config.dto.reports.INewPatientInfo;
import com.nymbl.config.dto.reports.PatientLastTouchRow;
import com.nymbl.config.dto.reports.ResupplyRow;
import com.nymbl.config.dto.reports.RxLastTouchRow;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.service.VersionExceptionLockInterface;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.ai.lmn.interfaces.LMNPatientDTO;
import com.nymbl.master.repository.CompanyRepository;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.model.interfaces.PatientInfo;
import com.nymbl.tenant.repository.PatientRepository;
import com.nymbl.tenant.specification.PatientSpecs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.nymbl.config.utils.OptimisticLockingUtil.SAVED;

/**
 * Created by Bradley Moore on 05/17/2017.
 */
@Slf4j
@Service
public class PatientService extends AbstractTableService<Patient, Long> implements VersionExceptionLockInterface<Patient> {

    private final PatientRepository patientRepository;
    private final UserService userService;
    private final CompanyRepository companyRepository;
    private final ClaimService claimService;
    private final ClaimSubmissionService claimSubmissionService;
    private final AppliedPaymentService appliedPaymentService;
    private final PaymentService paymentService;
    private final BranchService branchService;
    private final SystemSettingService systemSettingService;
    private final PatientContactService patientContactService;

    @Autowired
    private OptimisticLockingUtil optimisticLockingUtil;

    @Autowired
    public PatientService(PatientRepository patientRepository,
                          UserService userService,
                          CompanyRepository companyRepository,
                          @Lazy ClaimService claimService,
                          @Lazy ClaimSubmissionService claimSubmissionService,
                          @Lazy AppliedPaymentService appliedPaymentService,
                          @Lazy PaymentService paymentService,
                          BranchService branchService,
                          SystemSettingService systemSettingService,
                          PatientContactService patientContactService) {
        super(patientRepository);
        this.patientRepository = patientRepository;
        this.userService = userService;
        this.companyRepository = companyRepository;
        this.claimService = claimService;
        this.claimSubmissionService = claimSubmissionService;
        this.appliedPaymentService = appliedPaymentService;
        this.paymentService = paymentService;
        this.branchService = branchService;
        this.systemSettingService = systemSettingService;
        this.patientContactService = patientContactService;
    }

    public List<Patient> getPatients(String keyword, Boolean active, Long branchId) {
        List<Patient> result;
        String sortByName = systemSettingService.findBySectionAndField("general", "patient_sort_by_name").getValue();
        Sort sort;
        if (Objects.equals("Y", sortByName)) {
            sort = Sort.by(new Sort.Order(Sort.Direction.ASC, "lastName").ignoreCase(), new Sort.Order(Sort.Direction.ASC, "firstName").ignoreCase());
        } else {
            sort = makeSort(Sort.Direction.DESC, "id");
        }
        Specification<Patient> spec = PatientSpecs.searchNew(keyword, active, branchId, null);
        if (spec != null) {
            Pageable pageable = makePageable(sort, 0, 100);
            result = patientRepository.findAll(spec, pageable).getContent();
        } else {
            result = patientRepository.findAll(sort);
        }
        return result;
    }

    public Page<Patient> sort(String keyword, Boolean active, Long branchId, Long primaryPractitionerId, Pageable pageable) {
        Page<Patient> results = null;
        try {
            int pageNumber = pageable.getPageNumber() > 0 ? pageable.getPageNumber() - 1 : 0;
            Pageable newPageable = PageRequest.of(pageNumber, pageable.getPageSize(), pageable.getSort());
            Specification<Patient> spec = PatientSpecs.searchNew(keyword, active, branchId, primaryPractitionerId);

            if (spec != null) {
                results = patientRepository.findAll(spec, newPageable);
            } else {
                results = patientRepository.findAll(newPageable);
            }
            for (Patient o : results) {
                loadForeignKeys(o);
            }
            return results;
        } catch (Exception e) {
            return results;
        }
    }

    public Long countByActiveAndPrimaryBranchId(Boolean active, Long branchId) {
        return patientRepository.countByActiveAndPrimaryBranchId(active, branchId);
    }

    public Patient findByCellPhone(String cellPhone) {
        List<Patient> patients = patientRepository.findByCellPhone(cellPhone);
        return patients.get(0);
    }

    @Transactional("tenantTransactionManager")
    public Set<Patient> search(String text, Boolean inactive) {
        if (inactive != null)
            inactive = !inactive;
        List<Patient> results = getPatients(text, inactive, null);
        return new LinkedHashSet<>(results);
    }

    public Map<String, List<Object[]>> countPatientsPerZipcode(Long branchId) {
        List<PatientZipcodeCount> results = patientRepository.countPatientsPerZipcode(branchId);

        List<Object[]> top_ten_zipcodes = new ArrayList<>();
        List<Object[]> data_xaxis = new ArrayList<>();
        Map<String, List<Object[]>> map = new HashMap<>();
        int i = 0;
        for (PatientZipcodeCount entry : results) {
            top_ten_zipcodes.add(new Object[]{entry.getZipcode(), String.valueOf(entry.getCount())});
            data_xaxis.add(new Object[]{i, entry.getZipcode()});
            i++;
            if (i == 10) {
                break;
            }
        }
        map.put("top_ten_zipcodes", top_ten_zipcodes);
        map.put("data_xaxis", data_xaxis);
        return map;
    }

    public Map<String, List<Object[]>> countPatientsPerPrescription(Long branchId) {
        List<Object[]> top_ten_prescriptions = new ArrayList<>();
        List<Object[]> data_xaxis = new ArrayList<>();
        Map<String, List<Object[]>> map = new HashMap<>();
        List<PatientPrescriptionCount> results = patientRepository.countPatientsPerPrescription(branchId);
        int i = 0;
        for (PatientPrescriptionCount entry : results) {
            top_ten_prescriptions.add(new Object[]{entry.getName(), String.valueOf(entry.getCount())});
            data_xaxis.add(new Object[]{i, StringUtil.getAbbreviation(entry.getName())});
            i++;
            if (i == 10) {
                break;
            }
        }
        map.put("top_ten_prescriptions", top_ten_prescriptions);
        map.put("data_xaxis", data_xaxis);
        return map;
    }

    public List<Patient> patientListByPractitioner(Long practitionerId, Long branchId) {
        return patientRepository.findByPrimaryPractitionerIdAndPrimaryBranchId(practitionerId, branchId);
    }

    public List<Patient> patientListByDOBMonth(Long branchId, Integer month) {
        List<Patient> patients = patientRepository.findAllByDobLike(month);
        if (branchId != null) {
            patients.removeIf(p -> !Objects.equals(p.getPrimaryBranchId(), branchId));
        }
        return patients;
    }

    public List<Patient> validateUniquePatient(String firstName, String lastName, Date dob) {
        return patientRepository.findByFirstNameAndLastNameAndDob(firstName, lastName, dob);
    }

    public List<Patient> findByFirstNameAndLastNameAndDob(String firstName, String lastName, Date dob) {
        return patientRepository.findByFirstNameAndLastNameAndDob(firstName, lastName, dob);
    }

    public List<Patient> findByLastNameAndDob(String lastName, Date dob) {
        return patientRepository.findByLastNameAndDob(lastName, dob);
    }

    public boolean canInactivate(Patient patient) {
        if (patient.getId() != null) {
            Patient oPatient = findOne(patient.getId());
            if (oPatient.getActive() == null) {
                oPatient.setActive(false);
            }
            if (oPatient.getActive() && !patient.getActive()) {
                List<Payment> payments = paymentService.findByPatientId(patient.getId());
                if (payments.size() > 0) {
                    return false;
                }
                List<Claim> claims = claimService.findByPrescriptionPatientId(patient.getId(), 0);
                for (Claim c : claims) {
                    List<AppliedPayment> appliedPayments = appliedPaymentService.getByClaimId(c.getId());
                    List<ClaimSubmission> claimSubmissions = claimSubmissionService.findByClaimId(c.getId());
                    if (appliedPayments.size() > 0 || claimSubmissions.size() > 0) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    public List<Patient> findAllByPrimaryBranchIdAndSmsCompliantCell(Long primaryBranchId, Long patientId, String phoneNumber) {
        List<Patient> results = null;
//        System.out.println("findAllByPrimaryBranchIdAndSmsCompliantCellQuery = \n"+patientRepository.findAllByPrimaryBranchIdAndSmsCompliantCellQuery.replaceAll(":branchId",primaryBranchId == null ? "null" : primaryBranchId.toString())
//            .replaceAll(":phoneNumber", phoneNumber == null ? "null" : phoneNumber)
//            .replaceAll(":patientId", patientId == null ? "null" : patientId.toString())
//        );
        results = patientRepository.findAllByPrimaryBranchIdAndSmsCompliantCell(primaryBranchId, patientId, phoneNumber);
        for (Patient o : results) {
            loadForeignKeys(o);
        }
        return results;
    }

    public Optional<Patient> findPatientById(Long patientId) {
    	return patientRepository.findById(patientId);
    }

    @Override
    public void loadForeignKeys(Patient o) {
        if (o != null) {
            o.setPrimaryPractitioner(userService.getUserById(o.getPrimaryPractitionerId()));
            o.setPrimaryBranch(branchService.findOne(o.getPrimaryBranchId()));
            o.setPatientGuarantor(patientContactService.findOne(o.getPatientGuarantorId()));
        }
    }

    public List<INewPatientInfo> getPatientsByCreatedDate(Long branchId, Date startDate, Date endDate) {
        List<INewPatientInfo> results = patientRepository.getNewPatientInfo(branchId, startDate, endDate);
        return results;

    }

    @Override
    public Map<String, Object> saveForVersion(Patient patient) {
        if (patient.getId() == null && patient.getCreatedById() == null) {
            patient.setCreatedById(userService != null && userService.getCurrentUser() != null ? userService.getCurrentUser().getId() : 1L);
        }

        Map<String, Object> respMap = new HashMap<>();
        String strLockingMessage = "This patient has been edited by another user.  Please reload/re-open to get the latest version and re-save with your changes.";
        PatientInfo currentPatient = findOne(patient.getId());

        if (null != currentPatient && currentPatient.getVersion().intValue() != patient.getVersion().intValue()) {
            respMap = getAuditDetails(patient.getId(), optimisticLockingUtil.checkVersionLock(patient, currentPatient, strLockingMessage, Patient.class, currentPatient.getId()));
        } else {
            respMap.put(SAVED, super.save(patient));
        }

        return respMap;
    }
    public List<PatientLastTouchRow> getPatientLastTouchReport(Long branchId) {

        List<PatientLastTouchRow> patientRows = patientRepository.getPatientLastTouchReport(branchId);

        List<RxLastTouchRow> rxRows = patientRepository.getPatientRxLastTouchReport(branchId);

        // Create a map to store RxRow objects by their patient
        Map<Long, List<RxLastTouchRow>> rxRowMap = rxRows.stream()
                .collect(Collectors.groupingBy(RxLastTouchRow::getPatientId));

        // Loop through PatientRow list and add matching RxRows to prescriptions
        patientRows = filterDuplicatePatients(patientRows);
        patientRows.forEach(patientRow -> {
            List<RxLastTouchRow> matchingRxRows = rxRowMap.get(patientRow.getPatientId());
            if (matchingRxRows != null) {


                patientRow.getPrescriptions().addAll(matchingRxRows);
            }
        });

        return patientRows;
    }

    public static List<PatientLastTouchRow> filterDuplicatePatients(List<PatientLastTouchRow> patients) {
        // Filter out duplicate patients based on ID
        List<PatientLastTouchRow> uniquePatients = patients.stream()
                .collect(Collectors.toMap(PatientLastTouchRow::getPatientId, Function.identity(), (patient1, patient2) -> patient1))
                .values()
                .stream()
                .collect(Collectors.toList());

        return uniquePatients;
    }

    public List<ResupplyRow> getResupplyQuery(Long branchId, Long daysSinceDos) {
//        log.info(SQLConstantsReports.resupplyReportSQL.replaceAll(":branchId", branchId+"").replaceAll(":daysSinceDos", daysSinceDos+""));
        return patientRepository.getResupplyQuery(branchId, daysSinceDos);
    }

    public LMNPatientDTO findProjectedById(Long patientId) {
        Optional<LMNPatientDTO> lmnPatientDTOOptional = patientRepository.findLMNPatientDTOById(patientId);
        return lmnPatientDTOOptional.orElse(null);

    }
}
