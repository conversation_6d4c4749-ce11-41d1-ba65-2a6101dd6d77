package com.nymbl.tenant.service;

import com.nymbl.config.clearingHouse.WaystarAPI;
import com.nymbl.config.dto.DailyCloseReport.DCPayment;
import com.nymbl.config.dto.DailyCloseReport.DailyCloseBranchDTO;
import com.nymbl.config.dto.DailyCloseReport.DailyCloseReportDTO;
import com.nymbl.config.dto.*;
import com.nymbl.config.dto.profile.ClaimPayment;
import com.nymbl.config.dto.reports.CashRow;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.service.VersionExceptionLockInterface;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.interfaces.IClaimTotals;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.model.interfaces.PaymentInfo;
import com.nymbl.tenant.repository.*;
import com.nymbl.tenant.specification.PaymentSpecs;
import io.sentry.Sentry;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.google.common.collect.Iterables.contains;
import static com.nymbl.config.Constants.DF_YYYY_MM_DD_HH_MM;
import static com.nymbl.config.utils.OptimisticLockingUtil.SAVED;
import static java.util.Comparator.comparingLong;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by Bradley Moore on 10/19/2017.
 */
@Service
@Slf4j
public class PaymentService extends AbstractTableService<Payment, Long> implements VersionExceptionLockInterface<Payment> {

    private final PaymentRepository paymentRepository;
    private final AppliedPayment_L_CodeRepository appliedPaymentLCodeRepository;
    private final PhoneNumberRepository phoneNumberRepository;
    private final UserService userService;
    private final AppliedPaymentRepository appliedPaymentRepository;
    private final ClaimSubmissionService claimSubmissionService;
    private final ClaimSubmissionRepository claimSubmissionRepository;
    private final BranchRepository branchRepository;
    private final AppliedPaymentService appliedPaymentService;
    private final ClaimService claimService;
    private final AdjustmentService adjustmentService;
    private final L_CodeCategoryService l_codeCategoryService;
    private final L_CodeRepository l_codeRepository;
    private final InsuranceVerificationLCodeService insuranceVerificationLCodeService;
    private final PatientService patientService;
    private final AutoPostPatientService autoPostPatientService;
    private final AutoPostService autoPostService;
    private final WaystarAPI waystarAPI;

    private final FeatureFlagService featureFlagService;

    private final GeneralLedgerLiveService generalLedgerLiveService;

    private final GeneralLedgerService2 generalLedgerService2;

    private final WellFormedPaymentService wellFormedPaymentService;

    @Autowired
    private OptimisticLockingUtil optimisticLockingUtil;

    @PersistenceContext(unitName = "tenant")
    private EntityManager entityManager;
    @Autowired
    private GeneralLedgerStaticService generalLedgerStaticService;
    @Autowired
    private GeneralLedgerService generalLedgerService;

    @Autowired
    public PaymentService(PaymentRepository paymentRepository,
                          AppliedPayment_L_CodeRepository appliedPaymentLCodeRepository,
                          PhoneNumberRepository phoneNumberRepository,
                          UserService userService,
                          AppliedPaymentRepository appliedPaymentRepository,
                          ClaimSubmissionService claimSubmissionService,
                          ClaimSubmissionRepository claimSubmissionRepository,
                          BranchRepository branchRepository,
                          @Lazy AppliedPaymentService appliedPaymentService,
                          @Lazy ClaimService claimService,
                          AdjustmentService adjustmentService,
                          L_CodeCategoryService l_codeCategoryService,
                          L_CodeRepository l_codeRepository,
                          InsuranceVerificationLCodeService insuranceVerificationLCodeService,
                          PatientService patientService,
                          @Lazy AutoPostPatientService autoPostPatientService,
                          AutoPostService autoPostService,
                          WaystarAPI waystarAPI,
                          FeatureFlagService featureFlagService,
                          GeneralLedgerLiveService generalLedgerLiveService,
                          GeneralLedgerService2 generalLedgerService2,
                          @Lazy WellFormedPaymentService wellFormedPaymentService) {
        super(paymentRepository);
        this.paymentRepository = paymentRepository;
        this.appliedPaymentLCodeRepository = appliedPaymentLCodeRepository;
        this.phoneNumberRepository = phoneNumberRepository;
        this.userService = userService;
        this.appliedPaymentRepository = appliedPaymentRepository;
        this.claimSubmissionService = claimSubmissionService;
        this.claimSubmissionRepository = claimSubmissionRepository;
        this.branchRepository = branchRepository;
        this.appliedPaymentService = appliedPaymentService;
        this.claimService = claimService;
        this.adjustmentService = adjustmentService;
        this.l_codeCategoryService = l_codeCategoryService;
        this.l_codeRepository = l_codeRepository;
        this.insuranceVerificationLCodeService = insuranceVerificationLCodeService;
        this.patientService = patientService;
        this.autoPostPatientService = autoPostPatientService;
        this.autoPostService = autoPostService;
        this.waystarAPI = waystarAPI;
        this.featureFlagService = featureFlagService;
        this.generalLedgerLiveService = generalLedgerLiveService;
        this.generalLedgerService2 = generalLedgerService2;
        this.wellFormedPaymentService = wellFormedPaymentService;
    }

    @Override
    public void loadForeignKeys(Payment o) {
        if (o != null) {
            o.setCreatedBy(userService.getUserById(o.getCreatedById()));
//            if (o.getAutoPostPatientId() != null) {
//                o.setEob(waystarAPI.eobRequestParams(o.getAutoPostPatientId()));
//            }
        }
    }

    public void loadForeignKeysForDTO(PaymentDTO o) {
        if (o != null) {
            o.setCreatedBy(userService.getUserById(o.getCreatedById()));
        }
    }

    public Page<PaymentDTO> search(Long id, Long patientId, String insuranceCompany, java.util.Date checkStartDate,
                                   java.util.Date checkEndDate, String checkNumber, String totalAmount, String payerType,
                                   String unappliedType, Long branchId, Long createdBy, java.util.Date createdAt, java.util.Date appliedDate,
                                   Boolean patientDeposits, Boolean bulkPayments, Boolean showSLAdjustments,
                                   Long claimId, Long prescriptionId, boolean isWellFormed, Pageable pageable) {
        return search(id, patientId, insuranceCompany, checkStartDate, checkEndDate, checkNumber, totalAmount, payerType,
                unappliedType, branchId, createdBy, createdAt, appliedDate, patientDeposits, bulkPayments, showSLAdjustments, claimId,
                prescriptionId, isWellFormed, pageable, entityManager);
    }

    public Page<PaymentDTO> search(Long id, Long patientId, String insuranceCompany, java.util.Date checkStartDate,
                                   java.util.Date checkEndDate, String checkNumber, String totalAmount, String payerType,
                                   String unappliedType, Long branchId, Long createdBy, java.util.Date createdAt, java.util.Date appliedDate,
                                   Boolean patientDeposits, Boolean bulkPayments, Boolean showSLAdjustments, Long claimId,
                                   Long prescriptionId, boolean isWellFormed, Pageable pageable, EntityManager entityManager) {
        Page<PaymentDTO> result;
        String sort = pageable.getSort().toString();
        String field = sort.substring(0, sort.indexOf(":"));
        String direction = sort.substring(sort.indexOf(":") + 1).trim();
        String offset = pageable.getOffset() + "";
        List<PaymentDTO> results = new ArrayList<>();
        PaymentSpecs spec = new PaymentSpecs(id, patientId, insuranceCompany, checkStartDate, checkEndDate, checkNumber, totalAmount, payerType,
                unappliedType, branchId, createdBy, createdAt, appliedDate, patientDeposits, bulkPayments, showSLAdjustments, claimId, prescriptionId);
        Long paymentsLength = getQueryCountTotal(entityManager, spec);
        String dataQuery = spec.getDataQuery().concat(" ORDER BY ".concat(field).concat(" ").concat(direction)).concat(" LIMIT ").concat(offset).concat(", ").concat(Integer.toString(pageable.getPageSize()));
//        System.out.println("\ndataQuery = \n" + dataQuery);
        @SuppressWarnings("unchecked")
        List<Object[]> paymentDTOList = entityManager.createNativeQuery(dataQuery).getResultList();
        for (Object[] objects : paymentDTOList) {
            PaymentDTO paymentDTO = new PaymentDTO(objects);
            loadForeignKeysForDTO(paymentDTO);
            if (paymentDTO.getPatientId() != null && paymentDTO.getPatient() == null) {
                paymentDTO.setPatient(patientService.findOne(paymentDTO.getPatientId()));
            }
            if (paymentDTO.getAutoPostPatientId() != null && paymentDTO.getAutoPostPatient() == null) {
                AutoPostPatient app = autoPostPatientService.findOne(paymentDTO.getAutoPostPatientId());
                paymentDTO.setAutoPostPatient(app);
            }
            if (paymentDTO.getAutoPostId() != null && paymentDTO.getAutoPost() == null) {
                AutoPost ap = autoPostService.findOne(paymentDTO.getAutoPostId());
                paymentDTO.setAutoPost(ap);
            }
            if (paymentDTO.getClaimId() != null && paymentDTO.getClaim() == null) {
                paymentDTO.setClaim(claimService.findOne(paymentDTO.getClaimId()));
            }
            if (paymentDTO.getAdjustmentId() != null && paymentDTO.getAdjustmentType() == null) {
                paymentDTO.setAdjustmentType(adjustmentService.findOne(paymentDTO.getAdjustmentId()));
            }
            if (isWellFormed) {
                if (paymentDTO.getUnappliedAmount().compareTo(BigDecimal.ZERO) > 0 || paymentDTO.getUnappliedAdjustment().compareTo(BigDecimal.ZERO) > 0) {
                    paymentDTO = wellFormedPaymentService.setClaimInformationForPaymentDTO(paymentDTO);
                    if (paymentDTO.getClaim() != null && claimSubmissionService.findByClaimIdFirstSubmission(paymentDTO.getClaimId()) != null) {
                        if (wellFormedPaymentService.isWellFormedTransaction(paymentDTO)) {
                            results.add(paymentDTO);
                            paymentsLength++;
                        }
                    }
                }
            } else {
                results.add(paymentDTO);
                paymentsLength++;
            }
        }
        result = new PageImpl<>(results, pageable, paymentsLength);

        return result;
    }

    public String exportPayments(Long id, Long patientId, String insuranceCompany, java.util.Date checkStartDate,
                                 java.util.Date checkEndDate, String checkNumber, String totalAmount, String payerType,
                                 String unappliedType, Long branchId, Long createdBy, java.util.Date createdAt, java.util.Date appliedDate,
                                 Boolean patientDeposits, Boolean bulkPayments, Boolean showSLAdjustments,
                                 Long claimId, Long prescriptionId) {
        PaymentSpecs spec = new PaymentSpecs(id, patientId, insuranceCompany, checkStartDate, checkEndDate, checkNumber, totalAmount, payerType,
                unappliedType, branchId, createdBy, createdAt, appliedDate, patientDeposits, bulkPayments, showSLAdjustments, claimId, prescriptionId);
        String dataQuery = spec.getDataQuery().concat(" ORDER BY id DESC");
        @SuppressWarnings("unchecked")
        List<Object[]> paymentDTOList = entityManager.createNativeQuery(dataQuery).getResultList();
        String csv = "ID,Type,Payer,Patient,Patient Branch, Prescription Branch, Claim Branch, Amount,Unapplied Amount,Adjustment,Check #,ICN,Payment Date,Applied Date,Applied By,Deposit Date,Created Date, Created By,Description\n";
        for (Object[] objects : paymentDTOList) {
            PaymentDTO paymentDTO = new PaymentDTO(objects);
            csv = csv.concat(paymentDTO.getId() != null ? paymentDTO.getId().toString() : "").concat(",");
            csv = csv.concat(paymentDTO.getPaymentType() != null ? paymentDTO.getPaymentType() : "").concat(",");
            if (paymentDTO.getPaymentType() != null) {
                switch (paymentDTO.getPayerType()) {
                    case "patient":
                        csv = csv.concat(paymentDTO.getPatientName() != null ? paymentDTO.getPatientName().replace(",", "") : "").concat(",");
                        break;
                    case "insurance_company":
                        csv = csv.concat(paymentDTO.getPayer() != null ? paymentDTO.getPayer().replace(",", "") : "").concat(",");
                        break;
                    case "adjustment":
                    case "patient_adjustment":
                        csv = csv.concat("Internal AR").concat(",");
                        break;
                }
            }
            csv = csv.concat(paymentDTO.getPatientName() != null ? paymentDTO.getPatientName().replace(",", "") : "").concat(",");

            csv = csv.concat(paymentDTO.getBranchName() != null ? paymentDTO.getBranchName() : "").concat(",");
            if (paymentDTO.getClaimId() != null && paymentDTO.getClaim() == null) {
                paymentDTO.setClaim(claimService.findOne(paymentDTO.getClaimId()));
            }
            csv = csv.concat((paymentDTO.getClaim() != null && paymentDTO.getClaim().getPrescription() != null && paymentDTO.getClaim().getPrescription().getBranch() != null) ? paymentDTO.getClaim().getPrescription().getBranch().getName() : "").concat(",");
            csv = csv.concat((paymentDTO.getClaim() != null && paymentDTO.getClaim().getBillingBranch() != null) ? paymentDTO.getClaim().getBillingBranch().getName() : "").concat(",");

            csv = csv.concat(paymentDTO.getAmount() != null ? paymentDTO.getAmount().toString() : "").concat(",");
            csv = csv.concat(paymentDTO.getUnappliedAmount() != null ? paymentDTO.getUnappliedAmount().toString() : "").concat(",");
            csv = csv.concat(paymentDTO.getAdjustment() != null ? paymentDTO.getAdjustment().toString() : "").concat(",");
            csv = csv.concat(paymentDTO.getCheckNumber() != null ? paymentDTO.getCheckNumber() : "").concat(",");
            csv = csv.concat(paymentDTO.getIcn() != null ? paymentDTO.getIcn() : "").concat(",");
            csv = csv.concat(paymentDTO.getDate() != null ? paymentDTO.getDate().toString() : "").concat(",");
            csv = csv.concat(paymentDTO.getAppliedDate() != null ? paymentDTO.getAppliedDate().toString() : "").concat(",");
            csv = csv.concat(StringUtils.isNotBlank(paymentDTO.getAppliedBy()) ? paymentDTO.getAppliedBy() : "").concat(",");
            csv = csv.concat(paymentDTO.getDepositDate() != null ? paymentDTO.getDepositDate().toString() : "").concat(",");
            csv = csv.concat(paymentDTO.getCreatedAt() != null ? paymentDTO.getCreatedAt().toLocalDateTime().toString() : "").concat(",");
            if (paymentDTO.getCreatedById() != null) {
                User createUser = userService.findOne(paymentDTO.getCreatedById());
                if (createUser != null) {
                    csv = csv.concat("\"").concat(createUser.getLastName()).concat(", ").concat(createUser.getFirstName()).concat("\",");
                } else {
                    csv = csv.concat(",");
                }
            } else {
                csv = csv.concat(",");
            }

            csv = csv.concat("\"").concat(paymentDTO.getDescription() != null ? paymentDTO.getDescription().replace("\n", " ") : "");
            csv = csv.concat("\"\n");
        }
        return csv;
    }

    public Long getQueryCountTotal(EntityManager entityManager, PaymentSpecs spec) {
        String queryCount = spec.getQueryCount();
        //System.out.println("\nqueryCount = \n"+queryCount);
        Long paymentsLength = 0L;
        try {
            paymentsLength = (Long) entityManager.createNativeQuery(queryCount).getSingleResult();
        } catch (Exception e) {
            System.out.println(queryCount);
            throw e;
        }
        return paymentsLength;
    }

    public List<Payment> getByAutoPostPatientIdAndUnappliedAmountGreaterThan(Long autoPostPatientId, BigDecimal unappliedAmount) {
        List<Payment> results = paymentRepository.getByAutoPostPatientIdAndUnappliedAmountGreaterThan(autoPostPatientId, unappliedAmount);
        populatePaymentLoadForeignKeys(results);
        return results;
    }

    public void populatePaymentLoadForeignKeys(List<Payment> results) {
        for (Payment o : results) {
            loadForeignKeys(o);
        }
    }

    public List<Payment> getByAutoPostIdAndUnappliedAmountGreaterThan(Long autoPostId, BigDecimal unappliedAmount) {
        List<Payment> results = paymentRepository.getByAutoPostPatient_AutoPostIdAndUnappliedAmountGreaterThan(autoPostId, unappliedAmount);
        populatePaymentLoadForeignKeys(results);
        return results;
    }

    public List<Payment> findByInsuranceCompanyId(Long insuranceCompanyId) {
        List<Payment> results = paymentRepository.findByInsuranceCompanyId(insuranceCompanyId);
        populatePaymentLoadForeignKeys(results);
        return results;
    }

    public List<Payment> findByPatientId(Long patientId) {
        List<Payment> results = paymentRepository.findByPatientId(patientId);
        populatePaymentLoadForeignKeys(results);
        return results;
    }

    public List<Payment> loadAbleToApply(Long patientId, Long paymentId) {
        List<Payment> results = paymentRepository.findByPatientIdAndUnappliedAmountGreaterThan(patientId, BigDecimal.ZERO);
        results.removeIf(p -> p.getId().equals(paymentId));
        populatePaymentLoadForeignKeys(results);
        return results;
    }

    public Payment findOneUnappliedByPatientId(Long patientId) {
        List<Payment> results = paymentRepository.findByPatientIdAndUnappliedAmountGreaterThan(patientId, BigDecimal.ZERO);
        List<Payment> singlePayment = new ArrayList<>();
        if (results.size() > 0) {
            singlePayment.add(results.get(0));
            populatePaymentLoadForeignKeys(singlePayment);
            return singlePayment.get(0);
        }
        return null;
    }

    public List<Payment> findByClaimId(Long claimId) {
        List<Payment> results = paymentRepository.findByClaimId(claimId);
        populatePaymentLoadForeignKeys(results);
        return results;
    }

    public Map<String, List<Object>> percentCollectedByMonth(Long branchId, Long numberMonths) {
        Map<String, List<Object>> map = new HashMap<>();
        Map<String, List<Object>> billingCollectionsByMonth = billingCollectionsByMonth(branchId, numberMonths);
        List<Object> billings = billingCollectionsByMonth.get("billings");
        List<Object> collected = billingCollectionsByMonth.get("collected");
        List<Object> percentCollected = new ArrayList<>();

        for (int i = 0; i < billings.size(); i++) {

            double bill = (double) billings.get(i);
            double collect = (double) collected.get(i);
            double percent = 0.0;
            if (bill != 0)
                percent = (collect / bill) * 100.0;
            percentCollected.add(new BigDecimal(percent).round(new MathContext(2, RoundingMode.HALF_UP)));
        }

        map.put("months", billingCollectionsByMonth.get("months"));
        map.put("positions", billingCollectionsByMonth.get("positions"));
        map.put("percentCollected", percentCollected);
        return map;
    }

    public List<PractitionerBillingResultsDTO> monthlyBillingsByPractitioner(Long branchId, java.util.Date startDate, java.util.Date endDate, String lCodeDisplay, String usePatientBranch) {
        List<Object[]> practitionerBillingObjects = insuranceVerificationLCodeService.getPractitionerBillingTotalsBySubmissionDateBetweenAndBranchId(startDate, endDate, branchId, "true".equals(usePatientBranch));
//        BigDecimal a = BigDecimal.ZERO;
//        BigDecimal b = BigDecimal.ZERO;
//        for (Object[] objects : practitionerBillingObjects) {
//            b = b.add((BigDecimal) objects[2]);
//            a = a.add((BigDecimal) objects[3]);
//        }
//        System.out.println("b = " + b+ "\na = " + a);
        return monthlyBillingsBy(lCodeDisplay, practitionerBillingObjects);
    }

    public List<PractitionerBillingResultsDTO> monthlyBillingsByRepresentative(Long branchId, java.util.Date startDate, java.util.Date endDate, String lCodeDisplay, String usePatientBranch) {
        List<Object[]> practitionerBillingObjects = insuranceVerificationLCodeService.getViewUserBillingTotalsBySubmissionDateBetweenAndBranchId(startDate, endDate, branchId, "true".equals(usePatientBranch));
        return monthlyBillingsBy(lCodeDisplay, practitionerBillingObjects);
    }

    public List<PractitionerBillingResultsDTO> monthlyBillingsBy(String lCodeDisplay, List<Object[]> practitionerBillingObjects) {
        List<PractitionerBillingResultsDTO> resultsList = new ArrayList<>();
        if (practitionerBillingObjects.size() > 0) {
            Long prevPractitionerId = Long.parseLong(practitionerBillingObjects.get(0)[0].toString());
            PractitionerBillingResultsDTO pbrDTO = new PractitionerBillingResultsDTO();
            for (int i = 0; i < practitionerBillingObjects.size(); i++) {
                Object[] billingObject = practitionerBillingObjects.get(i);
                User practitioner = checkForPractitioner(billingObject);
                if (practitioner == null) {
                    continue;
                }
                if (!practitioner.getId().equals(prevPractitionerId)) {
                    resultsList.add(pbrDTO);
                    pbrDTO = new PractitionerBillingResultsDTO();
                    prevPractitionerId = practitioner.getId();
                }
                PractitionerBillingTotalDTO pbtDTO = new PractitionerBillingTotalDTO();

                if (lCodeDisplay.equals("lCodes")) {
                    if (billingObject[1] != null) {
                        Optional<L_Code> optionalL_code = l_codeRepository.findById(Long.parseLong(billingObject[1].toString()));
                        if (optionalL_code.isPresent()) {
                            pbtDTO.setL_code(optionalL_code.get());
                        } else {
                            pbtDTO.setL_code(null);
                        }
                    } else {
                        continue;
                    }
                } else {
                    if (billingObject[1] != null) {
                        Optional<L_Code> optionalL_code = l_codeRepository.findById(Long.parseLong(billingObject[1].toString()));
                        if (optionalL_code.isPresent()) {
                            L_Code lc = optionalL_code.get();
                            if (lc.getLCodeCategoryId() != null) {
                                pbtDTO.setL_codeCategory(l_codeCategoryService.findOne(lc.getLCodeCategoryId()));
                            }
                        }
                    }
                }

                pbrDTO.setPractitioner(practitioner);
                pbtDTO.setBillingTotal((BigDecimal) billingObject[2]);
                pbtDTO.setAllowableTotal((BigDecimal) billingObject[3]);
                pbrDTO.getPractitionerBillingTotalDTOs().add(pbtDTO);
                if (i + 1 == practitionerBillingObjects.size()) {
                    resultsList.add(pbrDTO);
                }
            }
        }

        // Add totals for each code/category to get overall billing and allowable totals
        processOverallBillingAndAllowableTotals(resultsList);
        return resultsList;
    }

    public void processOverallBillingAndAllowableTotals(List<PractitionerBillingResultsDTO> resultsList) {
        for (PractitionerBillingResultsDTO pbrDTO : resultsList) {
            BigDecimal overallBillingTotal = BigDecimal.ZERO;
            BigDecimal overallAllowableTotal = BigDecimal.ZERO;
            for (PractitionerBillingTotalDTO pbtDTO : pbrDTO.getPractitionerBillingTotalDTOs()) {
                overallBillingTotal = overallBillingTotal.add(pbtDTO.getBillingTotal());
                overallAllowableTotal = overallAllowableTotal.add(pbtDTO.getAllowableTotal());
            }
            pbrDTO.setOverallBillingTotal(overallBillingTotal);
            pbrDTO.setOverallAllowableTotal(overallAllowableTotal);
        }
    }

    public User checkForPractitioner(Object[] billingObject) {
        User practitioner;
        if (billingObject[0] != null) {
            practitioner = userService.getUserById(Long.parseLong(billingObject[0].toString()));
            if (practitioner == null) {
                System.out.println("Practioner is null for user id = " + billingObject[0].toString());
                return null;
            }
        } else {
            return null;
        }
        return practitioner;
    }

    public Map<String, List<Object>> billingCollectionsByMonth(Long branchId, Long numberMonths) {
        List<Object> months = new ArrayList<>();
        List<Object> positions = new ArrayList<>();
        List<Object> billings = new ArrayList<>();
        List<Object> collected = new ArrayList<>();
        Map<String, List<Object>> map = new HashMap<>();
        ArrayList<Long> prescriptionIds = new ArrayList<>();

        // use a position count to keep the months in order and spaced evenly
        int pos = 0;

        // go back one more month than the number passed in, example if one month is passed in, we're going to do just the prior month
        // if 6 months passed in, we do from 7 months back up through prior month
        for (int x = numberMonths.intValue(); x >= 1; x--) {
            Calendar calendar = Calendar.getInstance();                              // this should always take us to today so we can count backwards
            calendar.add(Calendar.MONTH, -x);                               // go to the correct month
            calendar.set(Calendar.DAY_OF_MONTH, 1);                         // now go to the beginning of that month and that's your start date
            Date startDate = new java.sql.Date(calendar.getTimeInMillis());
            int lastDayofMonth = calendar.getActualMaximum(Calendar.DATE);  // now jump to the end of that month and that's your end date
            calendar.set(Calendar.DATE, lastDayofMonth);
            Date endDate = new java.sql.Date(calendar.getTimeInMillis());
            int lastDate = calendar.getActualMaximum(Calendar.DATE);
            calendar.set(Calendar.DATE, lastDate);

            double billingsTotal = 0.0;
            double paymentsTotal = 0.0;
            List<ClaimSubmission> claimSubmissions = claimSubmissionService.getUniqueByClaimId(branchId, startDate, endDate);

            // billings (charges is an aggregation of all the total claim amounts
            for (ClaimSubmission claimSubmission : claimSubmissions) {
                if (!contains(prescriptionIds, claimSubmission.getClaim().getPrescriptionId())) {
                    prescriptionIds.add(claimSubmission.getClaim().getPrescriptionId());
                    billingsTotal += claimSubmission.getClaim().getTotalClaimAmount().doubleValue();
                }
                List<AppliedPayment> appliedPayments = appliedPaymentRepository.findByClaimId(claimSubmission.getClaim().getId());
                for (AppliedPayment payment : appliedPayments) {
                    paymentsTotal += payment.getAmountApplied().doubleValue();
                }
            }

            months.add(calendar.get(Calendar.MONTH)); // zero-based index same as on the angular side
            positions.add(pos++);
            billings.add(Math.round(billingsTotal * 100.0) / 100.0);
            collected.add(Math.round(paymentsTotal * 100.0) / 100.0);
        }

        map.put("months", months);
        map.put("positions", positions);
        map.put("billings", billings);
        map.put("collected", collected);
        return map;
    }

    public Map<String, Object> saveForVersion(Payment payment) {
        if (payment.getId() == null && payment.getCreatedById() == null) {
            payment.setCreatedById(userService != null && userService.getCurrentUser() != null ? userService.getCurrentUser().getId() : 1L);
        }

        Map<String, Object> respMap = new HashMap<>();
        String strLockingMessage = "This Payment has been edited by another user.  Please reload/re-search to get the latest version and re-save with your changes.";
        PaymentInfo currentPayment = findOne(payment.getId());
        if (currentPayment != null && payment.getVersion().intValue() != currentPayment.getVersion().intValue()) {
            // For formatting
            payment.setAmount(payment.getAmount().setScale(2, RoundingMode.UP));
            payment.setCredit(payment.getCredit().setScale(2, RoundingMode.UP));
            payment.setAdjustment(payment.getAdjustment().setScale(2, RoundingMode.UP));
            payment.setUnappliedAdjustment(payment.getUnappliedAdjustment().setScale(2, RoundingMode.UP));
            payment.setUnappliedAmount(payment.getUnappliedAmount().setScale(2, RoundingMode.UP));

            respMap = getAuditDetails(payment.getId(), optimisticLockingUtil.checkVersionLock(payment, currentPayment, strLockingMessage, Payment.class, currentPayment.getId()));
        } else {
            payment = super.save(payment);
            payment = findOne(payment.getId());
            respMap.put(SAVED, payment);
        }
        if (featureFlagService.findFeatureFlagByFeature("live_gl") != null) {
            try {
                generalLedgerService2.insertUpdatePaymentEntry(payment);
            } catch (Exception e) {
                Sentry.captureMessage("Payment # " + payment.getId() + " INSERT FAILURE - " + e.getCause().toString());
            }
        }
        return respMap;
    }

    public Map<String, List<Object>> billingCollectionsByYear(Long branchId, Long startYear, Long endYear) {
        List<Object> years = new ArrayList<>();
        List<Object> billings = new ArrayList<>();
        List<Object> collected = new ArrayList<>();
        Map<String, List<Object>> map = new HashMap<>();
        ArrayList<Long> prescriptionIds = new ArrayList<>();

        for (int y = startYear.intValue(); y <= endYear.intValue(); y++) {
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR, y);
            cal.set(Calendar.DAY_OF_YEAR, 1);
            Date startDate = new java.sql.Date(cal.getTimeInMillis());

            cal.set(Calendar.MONTH, 12);
            cal.set(Calendar.DAY_OF_MONTH, 31);
            cal.set(Calendar.HOUR_OF_DAY, 23);
            Date endDate = new java.sql.Date(cal.getTimeInMillis());

            // get all the claims during this time
            List<ClaimSubmission> claimSubmissions = claimSubmissionService.getUniqueByClaimId(branchId, startDate, endDate);
            double billingsTotal = 0.0;
            double paymentsTotal = 0.0;
            // billings (charges) is an aggregation of all the total claim amount + total patient responsibility amount
            for (ClaimSubmission claimSubmission : claimSubmissions) {
                if (!contains(prescriptionIds, claimSubmission.getClaim().getPrescriptionId())) {
                    prescriptionIds.add(claimSubmission.getClaim().getPrescriptionId());
                    billingsTotal += claimSubmission.getClaim().getTotalClaimAmount().doubleValue() + claimSubmission.getClaim().getTotalPtResponsibilityAmount().doubleValue();
                }
                List<Payment> payments = paymentRepository.findByClaimId(claimSubmission.getClaim().getId());
                for (Payment p : payments) {
                    paymentsTotal += p.getAmount().doubleValue();
                }
            }
            years.add(y);
            billings.add(Math.round(billingsTotal * 100.0) / 100.0);
            collected.add(Math.round(paymentsTotal * 100.0) / 100.0);
        }

        map.put("years", years);
        map.put("billings", billings);
        map.put("collected", collected);
        return map;
    }

    public List<ClaimSubmission> getClaimSubmissions(Date startDate, Date endDate, Long branchId, String deviceType) {
        List<ClaimSubmission> claimSubmissionList = claimSubmissionService.findBySubmissionDateBetween(startDate, endDate, branchId, deviceType);
        return claimSubmissionList.stream()
                .collect(collectingAndThen(toCollection(() -> new TreeSet<>(comparingLong(ClaimSubmission::getClaimId))), ArrayList::new));
    }

    public List<L_CodeBillingDTO> billingsByLCode(java.util.Date startDate, java.util.Date endDate) {
        List<L_CodeBillingDTO> lCodeBillings = new ArrayList<>();
        List<Object[]> lCodeBillingObjects = claimSubmissionRepository.getLCodeBillingTotalsBySubmissionDateBetween(startDate, endDate);
        for (Object[] lCodeBilling : lCodeBillingObjects) {
            L_CodeBillingDTO lCodeBillingDTO =
                    new L_CodeBillingDTO(
                            lCodeBilling[0].toString(),
                            Integer.parseInt(lCodeBilling[1].toString()),
                            BigDecimal.valueOf(Double.parseDouble(lCodeBilling[2].toString())),
                            BigDecimal.valueOf(Double.parseDouble(lCodeBilling[3].toString())));
            lCodeBillings.add(lCodeBillingDTO);
        }
        return lCodeBillings;
    }

    public DailyCloseReportDTO dailyClose(String payeeType, String dateOption, Date startDate, Date endDate, Long branchId) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        DailyCloseReportDTO result = new DailyCloseReportDTO();
        List<Branch> allBranches = branchRepository.findAll();
        List<Long> branchIds;
        if (branchId == null || branchId.equals(0L)) {
            branchIds = allBranches.stream().map(Branch::getId).toList();
        } else {
            branchIds = new ArrayList<>();
            branchIds.add(branchId);
        }
        if ("patient".equals(payeeType)) {
            payeeType = "patient%";
        } else if ("insurance_company".equals(payeeType)) {
            payeeType = "insurance%";
        }
        String payeeSQL = payeeType.equals("all") ? "%" : payeeType;
//            String query = paymentRepository.getAllDailyClosePaymentsForReportQuery.replaceAll(":startDate", "'" + startDate + "'")
//                .replaceAll(":endDate", "'" + endDate + "'")
//                .replaceAll(":dateOption", "'"+dateOption+ "'")
//                .replaceAll(":payerType", "'"+payeeSQL+ "'")
//                .replaceAll(":branches", branchIds.toString().replaceAll("\\[", "").replaceAll("\\]", ""));
//            System.out.println("query = " + query);
        List<DCPayment> payments = paymentRepository.getAllDailyClosePaymentsForReport(dateStart, dateEnd, dateOption, payeeSQL, branchIds);
        Map<Long, List<DCPayment>> paymentMapByBranch = payments.stream().collect(Collectors.groupingBy(Objects.requireNonNullElse(DCPayment::getBranchId, null)));
        for (Long bId : branchIds) {
            if (!paymentMapByBranch.containsKey(bId)) {
                paymentMapByBranch.put(bId, new ArrayList<>());
            }
        }

        List<DCPayment> appliedPayments = appliedPaymentRepository.getBranchesBulkPaymentAppliedPaymentsByDateOption(branchIds, startDate, endDate, dateOption);
        Map<Long, List<DCPayment>> bulkPaymentMapByBranch = appliedPayments.stream().collect(Collectors.groupingBy(Objects.requireNonNullElse(DCPayment::getBranchId, null)));;
        for (Long bId : branchIds) {
            if (!bulkPaymentMapByBranch.containsKey(bId)) {
                bulkPaymentMapByBranch.put(bId, new ArrayList<>());
            }
        }

        branchIds.forEach(bid -> {
            List<Branch> filtered = allBranches.stream().filter(e -> e.getId().equals(bid)).toList();
            if (filtered.isEmpty()) {
                return;
            }
            Branch b = filtered.get(0);
            DailyCloseBranchDTO dto = new DailyCloseBranchDTO();
            dto.setBranchId(b.getId());
            dto.setBranchName(b.getName());
            List<DCPayment> branchPayments = paymentMapByBranch.get(b.getId());
            List<DCPayment> branchBulkPayments = bulkPaymentMapByBranch.get(b.getId());
            dto.setPaymentList(branchPayments);
            dto.setAppliedPaymentList(branchBulkPayments);

            dto.setBranchCash(BigDecimal.ZERO);
            dto.setBranchCredit(BigDecimal.ZERO);
            dto.setBranchCheck(BigDecimal.ZERO);
            dto.setBranchAdjustment(BigDecimal.ZERO);
            dto.setBranchElectronic(BigDecimal.ZERO);
            dto.setBranchAch(BigDecimal.ZERO);
            dto.setBranchEra(BigDecimal.ZERO);
            dto.setBranchTotalApplied(BigDecimal.ZERO);
            dto.setBranchTotalUnApplied(BigDecimal.ZERO);
            dto.setBranchTotal(BigDecimal.ZERO);

            branchPayments.forEach(p -> {
                dto.setBranchTotalApplied(dto.getBranchTotalApplied().add(p.getPaymentApplied()));
                dto.setBranchTotalUnApplied(dto.getBranchTotalUnApplied().add(p.getPaymentUnapplied()));
                if ("adjustment".equals(p.getPaymentType()) || "patient_adjustment".equals(p.getPaymentType())) {
                    BigDecimal amount = p.getAdjustmentAmount();
                    if (BooleanUtils.toBooleanDefaultIfNull(p.getWithdraw(), false) && !StringUtils.isBlank(p.getPaymentTypeOrigin())) {
                        loadDailyCloseBranchTotals(dto, p.getPaymentTypeOrigin(), amount.negate());
                    } else {
                        dto.setBranchAdjustment(dto.getBranchAdjustment().add(amount));
                    }
                } else {
                    loadDailyCloseBranchTotals(dto, p.getPaymentType(), p.getPaymentAmount());
                }
            });

            branchBulkPayments.forEach(p -> {
                BigDecimal amount = p.getPaymentApplied();
                dto.setBranchTotalApplied(dto.getBranchTotalApplied().add(amount));
                dto.setBranchTotalUnApplied(dto.getBranchTotalUnApplied().add(p.getPaymentUnapplied()));

                if ("adjustment".equals(p.getPaymentType()) || "patient_adjustment".equals(p.getPaymentType())) {
                    dto.setBranchAdjustment(dto.getBranchAdjustment().add(amount));
                } else {
                    loadDailyCloseBranchTotals(dto, p.getPaymentType(), amount);
                }
            });
            result.getBranchCloseDtos().add(dto);
        });

        result.getBranchCloseDtos().forEach(cdto -> {
            result.setReportCash(result.getReportCash().add(cdto.getBranchCash()));
            result.setReportCheck(result.getReportCheck().add(cdto.getBranchCheck()));
            result.setReportCredit(result.getReportCredit().add(cdto.getBranchCredit()));
            result.setReportAdjustment(result.getReportAdjustment().add(cdto.getBranchAdjustment()));
            result.setReportElectronic(result.getReportElectronic().add(cdto.getBranchElectronic()));
            result.setReportAch(result.getReportAch().add(cdto.getBranchAch()));
            result.setReportEra(result.getReportEra().add(cdto.getBranchEra()));
            result.setReportApplied(result.getReportApplied().add(cdto.getBranchTotalApplied()));
            result.setReportUnApplied(result.getReportUnApplied().add(cdto.getBranchTotalUnApplied()));
            result.setReportTotalDeposited(result.getReportTotalDeposited().add(cdto.getBranchTotal()));
        });

        return result;
    }

    public void loadDailyCloseBranchTotals(DailyCloseBranchDTO dto, String paymentType, BigDecimal amount) {
        switch (paymentType) {
            case "cash":
                dto.setBranchCash(dto.getBranchCash().add(amount));
                dto.setBranchTotal(dto.getBranchTotal().add(amount));
                break;
            case "patient_check":
            case "insurance_payment_check":
                dto.setBranchCheck(dto.getBranchCheck().add(amount));
                dto.setBranchTotal(dto.getBranchTotal().add(amount));
                break;
            case "patient_credit_card":
            case "insurance_payment_credit_card":
            case "stripe":
                dto.setBranchCredit(dto.getBranchCredit().add(amount));
                dto.setBranchTotal(dto.getBranchTotal().add(amount));
                break;
            case "autopost":
                dto.setBranchEra(dto.getBranchEra().add(amount));
                dto.setBranchTotal(dto.getBranchTotal().add(amount));
                break;
            case "patient_ach":
                dto.setBranchAch(dto.getBranchAch().add(amount));
                dto.setBranchTotal(dto.getBranchTotal().add(amount));
                break;
            case "insurance_payment_electronic":
            case "patient_electronic":
                dto.setBranchElectronic(dto.getBranchElectronic().add(amount));
                dto.setBranchTotal(dto.getBranchTotal().add(amount));
                break;
        }
    }

    public List<CashRow> getDailyCloseV2(String payerType, String dateOption, Date startDate, Date endDate, Long branchId) {
        if ("patient".equals(payerType)) {
            payerType = "patient%";
        } else if ("insurance_company".equals(payerType)) {
            payerType = "insurance%";
        } else {
            payerType = "%";
        }
        List<CashRow> result = paymentRepository.getDailyCloseV2Rows(startDate, endDate, dateOption, payerType, branchId);
        result.removeIf((CashRow c) -> (BigDecimal.ZERO.equals(c.getPaymentAmount()) && BigDecimal.ZERO.equals(c.getAdjustmentAmount())));
        List<CashRow> subResult = paymentRepository.getDailyCloseV2SubRows();

        Iterator<CashRow> resItor = result.iterator();
        Iterator<CashRow> subItor = subResult.iterator();

        // add the patient claim level row data into the top level payment if there are multiple applied payments
        CashRow subRow = subItor.hasNext() ? subItor.next() : null;
        List<CashRow> subList = null;
        while (resItor.hasNext()) {
            CashRow topRow = resItor.next();
            Long paymentId = topRow.getPaymentId();

            if (topRow.getPatientFirstName() != null && topRow.getPatientLastName() != null) // if already associated with a claim, patient name exist
                continue;

            while (paymentId > subRow.getPaymentId() && subItor.hasNext()) {
                subRow = subItor.next();
            }

            while (paymentId.equals(subRow.getPaymentId())) {
                copyTopLevel(topRow, subRow);

                if (subList == null) {
                    subList = new ArrayList<>();
                    subList.add(subRow);
                    topRow.setAppliedPayments(subList);
                } else {
                    subList.add(subRow);
                }

                if (subItor.hasNext()) {
                    subRow = subItor.next();
                }
            }
            subList = null;
        }
        addUnappliedCashSubRow(result);
        return result;
    }

    private void copyTopLevel(CashRow topRow, CashRow subRow) {
        if (topRow.getPatientFirstName() == null && topRow.getPatientLastName() == null) {
            topRow.setPatientFirstName("Multiple Names");
            topRow.setPatientLastName("");
        }
        if (topRow.getTreatingPractitionerFirstName() == null && topRow.getTreatingPractitionerLastName() == null) {
            topRow.setTreatingPractitionerFirstName("Multiple Names");
            topRow.setTreatingPractitionerLastName("");
        }
        subRow.setPayerName(topRow.getPayerName());
        subRow.setPaymentType(topRow.getPaymentType());
        subRow.setPaymentAmount(topRow.getPaymentAmount());
        subRow.setPaymentDate(topRow.getPaymentDate());
        subRow.setCheckNumber(topRow.getCheckNumber());
        subRow.setPaymentDepositDate(topRow.getPaymentDepositDate());
        subRow.setCreatedByFirstName(topRow.getCreatedByFirstName());
        subRow.setCreatedByLastName(topRow.getCreatedByLastName());
        subRow.setPaymentDescription("Applied Payment");
        subRow.setPaymentAmount(topRow.getPaymentAmount());
        // subRow.setPaymentUnapplied(topRow.getPaymentUnapplied()); unapplied amount in its separate row
        subRow.setAdjustmentApplied(topRow.getAdjustmentApplied());
        subRow.setAdjustmentUnapplied(topRow.getAdjustmentUnapplied());
    }

    private void addUnappliedCashSubRow(List<CashRow> cashRowList) {
        cashRowList.forEach(data -> {
            if (data.getAppliedPayments() != null && (data.getPaymentUnapplied().abs()).compareTo(new BigDecimal(0.01)) >= 0) {
                CashRow unappliedRow = new CashRow(data.getPatientBranchName(), data.getRxBranchName(), data.getBillingBranchName(),
                        null, // data.getPatientId(),
                        "", // data.getPatientFirstName(),
                        "", // data.getPatientLastName(),
                        "", // data.getTreatingPractitionerFirstName(),
                        "", // data.getTreatingPractitionerLastName(),
                        data.getPaymentId(),
                        "", // data.getAppliedByFirstName(),
                        "", // data.getAppliedByLastName(),
                        null, // data.getPaymentApplied(),
                        null); // data.getAppliedDate());

                unappliedRow.setPaymentUnapplied(data.getPaymentUnapplied());
                unappliedRow.setPayerName(data.getPayerName());
                unappliedRow.setPaymentType(data.getPaymentType());
                unappliedRow.setPaymentDate(data.getPaymentDate());
                unappliedRow.setCheckNumber(data.getCheckNumber());
                unappliedRow.setPaymentDepositDate(data.getPaymentDepositDate());
                unappliedRow.setCreatedByFirstName(data.getCreatedByFirstName());
                unappliedRow.setCreatedByLastName(data.getCreatedByLastName());
                unappliedRow.setPaymentDescription("Unapplied Amount");
                unappliedRow.setPaymentAmount(data.getPaymentAmount());
                unappliedRow.setAppliedByFirstName("");
                unappliedRow.setAppliedByLastName("");

                data.getAppliedPayments().add(unappliedRow);
            }
        });
    }

    public List<PractitionerCommissionsDTO> getPractitionerCommissions(Date startDate, Date endDate, Long branchId, String dateOption, String deviceType) {
        return getPractitionerCommissionsDTOS(startDate, endDate, branchId, dateOption, deviceType, true);
    }

    public List<PractitionerCommissionsDTO> getRepresentativeCommissions(Date startDate, Date endDate, Long branchId, String dateOption, String deviceType) {
        return getPractitionerCommissionsDTOS(startDate, endDate, branchId, dateOption, deviceType, false);
    }

    public List<PractitionerCommissionsDTO> getPractitionerCommissionsDTOS(Date startDate, Date endDate, Long branchId, String dateOption, String deviceType, boolean isPractitioner) {
        List<PractitionerCommissionsDTO> dtoList = new ArrayList<>();
        if (deviceType == null || deviceType.equals("all") || deviceType.equals("")) {
            deviceType = "%";
        }
        List<AppliedPayment_L_Code> aplcList = appliedPaymentLCodeRepository.getAllAppliedPaymentLCodesByDateBetweenAndBranchAndDeviceType(startDate, endDate, branchId, dateOption, deviceType);

        // Create map to hold practitioner IDs with the payments and refunds.
        HashMap<Long, HashMap<String, BigDecimal>> map = new HashMap<>();

        for (AppliedPayment_L_Code aplc : aplcList) {
            Long treatingPractitionerId = null;
            if (isPractitioner) {
                treatingPractitionerId = aplc.getPrescriptionLCode().getPrescription().getTreatingPractitionerId();
            } else {
                treatingPractitionerId = aplc.getPrescriptionLCode().getPrescription().getViewUserId();
            }

            if (treatingPractitionerId != null) {
                //System.out.println("patient id = "+aplc.getPrescriptionLCode().getPrescription().getPatientId()+"  prescription id = "+aplc.getPrescriptionLCode().getPrescription().getId());
                // Add new entry to map if the aplc's treating practitioner isn't in the map already.
                if (!map.containsKey(treatingPractitionerId)) {
                    map.put(treatingPractitionerId, new HashMap<>());
                    map.get(treatingPractitionerId).put("insurancePayments", BigDecimal.ZERO);
                    map.get(treatingPractitionerId).put("insuranceRefunds", BigDecimal.ZERO);
                    map.get(treatingPractitionerId).put("patientPayments", BigDecimal.ZERO);
                    map.get(treatingPractitionerId).put("patientRefunds", BigDecimal.ZERO);
                }

                // Determine whether the amount is a payment or cash adjustment, then add it to the appropriate bucket.
                switch (aplc.getAppliedPayment().getPayment().getPayerType()) {
                    case "insurance_company":
                        map.get(treatingPractitionerId).put("insurancePayments", map.get(treatingPractitionerId).get("insurancePayments").add(aplc.getAmount()));
                        break;
                    case "patient":
                        map.get(treatingPractitionerId).put("patientPayments", map.get(treatingPractitionerId).get("patientPayments").add(aplc.getAmount()));
                        break;
                    case "adjustment":
                        if (aplc.getAppliedPayment().getPayment().getAdjustmentType().getWithdraw() != null && aplc.getAppliedPayment().getPayment().getAdjustmentType().getWithdraw()) {
                            map.get(treatingPractitionerId).put("insuranceRefunds", aplc.getAdjustment());
                        }
                        break;
                    case "patient_adjustment":
                        if (aplc.getAppliedPayment().getPayment().getAdjustmentType().getWithdraw() != null && aplc.getAppliedPayment().getPayment().getAdjustmentType().getWithdraw()) {
                            map.get(treatingPractitionerId).put("patientRefunds", aplc.getAdjustment());
                        }
                        break;
                }
            }
        }

        // Put the results from the map into a more readable DTO with all additional info.
        for (Map.Entry<Long, HashMap<String, BigDecimal>> entry : map.entrySet()) {
            PractitionerCommissionsDTO pcDTO = new PractitionerCommissionsDTO();
            User practitioner = userService.getUserById(entry.getKey());
            pcDTO.setPractitioner(practitioner);
            pcDTO.setInsurancePayments(entry.getValue().get("insurancePayments"));
            pcDTO.setInsuranceRefunds(entry.getValue().get("insuranceRefunds"));
            pcDTO.setTotalInsurancePaid(entry.getValue().get("insurancePayments").subtract(entry.getValue().get("insuranceRefunds")));
            pcDTO.setPatientPayments(entry.getValue().get("patientPayments"));
            pcDTO.setPatientRefunds(entry.getValue().get("patientRefunds"));
            pcDTO.setTotalPatientPaid(entry.getValue().get("patientPayments").subtract(entry.getValue().get("patientRefunds")));
            pcDTO.setTotalPayments(pcDTO.getTotalInsurancePaid().add(pcDTO.getTotalPatientPaid()));
            dtoList.add(pcDTO);
        }

        // Return DTO
        return dtoList;
    }

    public List<Payment> populateUnappliedPayments(Date startDate, Date endDate, Long branchId, String deviceType) {
        List<Payment> unappliedPayments;
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);

        if (branchId == null && StringUtil.isBlank(deviceType)) {
            unappliedPayments = paymentRepository.findUnappliedPatientPayments(dateStart, dateEnd);
        } else if (branchId != null && StringUtil.isBlank(deviceType)) {
            unappliedPayments = paymentRepository.findUnappliedPatientPaymentsByBranchId(dateStart, dateEnd, branchId);
        } else if (branchId == null && !StringUtil.isBlank(deviceType)) {
            unappliedPayments = paymentRepository.findUnappliedPatientPaymentsByDeviceType(dateStart, dateEnd, deviceType);
        } else {
            unappliedPayments = paymentRepository.findUnappliedPatientPaymentsByBranchIdAndDeviceType(dateStart, dateEnd, branchId, deviceType);
        }
        populatePaymentLoadForeignKeys(unappliedPayments);
        return unappliedPayments;
    }

    public List<Payment> populateClaimAppliedPayments(ClaimSubmission cs) {
        List<AppliedPayment> claimAppliedPayments = appliedPaymentService.getByClaimId(cs.getClaimId());
        List<Payment> payments = new ArrayList<>();
        for (AppliedPayment claimAP : claimAppliedPayments) {
            if (!payments.contains(claimAP.getPayment())) {
                loadForeignKeys(claimAP.getPayment());
                payments.add(claimAP.getPayment());
            }
        }
        return payments;
    }

    public List<Payment> getPaymentsForCashSummary(java.util.Date startDate,
                                                   java.util.Date endDate,
                                                   String dateOption,
                                                   String deviceType,
                                                   Long branchId) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        List<Payment> result = paymentRepository.getPaymentsForCashSummary(dateStart, dateEnd, dateOption, deviceType, branchId);
        populatePaymentLoadForeignKeys(result);
        return result;
    }

    public List<Payment> getPaymentsByBranchAndDate(java.util.Date startDate,
                                                    java.util.Date endDate,
                                                    String dateOption,
                                                    Long branchId) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        return paymentRepository.getPaymentsByBranchAndDate(dateStart, dateEnd, dateOption, branchId);
    }

    public List<Object[]> getAppliedAndUnappliedPaymentTotals(java.util.Date startDate, java.util.Date endDate) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        return paymentRepository.getAppliedAndUnappliedPaymentTotals(dateStart, dateEnd);
    }

    public BigDecimal getSumOfPaymentsByDateAndBranch(java.util.Date startDate,
                                                      java.util.Date endDate,
                                                      String dateOption,
                                                      Long branchId) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        BigDecimal result = paymentRepository.getSumOfPaymentsByDateAndBranch(dateStart, dateEnd, dateOption, branchId);
        return result == null ? BigDecimal.ZERO : result;
    }

    public List<Object[]> getLineAdjustmentTotalsByDate(java.util.Date startDate, java.util.Date endDate, String dateOption) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        return paymentRepository.getLineAdjustmentTotalsByDate(dateStart, dateEnd, dateOption);
    }

    public List<Object[]> getARAdjustmentTotalsByDate(java.util.Date startDate, java.util.Date endDate, String dateOption) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        return paymentRepository.getARAdjustmentTotalsByDate(dateStart, dateEnd, dateOption);
    }

    public List<Payment> findAllUnappliedPaymentsBeforeDate(Date startDate, Date endDate) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        List<Payment> payments = paymentRepository.findAllUnappliedPaymentsBeforeDate(dateStart, dateEnd);
        return payments;
    }

    public List<Payment> findAllNullDepositDatesBetweenDate(Date startDate, Date endDate) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
        List<Payment> payments = paymentRepository.findAllNullDepositDatesBetweenDate(dateStart, dateEnd);
        return payments;
    }

    public List<Payment> findByPrescriptionId(Long prescriptionId) {
        return paymentRepository.findByPrescriptionId(prescriptionId);
    }

    public Payment findTop1ByAutoPostPatientId(Long autoPostPatientId) {
        return paymentRepository.findTop1ByAutoPostPatientIdOrderByIdDesc(autoPostPatientId);
    }

    public List<Payment> findAllByAutoPostPatientIdIn(List<Long> autoPostPatientIds) {
        return paymentRepository.findAllByAutoPostPatientIdIn(autoPostPatientIds);
    }

    public IClaimTotals getClaimTotalsByClaimIdAndPrescriptionId(Long claimId, Long rxId) {
        return paymentRepository.getClaimTotalsByClaimIdAndPrescriptionId(claimId, rxId);
    }

    public Payment buildOffsetAdjustmentSalesAdjustment(BigDecimal totalAdjustmentAmount, Adjustment adjustment, Claim claim, Prescription rx, User user) {
        Payment result = new Payment();
        result.setVersion(0);
        result.setPaymentType("adjustment");
        result.setPayerType("adjustment");
        result.setDate(java.sql.Date.valueOf(LocalDate.now()));
        result.setAmount(BigDecimal.ZERO);
        result.setUnappliedAmount(BigDecimal.ZERO);
        result.setAdjustment(totalAdjustmentAmount);
        result.setUnappliedAdjustment(BigDecimal.ZERO);
        if (adjustment != null) {
            result.setAdjustmentId(adjustment.getId());
            result.setAdjustmentType(adjustment);
            result.setDescription(adjustment.getName());
        }
        result.setInsuranceCompanyId(claim.getPatientInsurance().getInsuranceCompanyId());
        result.setPrescriptionId(rx.getId());
        result.setClaimId(claim.getId());
        result.setPatientId(rx.getPatientId());
        //Question here not sure this used to be what we used for system actions.
        result.setCreatedById(user.getId());
        result.setCreatedAt(new Timestamp(Calendar.getInstance().getTimeInMillis()));
        return result;
    }

    public List<PaymentDTO> convertPayments(List<Payment> payments) {
        List<PaymentDTO> paymentDTOList = new ArrayList<>();
        for (Payment p : payments) {
            PaymentDTO dto = convertPaymentWithAppliedDate(p);
            paymentDTOList.add(dto);
        }
        return paymentDTOList;
    }

    public PaymentDTO convertPaymentWithAppliedDate(Payment payment) {
        PaymentDTO result = new PaymentDTO(payment);
        List<AppliedPayment> appliedPayments = appliedPaymentService.findByPaymentId(payment.getId());
        Date appliedDate = null;
        for (AppliedPayment ap : appliedPayments) {
            if (appliedDate == null) {
                appliedDate = ap.getAppliedDate();
            }
            if (appliedDate.before(ap.getAppliedDate())) {
                appliedDate = ap.getAppliedDate();
            }
        }
        result.setAppliedDate(appliedDate);
        return result;
    }

    public List<ClaimPayment> loadTransactionHistoryByClaimId(Long claimId) {
        List<ClaimPayment> claimPayments = new ArrayList<>();
        List<AppliedPayment> appliedPayments = appliedPaymentService.getByClaimId(claimId);
        for (AppliedPayment ap : appliedPayments) {
            ClaimPayment cp = new ClaimPayment(ap, ap.getAplcs());
            for (AppliedPayment_L_Code aplc : ap.getAplcs()) {
                BigDecimal temp = cp.adjustmentAppliedTotal.get(aplc.getId());
                if (temp == null) {
                    temp = BigDecimal.ZERO;
                }
                try {
                    BigDecimal total = appliedPaymentService.getTotalAdjustments(aplc, ap.getAppliedDate());
                    cp.adjustmentAppliedTotal.put(aplc.getId(), temp.add(total));
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            claimPayments.add(cp);
        }
        return claimPayments;
    }

    public List<ClaimPayment> loadTransactionHistoryByPrescriptionId(Long prescriptionId) {
        List<ClaimPayment> claimPayments = new ArrayList<>();
        List<Claim> claims = claimService.findByPrescriptionId(prescriptionId);
        for (Claim c : claims) {
            claimPayments.addAll(loadTransactionHistoryByClaimId(c.getId()));
        }
        return claimPayments;
    }

    public void deletePayment(Payment payment) {
        List<GeneralLedgerStatic> staticList = generalLedgerStaticService.findAllByPaymentId(payment.getId());
        List<GeneralLedger> payEntries = generalLedgerService.findAllByPaymentId(payment.getId());
        List<AppliedPayment> appliedPayments = appliedPaymentService.findByPaymentId(payment.getId());
        if ((staticList == null || staticList.isEmpty()) && (appliedPayments == null || appliedPayments.isEmpty())) {
            generalLedgerService.deleteInBatch(payEntries);
            delete(payment.getId());
        }
    }

}
