package com.nymbl.tenant.service;

import com.nymbl.config.Constants;
import com.nymbl.config.SqlConstants;
import com.nymbl.config.dto.*;
import com.nymbl.config.dto.arReports.ArAgingReportDTO;
import com.nymbl.config.dto.arReports.ArDataDTO;
import com.nymbl.config.dto.arReports.ArIvlcPeriodTotals;
import com.nymbl.config.dto.arReports.SalesDetailDTO;
import com.nymbl.config.dto.outstandingBalanceReport.OutstandingBalanceReportDTO;
import com.nymbl.config.dto.reports.PurchasingHistoryReport;
import com.nymbl.config.dto.reports.PurchasingHistoryReportRow;
import com.nymbl.config.model.FullName;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.NumberUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.config.utils.Utils;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.*;
import com.nymbl.tenant.specification.PurchaseOrderItemSpecs;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Version;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by J. Brett Rowell
 * 05/06/2019
 * Service To Centralize Adhoc Report Exports
 */
@Slf4j
@Service
public class AdHocReportService {

    private final ApplicationContext _appContext;
    private final InsuranceVerificationService insuranceVerificationService;
    private final InsuranceVerificationLCodeService insuranceVerificationLCodeService;
    private final AppliedPaymentService appliedPaymentService;
    private final AppliedPaymentRepository appliedPaymentRepository;
    private final ClaimSubmissionService claimSubmissionService;
    private final PaymentService paymentService;
    private final AppliedPaymentL_CodeService appliedPaymentLCodeService;
    private final PaymentRepository paymentRepository;
    private final PurchaseOrderService purchaseOrderService;
    private final ClaimRepository claimRepository;
    private final SystemSettingService systemSettingService;
    private final AppliedPayment_L_CodeRepository appliedPaymentLCodeRepository;
    private final BranchService branchService;
    private final InsuranceCompanyRepository insuranceCompanyRepository;
    private final TaskRepository taskRepository;
    private final PrescriptionService prescriptionService;
    private final PhoneNumberRepository phoneNumberRepository;
    private final UserService userService;
    private final GL_AccountRepository glAccountRepository;
    private final AdjustmentService adjustmentService;
    private final ClaimService claimService;
    private final PurchaseOrder_ItemService purchaseOrderItemService;
    private final EraAdjustmentReasonCodeService eraAdjustmentReasonCodeService;

    @Autowired
    public AdHocReportService(ApplicationContext _appContext,
                              InsuranceVerificationService insuranceVerificationService,
                              InsuranceVerificationLCodeService insuranceVerificationLCodeService,
                              AppliedPaymentService appliedPaymentService,
                              AppliedPaymentRepository appliedPaymentRepository,
                              PurchaseOrderService purchaseOrderService,
                              ClaimSubmissionService claimSubmissionService,
                              PaymentService paymentService,
                              AppliedPaymentL_CodeService appliedPaymentLCodeService,
                              PaymentRepository paymentRepository,
                              ClaimRepository claimRepository,
                              SystemSettingService systemSettingService,
                              AppliedPayment_L_CodeRepository appliedPaymentLCodeRepository,
                              BranchService branchService,
                              InsuranceCompanyRepository insuranceCompanyRepository,
                              TaskRepository taskRepository,
                              PrescriptionService prescriptionService,
                              PhoneNumberRepository phoneNumberRepository,
                              UserService userService,
                              GL_AccountRepository glAccountRepository,
                              AdjustmentService adjustmentService,
                              ClaimService claimService,
                              PurchaseOrder_ItemService purchaseOrderItemService,
                              EraAdjustmentReasonCodeService eraAdjustmentReasonCodeService) {
        this._appContext = _appContext;
        this.insuranceVerificationService = insuranceVerificationService;
        this.insuranceVerificationLCodeService = insuranceVerificationLCodeService;
        this.appliedPaymentService = appliedPaymentService;
        this.appliedPaymentRepository = appliedPaymentRepository;
        this.purchaseOrderService = purchaseOrderService;
        this.claimSubmissionService = claimSubmissionService;
        this.paymentService = paymentService;
        this.appliedPaymentLCodeService = appliedPaymentLCodeService;
        this.paymentRepository = paymentRepository;
        this.claimRepository = claimRepository;
        this.systemSettingService = systemSettingService;
        this.appliedPaymentLCodeRepository = appliedPaymentLCodeRepository;
        this.branchService = branchService;
        this.insuranceCompanyRepository = insuranceCompanyRepository;
        this.taskRepository = taskRepository;
        this.prescriptionService = prescriptionService;
        this.phoneNumberRepository = phoneNumberRepository;
        this.userService = userService;
        this.glAccountRepository = glAccountRepository;
        this.adjustmentService = adjustmentService;
        this.claimService = claimService;
        this.purchaseOrderItemService = purchaseOrderItemService;
        this.eraAdjustmentReasonCodeService = eraAdjustmentReasonCodeService;
    }

    public ByteArrayOutputStream getServerGeneratedExportFile(String primaryClassName, ArrayList<String> includedFields, ArrayList<String> includedKeys) throws Exception {

        // SCRUM-5304:
        String tenant = TenantContext.getCurrentTenant();
        log.info("**EXPORT TABLE START** (" + primaryClassName + "):" + DateUtil.timeFormatter().format(LocalDateTime.now()) + " for tenant: " + tenant);

        ByteArrayOutputStream outByteStream = new ByteArrayOutputStream();
        SXSSFWorkbook wb = new SXSSFWorkbook();
        wb = buildWorksheet(wb, primaryClassName, includedFields);
        List<String> worksheetsToInclude = new ArrayList<>();
        if (includedKeys != null) {
            includedKeys.removeIf(o -> o.equals("User"));
            worksheetsToInclude = includedKeys.stream().distinct().collect(Collectors.toList());
        }
        for (String key : worksheetsToInclude) {
            wb = buildWorksheet(wb, key, null);
        }
        wb.write(outByteStream);
        wb.close();

        // SCRUM-5304:
        log.info("*EXPORT TABLE END** (" + primaryClassName + "):" + DateUtil.timeFormatter().format(LocalDateTime.now()) + " for tenant: " + tenant);
        return outByteStream;
    }

    public SXSSFWorkbook buildWorksheet(SXSSFWorkbook wb, String className, ArrayList<String> includedFields) throws Exception {

        if (wb.getSheetIndex(className) < 0) {
            String beanName = StringUtil.checkCharUpperCase(className, 2) + "Service";
            Object bean = _appContext.getBean(beanName);

            Method method = bean.getClass().getMethod("exportTable", List.class);
            List<Map<String, Object>> list = (List<Map<String, Object>>) method.invoke(bean, includedFields);
            Integer maxSize = 65535;
            List<List<Map<String, Object>>> multipleSheets = new ArrayList<>();
            if (list.size() > maxSize) {
                populateMultipleSheets(list, maxSize, multipleSheets);
                for (int i = 0; i < multipleSheets.size(); i++) {
                    List<Map<String, Object>> aSheet = multipleSheets.get(i);
                    buildSheet(wb, className, i, includedFields, aSheet);
                }
            } else {
                buildSheet(wb, className, -1, includedFields, list);
            }
        }
        return wb;
    }

    public void populateMultipleSheets(List<Map<String, Object>> list, Integer maxSize, List<List<Map<String, Object>>> multipleSheets) {
        Integer size = list.size();
        Integer numSheets = size / maxSize;
        Integer remainder = size % maxSize;
        for (int i = 0; i < numSheets; i++) {
            Integer start = i * maxSize;
            multipleSheets.add(list.subList(start, (start + maxSize)));
        }
        if (remainder > 0) {
            Integer start = numSheets * maxSize;
            multipleSheets.add(list.subList(start, (start + remainder)));
        }
    }

    public void buildSheet(SXSSFWorkbook wb, String className, Integer sheetNumber, ArrayList<String> includedFields, List<Map<String, Object>> list) throws Exception {
        int rowCount = 0;
        int columnCount = 0;
        SXSSFSheet sheet = null;
        if (sheetNumber >= 0 && wb.getSheetIndex(className + sheetNumber) == -1) {
            sheet = wb.createSheet(className + sheetNumber);
        } else if (sheetNumber == -1) {
            sheet = wb.createSheet(className);
        }
        if (sheet != null) {
            Row header = sheet.createRow(rowCount++);
            Class modelClass = getClassModelFromName(className);
            Field[] fields = modelClass.getDeclaredFields();

            ArrayList<Field> workSheetFields = new ArrayList<>();
            for (Field field : fields) {
                boolean isTableColumn = field.isAnnotationPresent(Id.class) || field.isAnnotationPresent(Version.class) || field.isAnnotationPresent(Column.class);
                if (isTableColumn && ((includedFields == null || includedFields.contains(field.getName())) && !isForeignKey(field) && !field.getName().equals("password"))) {
                    workSheetFields.add(field);
                    Cell cell = header.createCell(columnCount++);
                    cell.setCellValue(StringUtil.headerFormat(field.getName()));
                }
            }

            //Build Sheet Data
            for (Map<String, Object> light : list) {
                columnCount = 0;
                Row row = sheet.createRow(rowCount++);
                for (Field field : workSheetFields) {
                    String name = field.getName();
                    if (field.isAnnotationPresent(Column.class)) {
                        name = "key".equals(field.getAnnotation(Column.class).name()) ? "`key`" : field.getAnnotation(Column.class).name();
                    }
                    Object value = light.get(name);
                    if (value != null) {
                        value = value.toString().replaceAll("<.+?>", "");
                    } else {
                        value = "";
                    }
                    if (value.toString().length() > 32767) {
                        String warning = " ERROR: This text is too long to fit and must be truncated";
                        String temp = value.toString().substring(0, 32767 - warning.length()) + warning;
                        row.createCell(columnCount++).setCellValue(temp);
                    } else {
                        row.createCell(columnCount++).setCellValue(value.toString());
                    }
                }
            }
        }
    }

    public boolean isForeignKey(Field field) {
        String[] excludedAnnotations = {"javax.persistence.JoinColumn", "com.fasterxml.jackson.annotation.JsonBackReference"};
        for (Annotation a : field.getDeclaredAnnotations()) {
            if (Arrays.asList(excludedAnnotations).contains(a.annotationType().getName())) {
                return true;
            }
        }
        return false;
    }

    // SCRUM-5304:
    public Boolean includeClassNameInTableListing(String className){

        if(className.contains("summary.") || className.contains("Dto"))
            return false;

        switch (className){
            case "AuditRevision":
            case "ExpandedGeneralLedger":
            case "ExpandedClinicalOperations":
            case "FeatureFlag":
            case "GeneralLedger":
            case "GeneralLedgerStatic":
            case "GeneralLedgerLive":
            case "GenerateModelObject":
            case "ModelStub":
            case "Message":
            case "Note":
            case "NotificationRecord":
            case "PrintScreen":
            case "SystemSetting":
            case "Block":
            case "ExpandedRxSummary":
                return false;
            default:
                //System.out.println(className);
                return true;
        }
    }
    public List<Map<String, String>> tableListing() {
        List<String> classes = Utils.getClassNames();
        String classPath = "com.nymbl.tenant.model.";
        List<Map<String, String>> results = new ArrayList<>();
        try {
            for (String c : classes) {
                Map<String, String> map = new HashMap<>();
                String className = c.replaceAll(classPath, "");
                if(includeClassNameInTableListing(className)) {
                    map.put("id", className);
                    String name = className.replaceAll("_", "");
                    String[] array = StringUtils.splitByCharacterTypeCamelCase(name);
                    map.put("label", StringUtils.join(array, " "));
                    results.add(map);
                }
            }
            return results;
        } catch (Exception ex) {
            return null;
        }
    }

    //Done to handle class not found for master model before commit break down to classnotfound and invocationtarget
    @SuppressWarnings({"finally", "ReturnInsideFinallyBlock"})
    private Class<?> getClassModelFromName(String className) {
        Class<?> result = null;
        try {
            result = Class.forName("com.nymbl.tenant.model.".concat(className));
        } catch (Exception e) {
            result = Class.forName("com.nymbl.master.model.".concat(className));
        } finally {
            return result;
        }
    }

    public List<Map<String, String>> getClassMetaData(String className) throws Exception {
        List<Map<String, String>> result = new ArrayList<>();
        Class<?> modelClass = Class.forName("com.nymbl.tenant.model.".concat(className));
        Field[] allFields = modelClass.getDeclaredFields();
        for (Field field : allFields) {
            if (!field.getName().toLowerCase().endsWith("signature")) {
                Map<String, String> temp = new HashMap<>();
                temp.put("name", field.getName());
                temp.put("header", StringUtil.headerFormat(field.getName()));
                if (Collection.class.isAssignableFrom(field.getType())) {
                    temp.put("type", ((Class<?>) ((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0]).getSimpleName());
                } else {
                    temp.put("type", field.getType().getSimpleName());
                }
                temp.put("join", isForeignKey(field) ? "true" : "false");
                result.add(temp);
            }
        }
        return result;
    }

    //    Returns a list of payments sorted by payer with accumulated totals
    public List<PaymentsByPayerWithTotalsDTO> paymentsByPayer(Date startDate, Date endDate, Long branchId, String dateOption) {
        List<PaymentsByPayerWithTotalsDTO> resultsList = new ArrayList<>();

//        Get payments from DB, filtered by Branch and Payment/Deposit date range
        List<Payment> payments = paymentService.getPaymentsByBranchAndDate(startDate, endDate, dateOption, branchId);

//        Organize payments into a map sorted by insurance company
        Map<InsuranceCompany, List<Payment>> paymentMap = payments
                .stream()
                .filter(p -> p.getInsuranceCompanyId() != null)
                .collect(Collectors.groupingBy(Payment::getInsuranceCompany));

//        Loop through paymentMap and sum payment amounts according to device type
        for (Map.Entry<InsuranceCompany, List<Payment>> payer : paymentMap.entrySet()) {
            PaymentsByPayerWithTotalsDTO dto = new PaymentsByPayerWithTotalsDTO();
            dto.setPayer(payer.getKey());
            List<Payment> payerPayments = payer.getValue();
            for (Payment p : payerPayments) {
                if (p.getClaimId() == null) {
                    List<AppliedPayment> appliedPayments = appliedPaymentService.findByPaymentId(p.getId());
                    for (AppliedPayment ap : appliedPayments) {
                        if (ap.getClaim().getPrescription().getDeviceType() != null) {
                            if (ap.getClaim().getPrescription().getDeviceType().getOrthoticOrProsthetic() != null) {
                                if (ap.getClaim().getPrescription().getDeviceType().getOrthoticOrProsthetic().equals("orthotic")) {
                                    dto.setOrthoticPaymentTotal(dto.getOrthoticPaymentTotal().add(ap.getAmountApplied()));
                                    dto.getOrthoticPaymentList().add(ap.getPayment());

                                } else if (ap.getClaim().getPrescription().getDeviceType().getOrthoticOrProsthetic().equals("prosthetic")) {
                                    dto.setProstheticPaymentTotal(dto.getProstheticPaymentTotal().add(ap.getAmountApplied()));
                                    dto.getProstheticPaymentList().add(ap.getPayment());
                                }
                            } else {
                                dto.setNoDeviceTypePaymentTotal(dto.getNoDeviceTypePaymentTotal().add(p.getAmount()));
                                dto.getNoDeviceTypePaymentList().add(ap.getPayment());
                            }
                        }
                    }
                } else {
                    if (p.getClaim().getPrescription().getDeviceType() != null) {
                        if (p.getClaim().getPrescription().getDeviceType().getOrthoticOrProsthetic() != null) {
                            if (p.getClaim().getPrescription().getDeviceType().getOrthoticOrProsthetic().equals("orthotic")) {
                                dto.setOrthoticPaymentTotal(dto.getOrthoticPaymentTotal().add(p.getAmount()));
                                dto.getOrthoticPaymentList().add(p);

                            } else if (p.getClaim().getPrescription().getDeviceType().getOrthoticOrProsthetic().equals("prosthetic")) {
                                dto.setProstheticPaymentTotal(dto.getProstheticPaymentTotal().add(p.getAmount()));
                                dto.getProstheticPaymentList().add(p);
                            }
                        }
                    }
                }
                dto.setPaymentTotal(dto.getOrthoticPaymentTotal().add(dto.getProstheticPaymentTotal()));
                dto.setAdjustmentTotal(dto.getOrthoticAdjustmentTotal().add(dto.getProstheticAdjustmentTotal()));
            }

            BillingSummaryDTO orthoBillingSummaryDTO = new BillingSummaryDTO();
            orthoBillingSummaryDTO = paymentAdjustmentAndAppliedAmount(orthoBillingSummaryDTO, dto.getOrthoticPaymentList());

            BillingSummaryDTO prosBillingSummaryDTO = new BillingSummaryDTO();
            prosBillingSummaryDTO = paymentAdjustmentAndAppliedAmount(prosBillingSummaryDTO, dto.getProstheticPaymentList());

            dto.setOrthoticAdjustmentTotal(orthoBillingSummaryDTO.getAdjustmentsTotal());
            dto.setProstheticAdjustmentTotal(prosBillingSummaryDTO.getAdjustmentsTotal());

            dto.setOrthoticLineAdjustmentTotal(orthoBillingSummaryDTO.getLineAdjustmentsTotal());
            dto.setProstheticLineAdjustmentTotal(prosBillingSummaryDTO.getLineAdjustmentsTotal());

            dto.setAdjustmentTotal(dto.getOrthoticAdjustmentTotal().add(dto.getProstheticAdjustmentTotal()).add(dto.getNoDeviceTypeAdjustmentTotal()));
            dto.setLineAdjustmentTotal(dto.getOrthoticLineAdjustmentTotal().add(dto.getProstheticLineAdjustmentTotal()).add(dto.getNoDeviceTypeLineAdjustmentTotal()));

            dto.setOrthoticAdjustmentList(orthoBillingSummaryDTO.getAdjustmentsMap());
            dto.setProstheticAdjustmentList(prosBillingSummaryDTO.getAdjustmentsMap());

            dto.setOrthoticLineAdjustmentList(orthoBillingSummaryDTO.getLineAdjustmentsMap());
            dto.setProstheticLineAdjustmentList(prosBillingSummaryDTO.getLineAdjustmentsMap());

            Map<String, BigDecimal> combinedAdjustmentMap = new HashMap<>(dto.getOrthoticAdjustmentList());
            dto.getProstheticAdjustmentList().forEach((k, v) -> combinedAdjustmentMap.merge(k, v, BigDecimal::add));
            dto.setAdjustmentList(combinedAdjustmentMap);

            Map<String, BigDecimal> combinedLineAdjustmentMap = new HashMap<>(dto.getOrthoticLineAdjustmentList());
            dto.getProstheticLineAdjustmentList().forEach((k, v) -> combinedLineAdjustmentMap.merge(k, v, BigDecimal::add));
            dto.setLineAdjustmentList(combinedLineAdjustmentMap);

            dto.setAllAdjustmentTotal(dto.getAdjustmentTotal().add(dto.getLineAdjustmentTotal()));
            dto.setOrthoticAllAdjustmentTotal(dto.getOrthoticAdjustmentTotal().add(dto.getOrthoticLineAdjustmentTotal()));
            dto.setProstheticAllAdjustmentTotal(dto.getProstheticAdjustmentTotal().add(dto.getProstheticLineAdjustmentTotal()));

            resultsList.add(dto);
        }
        return resultsList;
    }

    public BillingSummaryDTO cashSummary(Date startDate, Date endDate, Long branchId, String deviceType, String dateOption) {
        BillingSummaryDTO dto = new BillingSummaryDTO();
        branchId = branchId == null ? 0L : branchId;
        List<Payment> payments = paymentService.getPaymentsForCashSummary(startDate, endDate, dateOption, deviceType, branchId);

        dto = paymentAdjustmentAndAppliedAmount(dto, payments);

        dto.setTotalPatientCreditCard(dto.getPaymentsUnappliedPatientCreditCard().add(dto.getPaymentsAppliedPatientCreditCard()));
        dto.setTotalPatientCash(dto.getPaymentsAppliedPatientCash().add(dto.getPaymentsUnappliedPatientCash()));
        dto.setTotalPatientChecks(dto.getPaymentsAppliedPatientChecks().add(dto.getPaymentsUnappliedPatientChecks()));

        dto.setTotalAppliedPatient(dto.getPaymentsUnappliedPatientChecks().add(dto.getPaymentsAppliedPatientCash()).add(dto.getPaymentsAppliedPatientCreditCard()));
        dto.setTotalUnappliedPatient(dto.getPaymentsUnappliedPatientChecks().add(dto.getPaymentsUnappliedPatientCash()).add(dto.getPaymentsUnappliedPatientCreditCard()));

        dto.setTotalInsurancePayment(dto.getTotalAppliedInsurance().add(dto.getTotalUnappliedInsurance()));
        dto.setTotalPatientPayment(dto.getTotalPatientChecks().add(dto.getTotalPatientCash().add(dto.getTotalPatientCreditCard())));

        dto.setPaymentsTotal(dto.getTotalPatientPayment().add(dto.getTotalInsurancePayment()));
        return dto;
    }

    public Map<String, BillingSummaryDTO> salesSummary(java.sql.Date startDate, Date endDate, Long branchId, String dateOption, String usePatientBranch) {
        List<ClaimsSummaryDTO> claimsSummaryDTOList = getClaimsSummaryDTOListForSalesReportsFilterByClaimSubmissionDateTrue(startDate, endDate, branchId, usePatientBranch);
        return salesSummary(startDate, endDate, branchId, dateOption, claimsSummaryDTOList);
    }

    public Map<String, BillingSummaryDTO> salesSummary(Date startDate, Date endDate, Long branchId, String dateOption, List<ClaimsSummaryDTO> claimsSummaryDTOList) {
        Map<String, BillingSummaryDTO> salesSummaryMap = new HashMap<>();

        BillingSummaryDTO orthoticSalesSummary = new BillingSummaryDTO();
        BillingSummaryDTO prostheticSalesSummary = new BillingSummaryDTO();
        BillingSummaryDTO pedorthicSalesSummary = new BillingSummaryDTO();
        BillingSummaryDTO mastectomySalesSummary = new BillingSummaryDTO();
        BillingSummaryDTO miscSalesSummary = new BillingSummaryDTO();
        BillingSummaryDTO allDeviceTypeSalesSummary = new BillingSummaryDTO();

        Map<String, ArDataDTO> arDataDTOMap = new HashMap<>();
        arDataDTOMap.put("orthotic", new ArDataDTO());
        arDataDTOMap.put("prosthetic", new ArDataDTO());
        arDataDTOMap.put("pedorthic", new ArDataDTO());
        arDataDTOMap.put("mastectomy", new ArDataDTO());
        arDataDTOMap.put("misc", new ArDataDTO());

//        List<AppliedPaymentLCodesAmountsDTO> orthoticApLcodeAmountsList = getListOfAppliedPaymentLCodeData(startDate, endDate, endDate, branchId, "payment", "orthotic");
        processArDataDTOSummary(orthoticSalesSummary, claimsSummaryDTOList, "orthotic");

//        List<AppliedPaymentLCodesAmountsDTO> prostheticApLcodeAmountsList = getListOfAppliedPaymentLCodeData(startDate, endDate, endDate, branchId, "payment", "prosthetic");
        processArDataDTOSummary(prostheticSalesSummary, claimsSummaryDTOList, "prosthetic");

//        List<AppliedPaymentLCodesAmountsDTO> pedorthicApLcodeAmountsList = getListOfAppliedPaymentLCodeData(startDate, endDate, endDate, branchId, "payment", "pedorthic");
        processArDataDTOSummary(pedorthicSalesSummary, claimsSummaryDTOList, "pedorthic");

//        List<AppliedPaymentLCodesAmountsDTO> miscApLcodeAmountsList = getListOfAppliedPaymentLCodeData(startDate, endDate, endDate, branchId, "payment", "misc");
        processArDataDTOSummary(miscSalesSummary, claimsSummaryDTOList, "misc");

//        System.out.println("claimRepository.findClaimsWithPaymentWithOrWithoutClaimSubmissionQuery = \n"+
//                claimRepository.findClaimsWithPaymentWithOrWithoutClaimSubmissionQuery
//                        .replaceAll(":branchId", branchId+"")
//                        .replaceAll(":startDate", "'"+startDate+"'")
//                        .replaceAll(":endDate", "'"+endDate+"'")
//        );

        Map<String, BigDecimal> arAdjustmentsMap = populateDeviceTypeMap();

        DataModel dm = new DataModel();
        dm.setHeader(SqlConstants.getSalesSummaryArAdjustmentsHeader);
        dm.setData(appliedPaymentLCodeRepository.getSalesSummaryArAdjustments(branchId, dateOption, startDate, endDate));
        for(Object[] row : dm.getData()) {
            arAdjustmentsMap.put(dm.getString("orthotic_or_prosthetic",row),dm.getBigDecimal("adjustment",row));
        }

        Map<String, BigDecimal> cdMap = populateDeviceTypeMap();
        Map<String, BigDecimal> lineMap = populateDeviceTypeMap();
        getContractualDifferenceForSalesSummary(startDate, endDate, branchId, dateOption, cdMap, lineMap);

        orthoticSalesSummary.setContractualAdjustmentsRange(cdMap.get("orthotic"));
        orthoticSalesSummary.setLineAdjustmentsRange(lineMap.get("orthotic"));
        orthoticSalesSummary.setArAdjustmentsRange(arAdjustmentsMap.get("orthotic"));

        prostheticSalesSummary.setContractualAdjustmentsRange(cdMap.get("prosthetic"));
        prostheticSalesSummary.setLineAdjustmentsRange(lineMap.get("prosthetic"));
        prostheticSalesSummary.setArAdjustmentsRange(arAdjustmentsMap.get("prosthetic"));

        pedorthicSalesSummary.setContractualAdjustmentsRange(cdMap.get("pedorthic"));
        pedorthicSalesSummary.setLineAdjustmentsRange(lineMap.get("pedorthic"));
        pedorthicSalesSummary.setArAdjustmentsRange(arAdjustmentsMap.get("pedorthic"));

        pedorthicSalesSummary.setContractualAdjustmentsRange(cdMap.get("mastectomy"));
        pedorthicSalesSummary.setLineAdjustmentsRange(lineMap.get("mastectomy"));
        pedorthicSalesSummary.setArAdjustmentsRange(arAdjustmentsMap.get("mastectomy"));

        miscSalesSummary.setContractualAdjustmentsRange(cdMap.get("misc"));
        miscSalesSummary.setLineAdjustmentsRange(lineMap.get("misc"));
        miscSalesSummary.setArAdjustmentsRange(arAdjustmentsMap.get("misc"));

        allDeviceTypeSalesSummary.setCharges(orthoticSalesSummary.getCharges().add(prostheticSalesSummary.getCharges()).add(pedorthicSalesSummary.getCharges()).add(miscSalesSummary.getCharges()).add(mastectomySalesSummary.getCharges()));
        allDeviceTypeSalesSummary.setInitialUnallowed(orthoticSalesSummary.getInitialUnallowed().add(prostheticSalesSummary.getInitialUnallowed()).add(pedorthicSalesSummary.getInitialUnallowed()).add(miscSalesSummary.getInitialUnallowed()).add(mastectomySalesSummary.getInitialUnallowed()));
        allDeviceTypeSalesSummary.setTotalAllowable(orthoticSalesSummary.getTotalAllowable().add(prostheticSalesSummary.getTotalAllowable()).add(pedorthicSalesSummary.getTotalAllowable()).add(miscSalesSummary.getTotalAllowable()).add(mastectomySalesSummary.getTotalAllowable()));
        allDeviceTypeSalesSummary.setContractualAdjustmentsRange(orthoticSalesSummary.getContractualAdjustmentsRange().add(prostheticSalesSummary.getContractualAdjustmentsRange()).add(pedorthicSalesSummary.getContractualAdjustmentsRange()).add(miscSalesSummary.getContractualAdjustmentsRange()).add(mastectomySalesSummary.getContractualAdjustmentsRange()));
        allDeviceTypeSalesSummary.setLineAdjustmentsRange(orthoticSalesSummary.getLineAdjustmentsRange().add(prostheticSalesSummary.getLineAdjustmentsRange()).add(pedorthicSalesSummary.getLineAdjustmentsRange()).add(miscSalesSummary.getLineAdjustmentsRange()).add(mastectomySalesSummary.getLineAdjustmentsRange()));
        allDeviceTypeSalesSummary.setArAdjustmentsRange(orthoticSalesSummary.getArAdjustmentsRange().add(prostheticSalesSummary.getArAdjustmentsRange()).add(pedorthicSalesSummary.getArAdjustmentsRange()).add(miscSalesSummary.getArAdjustmentsRange()).add(mastectomySalesSummary.getArAdjustmentsRange()));

        orthoticSalesSummary.setClaimsFiled(orthoticSalesSummary.getPrimaryClaimsFiled() + orthoticSalesSummary.getSecondaryClaimsFiled() + orthoticSalesSummary.getTertiaryClaimsFiled()
                + orthoticSalesSummary.getQuaternaryClaimsFiled() + orthoticSalesSummary.getQuinaryClaimsFiled() + orthoticSalesSummary.getSenaryClaimsFiled()
                + orthoticSalesSummary.getSeptenaryClaimsFiled() + orthoticSalesSummary.getOctonaryClaimsFiled() + orthoticSalesSummary.getNonaryClaimsFiled()
                + orthoticSalesSummary.getDenaryClaimsFiled() + orthoticSalesSummary.getOtherClaimsFiled());
        prostheticSalesSummary.setClaimsFiled(prostheticSalesSummary.getPrimaryClaimsFiled() + prostheticSalesSummary.getSecondaryClaimsFiled()
                + prostheticSalesSummary.getTertiaryClaimsFiled() + prostheticSalesSummary.getQuaternaryClaimsFiled()
                + prostheticSalesSummary.getQuinaryClaimsFiled() + prostheticSalesSummary.getSenaryClaimsFiled()
                + prostheticSalesSummary.getSeptenaryClaimsFiled() + prostheticSalesSummary.getOctonaryClaimsFiled()
                + prostheticSalesSummary.getNonaryClaimsFiled() + prostheticSalesSummary.getDenaryClaimsFiled()
                + prostheticSalesSummary.getOtherClaimsFiled());
        pedorthicSalesSummary.setClaimsFiled(pedorthicSalesSummary.getPrimaryClaimsFiled() + pedorthicSalesSummary.getSecondaryClaimsFiled()
                + pedorthicSalesSummary.getTertiaryClaimsFiled() + pedorthicSalesSummary.getQuaternaryClaimsFiled()
                + pedorthicSalesSummary.getQuinaryClaimsFiled() + pedorthicSalesSummary.getSenaryClaimsFiled()
                + pedorthicSalesSummary.getSeptenaryClaimsFiled() + pedorthicSalesSummary.getOctonaryClaimsFiled()
                + pedorthicSalesSummary.getNonaryClaimsFiled() + pedorthicSalesSummary.getDenaryClaimsFiled()
                + pedorthicSalesSummary.getOtherClaimsFiled());
        miscSalesSummary.setClaimsFiled(miscSalesSummary.getPrimaryClaimsFiled() + miscSalesSummary.getSecondaryClaimsFiled()
                + miscSalesSummary.getTertiaryClaimsFiled() + miscSalesSummary.getQuaternaryClaimsFiled()
                + miscSalesSummary.getQuinaryClaimsFiled() + miscSalesSummary.getSenaryClaimsFiled()
                + miscSalesSummary.getSeptenaryClaimsFiled() + miscSalesSummary.getOctonaryClaimsFiled()
                + miscSalesSummary.getNonaryClaimsFiled() + miscSalesSummary.getDenaryClaimsFiled()
                + miscSalesSummary.getOtherClaimsFiled());
        mastectomySalesSummary.setClaimsFiled(mastectomySalesSummary.getPrimaryClaimsFiled() + mastectomySalesSummary.getSecondaryClaimsFiled()
                + mastectomySalesSummary.getTertiaryClaimsFiled() + mastectomySalesSummary.getQuaternaryClaimsFiled()
                + mastectomySalesSummary.getQuinaryClaimsFiled() + mastectomySalesSummary.getSenaryClaimsFiled()
                + mastectomySalesSummary.getSeptenaryClaimsFiled() + mastectomySalesSummary.getOctonaryClaimsFiled()
                + mastectomySalesSummary.getNonaryClaimsFiled() + mastectomySalesSummary.getDenaryClaimsFiled()
                + mastectomySalesSummary.getOtherClaimsFiled());

        allDeviceTypeSalesSummary.setPrimaryClaimsFiled(orthoticSalesSummary.getPrimaryClaimsFiled() + prostheticSalesSummary.getPrimaryClaimsFiled() + pedorthicSalesSummary.getPrimaryClaimsFiled() + miscSalesSummary.getPrimaryClaimsFiled() + mastectomySalesSummary.getPrimaryClaimsFiled());
        allDeviceTypeSalesSummary.setSecondaryClaimsFiled(orthoticSalesSummary.getSecondaryClaimsFiled() + prostheticSalesSummary.getSecondaryClaimsFiled() + pedorthicSalesSummary.getSecondaryClaimsFiled() + miscSalesSummary.getSecondaryClaimsFiled() + mastectomySalesSummary.getSecondaryClaimsFiled());
        allDeviceTypeSalesSummary.setTertiaryClaimsFiled(orthoticSalesSummary.getTertiaryClaimsFiled() + prostheticSalesSummary.getTertiaryClaimsFiled() + pedorthicSalesSummary.getTertiaryClaimsFiled() + miscSalesSummary.getTertiaryClaimsFiled() + mastectomySalesSummary.getTertiaryClaimsFiled());
        allDeviceTypeSalesSummary.setQuaternaryClaimsFiled(orthoticSalesSummary.getQuaternaryClaimsFiled() + prostheticSalesSummary.getQuaternaryClaimsFiled() + pedorthicSalesSummary.getQuaternaryClaimsFiled() + miscSalesSummary.getQuaternaryClaimsFiled() + mastectomySalesSummary.getQuaternaryClaimsFiled());
        allDeviceTypeSalesSummary.setQuinaryClaimsFiled(orthoticSalesSummary.getQuinaryClaimsFiled() + prostheticSalesSummary.getQuinaryClaimsFiled() + pedorthicSalesSummary.getQuinaryClaimsFiled() + miscSalesSummary.getQuinaryClaimsFiled() + mastectomySalesSummary.getQuinaryClaimsFiled());
        allDeviceTypeSalesSummary.setSenaryClaimsFiled(orthoticSalesSummary.getSenaryClaimsFiled() + prostheticSalesSummary.getSenaryClaimsFiled() + pedorthicSalesSummary.getSenaryClaimsFiled() + miscSalesSummary.getSenaryClaimsFiled() + mastectomySalesSummary.getSenaryClaimsFiled());
        allDeviceTypeSalesSummary.setSeptenaryClaimsFiled(orthoticSalesSummary.getSeptenaryClaimsFiled() + prostheticSalesSummary.getSeptenaryClaimsFiled() + pedorthicSalesSummary.getSeptenaryClaimsFiled() + miscSalesSummary.getSeptenaryClaimsFiled() + mastectomySalesSummary.getSeptenaryClaimsFiled());
        allDeviceTypeSalesSummary.setOctonaryClaimsFiled(orthoticSalesSummary.getOctonaryClaimsFiled() + prostheticSalesSummary.getOctonaryClaimsFiled() + pedorthicSalesSummary.getOctonaryClaimsFiled() + miscSalesSummary.getOctonaryClaimsFiled() + mastectomySalesSummary.getOctonaryClaimsFiled());
        allDeviceTypeSalesSummary.setNonaryClaimsFiled(orthoticSalesSummary.getNonaryClaimsFiled() + prostheticSalesSummary.getNonaryClaimsFiled() + pedorthicSalesSummary.getNonaryClaimsFiled() + miscSalesSummary.getNonaryClaimsFiled() + mastectomySalesSummary.getNonaryClaimsFiled());
        allDeviceTypeSalesSummary.setDenaryClaimsFiled(orthoticSalesSummary.getDenaryClaimsFiled() + prostheticSalesSummary.getDenaryClaimsFiled() + pedorthicSalesSummary.getDenaryClaimsFiled() + miscSalesSummary.getDenaryClaimsFiled() + mastectomySalesSummary.getDenaryClaimsFiled());
        allDeviceTypeSalesSummary.setOtherClaimsFiled(orthoticSalesSummary.getOtherClaimsFiled() + prostheticSalesSummary.getOtherClaimsFiled() + pedorthicSalesSummary.getOtherClaimsFiled() + miscSalesSummary.getOtherClaimsFiled() + mastectomySalesSummary.getOtherClaimsFiled());
        allDeviceTypeSalesSummary.setClaimsFiled(orthoticSalesSummary.getClaimsFiled() + prostheticSalesSummary.getClaimsFiled() + pedorthicSalesSummary.getClaimsFiled() + miscSalesSummary.getClaimsFiled() + mastectomySalesSummary.getClaimsFiled());

        salesSummaryMap.put("orthotic", orthoticSalesSummary);
        salesSummaryMap.put("prosthetic", prostheticSalesSummary);
        salesSummaryMap.put("pedorthic", pedorthicSalesSummary);
        salesSummaryMap.put("mastectomy", mastectomySalesSummary);
        salesSummaryMap.put("misc", miscSalesSummary);
        salesSummaryMap.put("allDeviceType", allDeviceTypeSalesSummary);

        return salesSummaryMap;
    }

    public void getContractualDifferenceForSalesSummary(Date startDate, Date endDate, Long branchId, String dateOption, Map<String, BigDecimal> cdMap, Map<String, BigDecimal> lineMap) {
        List<ClaimLightWeightDTO> claimLightWeightDTOList = getClaimLightWeightDTOList(Date.valueOf("1990-01-01"), endDate, branchId, true, true);
        getContractualDifferenceForSalesSummary(startDate, endDate, dateOption, cdMap, lineMap, claimLightWeightDTOList);
    }

    public void getContractualDifferenceForSalesSummary(Date startDate, Date endDate, String dateOption, Map<String, BigDecimal> cdMap, Map<String, BigDecimal> lineMap, List<ClaimLightWeightDTO> claimLightWeightDTOList) {
        for (ClaimLightWeightDTO claimLightWeightDTO : claimLightWeightDTOList) {
            claimLightWeightDTO.setArDataDTO(processArDataDTOAndProcessAppliedPaymentAndAdjustments(claimLightWeightDTO, startDate, endDate, dateOption));
            claimLightWeightDTO.setDeviceType(claimLightWeightDTO.getArDataDTO().getDeviceType());
            if (claimLightWeightDTO.getArDataDTO() == null || claimLightWeightDTO.getDeviceType() == null) {
                continue;
            }
            BigDecimal cdTotal = cdMap.get(claimLightWeightDTO.getDeviceType());
            BigDecimal lineTotal = lineMap.get(claimLightWeightDTO.getDeviceType());
            cdTotal = cdTotal.add(claimLightWeightDTO.getArDataDTO().getContractualAdjustmentDiffTotals());
            lineTotal = lineTotal.add(claimLightWeightDTO.getArDataDTO().getAppliedAdjustmentsTotals());
            cdMap.put(claimLightWeightDTO.getDeviceType(), cdTotal);
            lineMap.put(claimLightWeightDTO.getDeviceType(), lineTotal);
        }
    }

    public Map<String, BigDecimal> populateDeviceTypeMap() {
        Map<String, BigDecimal> lineMap = new HashedMap<>();
        lineMap.put("orthotic", BigDecimal.ZERO);
        lineMap.put("prosthetic", BigDecimal.ZERO);
        lineMap.put("pedorthic", BigDecimal.ZERO);
        lineMap.put("mastectomy", BigDecimal.ZERO);
        lineMap.put("misc", BigDecimal.ZERO);
        lineMap.put("", BigDecimal.ZERO); // CRT customers don't need to set their device type categories, so could be null or empty
        lineMap.put(null, BigDecimal.ZERO);
        return lineMap;
    }

//    public Map<String, Map<String, BigDecimal>> processCdAndLaByPrescriptionAndAppliedPayments(List<String> adjustmentReasonCodes, Map<String, Map<String, BigDecimal>> cdLineMap, Long rx, List<AppliedPayment> apList) {
//        String deviceType = apList.get(0).getClaim().getPrescription().getDeviceType().getOrthoticOrProsthetic();
//        Long firstPatientInsuranceId = Long.parseLong(insuranceVerificationService.findFirstPatientInsuranceIdByPrescriptionId(rx).toString());
//                List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByInsuranceVerificationPatientInsuranceIdAndPrescriptionId(firstPatientInsuranceId, rx);
//                //Map<Long, InsuranceVerification_L_Code> rxIvlcMap = ivlcs.stream().collect(Collectors.toMap(x -> x.getPrescription_l_codeId(), x -> x, (x1, x2) -> x1));
//                Map<Long, InsuranceVerification_L_Code> rxIvlcMap = new HashMap<>();
//                for (InsuranceVerification_L_Code insuranceVerificationLCode : ivlcs) {
//                    rxIvlcMap.put(insuranceVerificationLCode.getPrescription_l_codeId(), insuranceVerificationLCode);
//                }
//        List<AppliedPayment_L_Code> aplcs = new ArrayList<>();
//        Map<Long, BigDecimal> aplcLineTotalMap = new HashMap<>();
//        for (AppliedPayment ap : apList) {
//            List<AppliedPayment_L_Code> tempAplcList = appliedPaymentLCodeRepository.findByAppliedPaymentId(ap.getId());
//            aplcs.addAll(tempAplcList);
//        }
//        //totalAplcList.addAll(aplcs);
//        return processCdAndLaByAppliedPaymentLCodes(cdLineMap, rx, deviceType, firstPatientInsuranceId, rxIvlcMap, aplcs, aplcLineTotalMap);
//    }

    public Map<String, Map<String, BigDecimal>> processCdAndLaByPrescriptionIdAppliedPaymentLCodes(Long rx, List<AppliedPayment_L_Code> aplcs) {
        Map<String, Map<String, BigDecimal>> cdLineMap = initializeCdLineMap();

        if (aplcs != null && aplcs.size() > 0) {
            String deviceType = aplcs.get(0).getAppliedPayment().getClaim().getPrescription().getDeviceType().getOrthoticOrProsthetic();
            DataModel dm = insuranceVerificationLCodeService.getIvlcsByPrescriptionId(rx);

            if (dm.getData().size() > 0) {
                Map<Long, InsuranceVerification_L_Code> rxIvlcMap = new HashMap<>();
                for (Object[] row : dm.getData()) {
                    InsuranceVerification_L_Code ivlc = new InsuranceVerification_L_Code();
                    ivlc.setId(dm.getLong("id", row));
                    ivlc.setAllowableFee(dm.getBigDecimal("allowable_fee", row));
                    ivlc.setBillingFee(dm.getBigDecimal("billing_fee", row));
                    ivlc.setCovered(dm.getBoolean("covered", row));
                    ivlc.setInsuranceVerificationId(dm.getLong("insurance_verification_id", row));
                    ivlc.setPrescriptionLCodeId(dm.getLong("prescription_l_code_id", row));
                    ivlc.setTotalAllowable(dm.getBigDecimal("total_allowable", row));
                    ivlc.setTotalCharge(dm.getBigDecimal("total_charge", row));
                    ivlc.setUseSalesTax(dm.getBoolean("use_sales_tax", row));
                    ivlc.setSalesTax(dm.getBigDecimal("sales_tax", row));
                    ivlc.setStartDate(dm.getSqlDate("start_date", row));
                    ivlc.setExpirationDate(dm.getSqlDate("expiration_date", row));
                    ivlc.setAuthNumber(dm.getString("auth_number", row));
                    rxIvlcMap.put(ivlc.getPrescriptionLCodeId(), ivlc);
                }

                Map<Long, BigDecimal> aplcLineTotalMap = new HashMap<>();
                cdLineMap = processCdAndLaByAppliedPaymentLCodes(cdLineMap, rx, deviceType, dm.getLong("patient_insurance_id", dm.getData().get(0)), rxIvlcMap, aplcs, aplcLineTotalMap);
            } else {
                Prescription prescription = prescriptionService.findOne(rx);
                String errorMessage = "Something is wrong with the insurance company setup for patient id = " + prescription.getPatientId() + ", and prescription id = " + rx;
                cdLineMap.get("error").put(errorMessage, BigDecimal.ZERO);
            }
        }
        return cdLineMap;
    }

    public Map<String, Map<String, BigDecimal>> initializeCdLineMap() {
        //CO Map is added to keep up with CO total and not just difference for other uses.
        Map<String, Map<String, BigDecimal>> cdLineMap = new HashMap<>();
        Map<String, BigDecimal> cdMap = populateDeviceTypeMap();
        Map<String, BigDecimal> lineMap = populateDeviceTypeMap();
        Map<String, BigDecimal> coMap = populateDeviceTypeMap();
        Map<String, BigDecimal> errorMap = new HashMap<>();
        cdLineMap.put("cd", cdMap);
        cdLineMap.put("line", lineMap);
        cdLineMap.put("co", coMap);
        cdLineMap.put("error", errorMap);
        return cdLineMap;
    }

    public Map<String, Map<String, BigDecimal>> processCdAndLaByAppliedPaymentLCodes(Map<String, Map<String, BigDecimal>> cdLineMap, Long rx, String deviceType, Long firstPatientInsuranceId, Map<Long, InsuranceVerification_L_Code> rxIvlcMap, List<AppliedPayment_L_Code> aplcs, Map<Long, BigDecimal> aplcLineTotalMap) {
        for (AppliedPayment_L_Code aplc : aplcs) {
            if (!rxIvlcMap.containsKey(aplc.getPrescriptionLCodeId())) {
                String errorMessage = "Missing ivlc for plc id = " + aplc.getPrescriptionLCodeId() + ", on patient insurance id = " + firstPatientInsuranceId + ", on aplc id = " + aplc.getId() + ", on prescription id = " + rx + ", for payment id = " + aplc.getAppliedPayment().getPaymentId()
                        + ", for applied payment id = " + aplc.getAppliedPayment().getId() + ", claim id = " + aplc.getAppliedPayment().getClaim().getId() + ", patient id = " + aplc.getAppliedPayment().getClaim().getPrescription().getPatientId()
//                    + ", aplc patient insurance id = " + aplc.getAppliedPayment().getClaim().getPrescription().getPatientInsurance().getInsuranceCompany().getId()
                        ;
                cdLineMap.get("error").put(errorMessage, BigDecimal.ZERO);
            }

            try {
                BigDecimal lineContractual = getAdjustmentTotal(aplc, false);
                BigDecimal tempLineRunningTotal = cdLineMap.get("line").get(deviceType);
                if (tempLineRunningTotal == null) {
                    tempLineRunningTotal = BigDecimal.ZERO;
                }
                cdLineMap.get("line").put(deviceType, tempLineRunningTotal.add(lineContractual));

                BigDecimal coContractual = getAdjustmentTotal(aplc, true);
                BigDecimal runningContractualTotal = (aplcLineTotalMap.containsKey(aplc.getPrescriptionLCodeId())) ? aplcLineTotalMap.get(aplc.getPrescriptionLCodeId()) : BigDecimal.ZERO;
                //Negate here to keep from adding non-negated totals which would be incorrect math.
                //coContractual = coContractual.negate();
                aplcLineTotalMap.put(aplc.getPrescriptionLCodeId(), runningContractualTotal.add(coContractual));
            } catch (Exception e) {
                cdLineMap.get("error").put(e.getMessage(), BigDecimal.ZERO);
            }

        }

        aplcLineTotalMap.forEach((plc, totalPlc) -> {
            if (totalPlc.compareTo(BigDecimal.ZERO) != 0) {
                InsuranceVerification_L_Code primaryIvlc = rxIvlcMap.get(plc);
                if (primaryIvlc == null || primaryIvlc.getTotalCharge() == null || primaryIvlc.getTotalAllowable() == null) {
                    String errorMessage = "Error: primaryIvlc should not be null for aplc.getPrescription_l_codeId() = " + plc + ", and prescriptionID = " + rx;
                    cdLineMap.get("error").put(errorMessage, BigDecimal.ZERO);
                } else {
                    BigDecimal obligatedContractual = primaryIvlc.getTotalCharge().subtract(primaryIvlc.getTotalAllowable()).negate();
                    totalPlc = totalPlc.negate();
                    //Total CO amounts Portion
                    BigDecimal tempTotalCO = cdLineMap.get("co").get(deviceType);
                    if (tempTotalCO == null) {
                        tempTotalCO = BigDecimal.ZERO;
                    }
                    cdLineMap.get("co").put(deviceType, tempTotalCO.add(totalPlc));
                    //Contractual Difference Portion
                    BigDecimal diff = totalPlc.subtract(obligatedContractual);
                    if (diff.compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal tempCODiffTotal = cdLineMap.get("cd").get(deviceType);
                        cdLineMap.get("cd").put(deviceType, tempCODiffTotal.add(diff));
                    }
                }
            }
        });

        return cdLineMap;
    }

    public BigDecimal getAdjustmentTotal(AppliedPayment_L_Code aplc, boolean contractualAdjustmentIfTrueElseLineAdjustmentNotContractual) throws Exception {
        BigDecimal adjustmentTotal = BigDecimal.ZERO;
        String payerType = aplc.getAppliedPayment().getPayment().getPayerType();
        List<EraAdjustmentReasonCode> eraAdjustmentReasonCodes = eraAdjustmentReasonCodeService.findAllEraReasonCodesByAppliedDate(aplc.getAppliedPayment().getAppliedDate());
        try {
            adjustmentTotal = calculateAdjustment(adjustmentTotal, aplc.getAdjustmentType1(), aplc.getAdjustmentAmount1(), payerType, eraAdjustmentReasonCodes, contractualAdjustmentIfTrueElseLineAdjustmentNotContractual);
            adjustmentTotal = calculateAdjustment(adjustmentTotal, aplc.getAdjustmentType2(), aplc.getAdjustmentAmount2(), payerType, eraAdjustmentReasonCodes, contractualAdjustmentIfTrueElseLineAdjustmentNotContractual);
            adjustmentTotal = calculateAdjustment(adjustmentTotal, aplc.getAdjustmentType3(), aplc.getAdjustmentAmount3(), payerType, eraAdjustmentReasonCodes, contractualAdjustmentIfTrueElseLineAdjustmentNotContractual);
            adjustmentTotal = calculateAdjustment(adjustmentTotal, aplc.getAdjustmentType4(), aplc.getAdjustmentAmount4(), payerType, eraAdjustmentReasonCodes, contractualAdjustmentIfTrueElseLineAdjustmentNotContractual);
            adjustmentTotal = calculateAdjustment(adjustmentTotal, aplc.getAdjustmentType5(), aplc.getAdjustmentAmount5(), payerType, eraAdjustmentReasonCodes, contractualAdjustmentIfTrueElseLineAdjustmentNotContractual);
        } catch (Exception e) {
            throw new Exception(e.getMessage() + " for applied payment L code id = " + aplc.getId());
        }

        return adjustmentTotal;
    }

    public BigDecimal calculateAdjustment(BigDecimal contractualAdjustmentTotal, String adjustmentType, BigDecimal adjustmentAmount, String payerType, List<EraAdjustmentReasonCode> adjustmentReasonCodes, boolean contractualIfTrue) throws Exception {
        boolean affectsBalance = false;
        for (EraAdjustmentReasonCode o : adjustmentReasonCodes) {
                if (!StringUtils.isBlank(adjustmentType) && !StringUtils.isBlank(o.getCode()) && o.getCode().equals(adjustmentType.split("-")[1])) {
                affectsBalance = o.getAffectsBalance();
                break;
            }
        }
        if ("insurance_company".equals(payerType) && !StringUtils.isBlank(adjustmentType) && !adjustmentType.contains("-")) {
            throw new Exception("Bad applied payment L code adjustment_type = " + adjustmentType);
        } else if (!StringUtils.isBlank(adjustmentType) && adjustmentAmount != null && affectsBalance) {
            if ((contractualIfTrue && "CO-45".equals(adjustmentType)) || (!contractualIfTrue && !"CO-45".equals(adjustmentType))) {
                contractualAdjustmentTotal = contractualAdjustmentTotal.add(adjustmentAmount);
            }
        }
        return contractualAdjustmentTotal;
    }

    public void processArDataDTOSummary(BillingSummaryDTO salesSummary, List<ClaimsSummaryDTO> claimsSummaryDTOList, String deviceType) {
        for (ClaimsSummaryDTO claimsSummaryDTO : claimsSummaryDTOList) {
            if (deviceType.equals(claimsSummaryDTO.getClaimsSummarySlimClaimDataDTO().getOrthoticOrProsthetic())) {
                salesSummary.setCharges(salesSummary.getCharges().add(claimsSummaryDTO.getArDataDTO().getBillable()));
                salesSummary.setInitialUnallowed(salesSummary.getInitialUnallowed().add(claimsSummaryDTO.getArDataDTO().getContractualAdjustment()));
                salesSummary.setTotalAllowable(salesSummary.getTotalAllowable().add(claimsSummaryDTO.getArDataDTO().getAllowable().add(claimsSummaryDTO.getArDataDTO().getContractualAdjustmentDiffTotals())));
                salesSummary.setContractualAdjustmentsRange(salesSummary.getContractualAdjustmentsRange().add(claimsSummaryDTO.getArDataDTO().getContractualAdjustmentsRange()));

                switch (claimsSummaryDTO.getCarrierType()) {
                    case "primary":
                        salesSummary.setPrimaryClaimsFiled(salesSummary.getPrimaryClaimsFiled() + 1);
                        break;
                    case "secondary":
                        salesSummary.setSecondaryClaimsFiled(salesSummary.getSecondaryClaimsFiled() + 1);
                        break;
                    case "tertiary":
                        salesSummary.setTertiaryClaimsFiled(salesSummary.getTertiaryClaimsFiled() + 1);
                        break;
                    case "quaternary":
                        salesSummary.setQuaternaryClaimsFiled(salesSummary.getQuaternaryClaimsFiled() + 1);
                        break;
                    case "quinary":
                        salesSummary.setQuinaryClaimsFiled(salesSummary.getQuinaryClaimsFiled() + 1);
                        break;
                    case "senary":
                        salesSummary.setSenaryClaimsFiled(salesSummary.getSenaryClaimsFiled() + 1);
                        break;
                    case "septenary":
                        salesSummary.setSeptenaryClaimsFiled(salesSummary.getSeptenaryClaimsFiled() + 1);
                        break;
                    case "octonary":
                        salesSummary.setOctonaryClaimsFiled(salesSummary.getOctonaryClaimsFiled() + 1);
                        break;
                    case "nonary":
                        salesSummary.setNonaryClaimsFiled(salesSummary.getNonaryClaimsFiled() + 1);
                        break;
                    case "denary":
                        salesSummary.setDenaryClaimsFiled(salesSummary.getDenaryClaimsFiled() + 1);
                        break;
                    default:
                        //System.out.println("carrier type = "+claimsSummaryDTO.getCarrierType());
                        salesSummary.setOtherClaimsFiled(salesSummary.getOtherClaimsFiled() + 1);
                        break;
                }
            }
        }
    }

    /**
     * The salesDetail generates a list of SalesDetailDTOs categorized by the prescription device type
     * (orthotic or prosthetic). The SalesDetailDTOs contain a list of claims and associated Insurance Verification
     * L-Codes as well as the total billable, allowable, and contractual values for the claim.
     * <p>
     * NOTE: Any change to this method MUST be addressed in the Sales Summary Report and vice versa.
     *
     * @param startDate
     * @param endDate
     * @param branchId
     * @return
     * <AUTHOR> Browder
     */
    public Map<String, SalesDetailDTO> salesDetail(java.sql.Date startDate, Date endDate, Long branchId, String usePatientBranch) {

        Map<String, SalesDetailDTO> salesDetailMap = new HashMap<>();
        SalesDetailDTO orthoticSalesDetailDto = new SalesDetailDTO();
        SalesDetailDTO prostheticSalesDetailDto = new SalesDetailDTO();
        SalesDetailDTO pedorthicSalesDetailDto = new SalesDetailDTO();
        SalesDetailDTO mastectomySalesDetailDto = new SalesDetailDTO();
        SalesDetailDTO miscSalesDetailDto = new SalesDetailDTO();
        SalesDetailDTO totalSalesDetailDto = new SalesDetailDTO();

        List<ClaimsSummaryDTO> claimsSummaryDTOList = getClaimsSummaryDTOListForSalesReportsFilterByClaimSubmissionDateTrue(startDate, endDate, branchId, usePatientBranch);
        for (ClaimsSummaryDTO claimsSummaryDTO : claimsSummaryDTOList) {
            if (claimsSummaryDTO.getClaimsSummarySlimClaimDataDTO().getOrthoticOrProsthetic().equals("orthotic")) {
                processSalesDetailDto(orthoticSalesDetailDto, claimsSummaryDTO, claimsSummaryDTO.getIvlcTotal());
            } else if (claimsSummaryDTO.getClaimsSummarySlimClaimDataDTO().getOrthoticOrProsthetic().equals("prosthetic")) {
                processSalesDetailDto(prostheticSalesDetailDto, claimsSummaryDTO, claimsSummaryDTO.getIvlcTotal());
            } else if (claimsSummaryDTO.getClaimsSummarySlimClaimDataDTO().getOrthoticOrProsthetic().equals("pedorthic")) {
                processSalesDetailDto(pedorthicSalesDetailDto, claimsSummaryDTO, claimsSummaryDTO.getIvlcTotal());
            } else if (claimsSummaryDTO.getClaimsSummarySlimClaimDataDTO().getOrthoticOrProsthetic().equals("mastectomy")) {
                processSalesDetailDto(mastectomySalesDetailDto, claimsSummaryDTO, claimsSummaryDTO.getIvlcTotal());
            } else if (claimsSummaryDTO.getClaimsSummarySlimClaimDataDTO().getOrthoticOrProsthetic().equals("misc")) {
                processSalesDetailDto(miscSalesDetailDto, claimsSummaryDTO, claimsSummaryDTO.getIvlcTotal());
            }
            processSalesDetailDto(totalSalesDetailDto, claimsSummaryDTO, claimsSummaryDTO.getIvlcTotal());
        }
        salesDetailMap.put("orthotic", orthoticSalesDetailDto);
        salesDetailMap.put("prosthetic", prostheticSalesDetailDto);
        salesDetailMap.put("pedorthic", pedorthicSalesDetailDto);
        salesDetailMap.put("mastectomy", mastectomySalesDetailDto);
        salesDetailMap.put("misc", miscSalesDetailDto);
        salesDetailMap.put("total", totalSalesDetailDto);

        return salesDetailMap;
    }

    public List<ClaimsSummaryDTO> getClaimsSummaryDTOListForSalesReportsFilterByClaimSubmissionDateTrue(Date startDate, Date endDate, Long branchId, String usePatientBranch) {
        List<ClaimsSummaryDTO> claimsSummaryDTOList = new ArrayList<>();

        List<ClaimInsuranceVerificationLCodesDTO> claimInsuranceVerificationLCodesDTOList = getClaimInsuranceVerificationLCodesDTOList(startDate, endDate, branchId, true, true, "true".equals(usePatientBranch));
        for (ClaimInsuranceVerificationLCodesDTO claimInsuranceVerificationLCodesDTO : claimInsuranceVerificationLCodesDTOList) {
            if (claimInsuranceVerificationLCodesDTO.getHasFirstClaimSubmission()) {
                User treatingPractitioner = userService.getUserById(claimInsuranceVerificationLCodesDTO.getClaim().getPrescription().getTreatingPractitionerId());
                claimInsuranceVerificationLCodesDTO.getClaim().getPrescription().setTreatingPractitioner(treatingPractitioner);
                ClaimsSummaryDTO dto = new ClaimsSummaryDTO();
                claimsSummaryDTOList.add(dto);
                dto.setCarrierType(claimInsuranceVerificationLCodesDTO.getCarrierType());
                ClaimsSummarySlimClaimDataDTO slimClaimDataDTO = claimService.populateClaimsSummarySlimClaimDataDTO(claimInsuranceVerificationLCodesDTO.getClaim());
                dto.setClaimsSummarySlimClaimDataDTO(slimClaimDataDTO);
                dto.setSubmissionDate(claimInsuranceVerificationLCodesDTO.getClaimSubmissionDate());

                ArDataDTO claimAR = new ArDataDTO();
                ArIvlcPeriodTotals ivlcTotal = new ArIvlcPeriodTotals();
                ivlcTotal.setBillable(claimInsuranceVerificationLCodesDTO.getBillable());
                ivlcTotal.setAllowable(claimInsuranceVerificationLCodesDTO.getAllowable());
                ivlcTotal.setWriteOff(claimInsuranceVerificationLCodesDTO.getContractualAdjustment());
                ivlcTotal.setSalesTax(claimInsuranceVerificationLCodesDTO.getSalesTax());
                claimAR.setId(claimInsuranceVerificationLCodesDTO.getClaim().getId());
                claimAR.setBillable(ivlcTotal.getBillable());
                claimAR.setAllowable(ivlcTotal.getAllowable());
                claimAR.setContractualAdjustment(ivlcTotal.getWriteOff());
                claimAR.setSalesTax(ivlcTotal.getSalesTax());

                List<IvlcDTO> dtos = new ArrayList<>();
                List<InsuranceVerification_L_Code> claimIvlcs = insuranceVerificationLCodeService.getIvlcsByClaimId(claimInsuranceVerificationLCodesDTO.getClaim().getId());
                for (InsuranceVerification_L_Code ivlc : claimIvlcs) {
                    IvlcDTO ivlcdto = new IvlcDTO(ivlc);
                    dtos.add(ivlcdto);
                }
                dto.setIvlcs(dtos);

                dto.setArDataDTO(claimAR);
                dto.setIvlcTotal(ivlcTotal);
                //sb.append("getClaimsSummaryDTOList,"+claim.getId() + ","+cs.getSubmissionDate()+"\n");
            }
        }
        //System.out.println(sb);
//        System.out.println("billable  = "+billable);
//        System.out.println("allowable = "+allowable);

        return claimsSummaryDTOList;
    }

    public List<ClaimInsuranceVerificationLCodesDTO> getClaimInsuranceVerificationLCodesDTOList(Date startDate, Date endDate, Long branchId, boolean useSubmissionDate, boolean requiresClaimSubmission, boolean usePatientBranch) {
        List<ClaimInsuranceVerificationLCodesDTO> ClaimInsuranceVerificationLCodesDTOList = new ArrayList<>();
//        System.out.println("claimRepository.findMassIvlcTotalsByClaimIdsQuery = \n"
//                .concat(claimRepository.findMassIvlcTotalsByClaimIdsQuery
//                        .replaceAll(":branchId", branchId == null ? "null" : branchId.toString())
//                        .replaceAll(":startDate", "'" + startDate + "'")
//                        .replaceAll(":endDate", "'" + endDate + "'")
//                        .replaceAll(":deviceType", "'%'")
//                        .replaceAll(":isSubmission", useSubmissionDate ? "1" : "0")
//                        .replaceAll(":usePatientBranch", "0")));
        List<Object[]> claimBillableAllowableTotalsLists = claimRepository.findMassIvlcTotalsByClaimIds(branchId, startDate, endDate, "%", useSubmissionDate, usePatientBranch);
        Set<Long> bASet = new HashSet<>();
        StringBuilder sb = new StringBuilder();
        BigDecimal totalBillable = BigDecimal.ZERO;
        BigDecimal totalAllowable = BigDecimal.ZERO;
        BigDecimal totalContractual = BigDecimal.ZERO;

        Map<Long, Object[]> bAMap = new HashMap<>();
        for (Object[] objects : claimBillableAllowableTotalsLists) {
            bAMap.put(((Long) objects[0]), new Object[]{objects[1], objects[2], objects[3], objects[4], objects[5], objects[6]});
        }

//        System.out.println("findByPrescriptionClaimBranchIdAndDateOfServiceBetweenQuery = \n" +
//                        claimRepository.findByPrescriptionClaimBranchIdAndDateOfServiceBetweenQuery
//                                .replaceAll(":branchId", branchId == null ? "null" : branchId.toString())
//                                .replaceAll(":startDate", "'" + startDate + "'")
//                                .replaceAll(":endDate", "'" + endDate + "'")
//                                .replaceAll(":deviceType", "'%'")
//                                .replaceAll(":isSubmission", useSubmissionDate ? "1" : "0")
//                                .replaceAll(":requiresClaimSubmission", requiresClaimSubmission ? "1" : "0")
//        );
        // REPLACE
        List<Claim> claims = claimRepository.findByPrescriptionClaimBranchIdAndDateOfServiceBetween(branchId, startDate, endDate, useSubmissionDate, requiresClaimSubmission);
        //StringBuilder sb = new StringBuilder("type,claimId,claimSubmissionDate\n");
        for (Claim claim : claims) {
            ClaimInsuranceVerificationLCodesDTO claimInsuranceVerificationLCodesDTO = new ClaimInsuranceVerificationLCodesDTO();
            List<Object[]> properSalesNumbers = insuranceVerificationLCodeService.getProperSalesNumbersByPrescriptionId(claim.getPrescriptionId());
            // This report only looks at primary claims in order to have accurate totals
            if (bAMap.containsKey(claim.getId()) && bAMap.get(claim.getId())[5] != null) {
                ClaimInsuranceVerificationLCodesDTOList.add(claimInsuranceVerificationLCodesDTO);
                claimInsuranceVerificationLCodesDTO.setBillable((BigDecimal) bAMap.get(claim.getId())[0]);
                claimInsuranceVerificationLCodesDTO.setAllowable((BigDecimal) bAMap.get(claim.getId())[1]);
                claimInsuranceVerificationLCodesDTO.setContractualAdjustment((BigDecimal) bAMap.get(claim.getId())[2]);
                claimInsuranceVerificationLCodesDTO.setSalesTax((BigDecimal) bAMap.get(claim.getId())[3]);
                claimInsuranceVerificationLCodesDTO.setCarrierType(bAMap.get(claim.getId())[4].toString());
                claimInsuranceVerificationLCodesDTO.setClaimSubmissionDate((Date) bAMap.get(claim.getId())[5]);
                claimInsuranceVerificationLCodesDTO.setClaim(claim);
                claimInsuranceVerificationLCodesDTO.setHasFirstClaimSubmission(true);
                //sb.append("getClaimsSummaryDTOList,"+claim.getId() + ","+cs.getSubmissionDate()+"\n");
            } else if (bAMap.containsKey(claim.getId()) && bAMap.get(claim.getId())[5] == null && requiresClaimSubmission) {
                ClaimInsuranceVerificationLCodesDTOList.add(claimInsuranceVerificationLCodesDTO);
                claimInsuranceVerificationLCodesDTO.setBillable((BigDecimal) bAMap.get(claim.getId())[0]);
                claimInsuranceVerificationLCodesDTO.setAllowable((BigDecimal) bAMap.get(claim.getId())[1]);
                claimInsuranceVerificationLCodesDTO.setContractualAdjustment((BigDecimal) bAMap.get(claim.getId())[2]);
                claimInsuranceVerificationLCodesDTO.setSalesTax((BigDecimal) bAMap.get(claim.getId())[3]);
                claimInsuranceVerificationLCodesDTO.setCarrierType(bAMap.get(claim.getId())[4].toString());
                claimInsuranceVerificationLCodesDTO.setClaimSubmissionDate(null);
                claimInsuranceVerificationLCodesDTO.setClaim(claim);
                claimInsuranceVerificationLCodesDTO.setHasFirstClaimSubmission(false);
                //sb.append("getClaimsSummaryDTOList,"+claim.getId() + ","+cs.getSubmissionDate()+"\n");
            } else {
                ClaimInsuranceVerificationLCodesDTOList.add(claimInsuranceVerificationLCodesDTO);
                claimInsuranceVerificationLCodesDTO.setBillable(bAMap.get(claim.getId()) == null ? BigDecimal.ZERO : (BigDecimal) bAMap.get(claim.getId())[0]);
                claimInsuranceVerificationLCodesDTO.setAllowable(bAMap.get(claim.getId()) == null ? BigDecimal.ZERO : (BigDecimal) bAMap.get(claim.getId())[1]);
                claimInsuranceVerificationLCodesDTO.setCarrierType(null);
                claimInsuranceVerificationLCodesDTO.setClaim(claim);
                claimInsuranceVerificationLCodesDTO.setHasFirstClaimSubmission(false);
            }
            claimInsuranceVerificationLCodesDTO.setBillable(properSalesNumbers != null && !properSalesNumbers.isEmpty() ? (BigDecimal) properSalesNumbers.get(0)[2] : BigDecimal.ZERO);
            claimInsuranceVerificationLCodesDTO.setAllowable(properSalesNumbers != null && !properSalesNumbers.isEmpty() ? (BigDecimal) properSalesNumbers.get(0)[3] : BigDecimal.ZERO);
            claimInsuranceVerificationLCodesDTO.setContractualAdjustment(properSalesNumbers != null && !properSalesNumbers.isEmpty() ? (BigDecimal) properSalesNumbers.get(0)[4] : BigDecimal.ZERO);
            claimInsuranceVerificationLCodesDTO.setSalesTax(properSalesNumbers != null && !properSalesNumbers.isEmpty() ? (BigDecimal) properSalesNumbers.get(0)[5] : BigDecimal.ZERO);
        }
        //System.out.println(sb);
        return ClaimInsuranceVerificationLCodesDTOList;
    }

    public void processSalesDetailDto(SalesDetailDTO salesDetailDto, ClaimsSummaryDTO dto, ArIvlcPeriodTotals ivlcTotal) {
        List<ClaimsSummaryDTO> tempOrthoticClaimsDTOList = salesDetailDto.getClaimsSummaryDTOList();
        tempOrthoticClaimsDTOList.add(dto);
        salesDetailDto.setClaimsSummaryDTOList(tempOrthoticClaimsDTOList);

        salesDetailDto.setTotalContractual(salesDetailDto.getTotalContractual().add(ivlcTotal.getWriteOff()));
        salesDetailDto.setTotalBillable(salesDetailDto.getTotalBillable().add(ivlcTotal.getBillable()));
        salesDetailDto.setTotalAllowable(salesDetailDto.getTotalAllowable().add(ivlcTotal.getAllowable()));
    }

    public BillingSummaryDTO billingSummary(java.sql.Date startDate, Date endDate, Long branchId, String deviceType) {
        BillingSummaryDTO dto = new BillingSummaryDTO();
        Integer claimsFiled = 0;
        Integer primaryClaimsFiled = 0;
        Integer secondaryClaimsFiled = 0;
        Integer tertiaryClaimsFiled = 0;
        Integer quaternaryClaimsFiled = 0;
        Integer quinaryClaimsFiled = 0;
        Integer senaryClaimsFiled = 0;
        Integer septenaryClaimsFiled = 0;
        Integer octonaryClaimsFiled = 0;
        Integer nonaryClaimsFiled = 0;
        Integer denaryClaimsFiled = 0;
        Integer otherClaimsFiled = 0;
        List<Payment> payments;
        List<Payment> unappliedPayments = paymentService.populateUnappliedPayments(startDate, endDate, branchId, deviceType);

        for (Payment p : unappliedPayments) {
            if (p.getPaymentType() != null) {
                switch (p.getPaymentType()) {
                    case "patient_check":
                        dto.setPaymentsUnappliedPatientChecks(dto.getPaymentsUnappliedPatientChecks().add(p.getAmount()));
                        break;
                    case "cash":
                        dto.setPaymentsUnappliedPatientCash(dto.getPaymentsAppliedPatientCash().add(p.getAmount()));
                        break;
                    case "patient_credit_card":
                        dto.setPaymentsUnappliedPatientCreditCard(dto.getPaymentsUnappliedPatientCreditCard().add(p.getAmount()));
                        break;
                    case "insurance_payment_check":
                    case "insurance_payment_credit_card":
                    case "insurance_payment_electronic":
                        dto.setTotalUnappliedInsurance(dto.getTotalUnappliedInsurance().add(p.getAmount()));
                        break;
                }
            }
        }

        List<ClaimSubmission> allClaimSubmissions = claimSubmissionService.getClaimSubmissions(startDate, endDate, branchId, deviceType);
        List<Long> claimIdList = new ArrayList<>();
        List<ClaimSubmission> claimSubmissions = new ArrayList<>();
        for (ClaimSubmission cs : allClaimSubmissions) {
            if (!claimIdList.contains(cs.getClaimId())) {
                claimIdList.add(cs.getClaimId());
                claimSubmissions.add(cs);
            }
        }

        for (ClaimSubmission cs : claimSubmissions) {
            if (cs.getClaim() != null && cs.getClaim().getPrescription().getActive()) {
                SystemSetting useSingleClaim = systemSettingService.findBySectionAndField("claim", "use_single_claim");
                Long patientInsuranceId = "Y".equals(useSingleClaim.getValue()) ? cs.getClaim().getResponsiblePatientInsuranceId() : cs.getClaim().getPatientInsuranceId();
                if (patientInsuranceId != null) {
                    InsuranceVerification iv =
                            insuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(
                                    patientInsuranceId, cs.getClaim().getPrescriptionId());
                    if (iv != null) {
                        switch (iv.getCarrierType()) {
                            case "primary":
                                primaryClaimsFiled++;
                                dto.setCharges(dto.getCharges().add(cs.getClaim().getTotalClaimAmount()));
                                break;
                            case "secondary":
                                secondaryClaimsFiled++;
                                break;
                            case "tertiary":
                                tertiaryClaimsFiled++;
                                break;
                            case "quaternary":
                                quaternaryClaimsFiled++;
                                break;
                            case "quinary":
                                quinaryClaimsFiled++;
                                break;
                            case "senary":
                                senaryClaimsFiled++;
                                break;
                            case "septenary":
                                septenaryClaimsFiled++;
                                break;
                            case "octonary":
                                octonaryClaimsFiled++;
                                break;
                            case "nonary":
                                nonaryClaimsFiled++;
                                break;
                            case "denary":
                                denaryClaimsFiled++;
                                break;
                            case "other":
                                otherClaimsFiled++;
                                break;
                        }
                    }
                }
                claimsFiled++;
                dto.setTotalPatientBalance(dto.getTotalPatientBalance().add(cs.getClaim().getTotalPtResponsibilityBalance()));
                dto.setTotalInsuranceBalance(dto.getTotalInsuranceBalance().add(cs.getClaim().getTotalClaimBalance()));

                List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdWithCalculatedFirstCarrier(cs.getClaim().getPrescriptionId());
                //List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService.findByPrescriptionIdAndCarrierType(cs.getClaim().getPrescriptionId(), "primary");
                for (InsuranceVerification_L_Code ivlc : ivlcs) {
                    dto.setTotalAllowable(dto.getTotalAllowable().add(ivlc.getTotalAllowable()));
                }
            }

            payments = paymentService.populateClaimAppliedPayments(cs);
            dto = paymentAdjustmentAndAppliedAmount(dto, payments);
        }
        BigDecimal arPatientBalance;
        BigDecimal arInsuranceBalance;

        arPatientBalance = claimRepository.getSumOfPatientBalancesFromAllClaimsWithActivePrescriptionByBranch(branchId);
        arInsuranceBalance = claimRepository.getSumOfInsuranceBalancesFromAllClaimsWithActivePrescriptionByBranch(branchId);

        dto.setArPatientBalance(arPatientBalance == null ? BigDecimal.ZERO : arPatientBalance);
        dto.setArInsuranceBalance(arInsuranceBalance == null ? BigDecimal.ZERO : arInsuranceBalance);

        dto.setAr(dto.getArPatientBalance().add(dto.getArInsuranceBalance()));
        dto.setTotalPatientCreditCard(dto.getPaymentsUnappliedPatientCreditCard().add(dto.getPaymentsAppliedPatientCreditCard()));
        dto.setTotalPatientCash(dto.getPaymentsAppliedPatientCash().add(dto.getPaymentsUnappliedPatientCash()));
        dto.setTotalPatientChecks(dto.getPaymentsAppliedPatientChecks().add(dto.getPaymentsUnappliedPatientChecks()));
        dto.setTotalAppliedPatient(dto.getPaymentsAppliedPatientChecks().add(dto.getPaymentsAppliedPatientCash()).add(dto.getPaymentsAppliedPatientCreditCard()));
        dto.setTotalUnappliedPatient(dto.getPaymentsUnappliedPatientChecks().add(dto.getPaymentsUnappliedPatientCash()).add(dto.getPaymentsUnappliedPatientCreditCard()));
        dto.setTotalInsurancePayment(dto.getTotalAppliedInsurance().add(dto.getTotalUnappliedInsurance()));
        dto.setTotalPatientPayment(dto.getTotalPatientChecks().add(dto.getTotalPatientCash()).add(dto.getTotalPatientCreditCard()));
        dto.setPaymentsTotal(dto.getTotalPatientPayment().add(dto.getTotalInsurancePayment()));
        dto.setClaimsFiled(claimsFiled);
        dto.setPrimaryClaimsFiled(primaryClaimsFiled);
        dto.setSecondaryClaimsFiled(secondaryClaimsFiled);
        dto.setTertiaryClaimsFiled(tertiaryClaimsFiled);
        dto.setQuaternaryClaimsFiled(quaternaryClaimsFiled);
        dto.setQuinaryClaimsFiled(quinaryClaimsFiled);
        dto.setSenaryClaimsFiled(senaryClaimsFiled);
        dto.setSeptenaryClaimsFiled(septenaryClaimsFiled);
        dto.setOctonaryClaimsFiled(octonaryClaimsFiled);
        dto.setNonaryClaimsFiled(nonaryClaimsFiled);
        dto.setDenaryClaimsFiled(denaryClaimsFiled);
        dto.setOtherClaimsFiled(otherClaimsFiled);
        dto.setInitialUnallowed(dto.getCharges().subtract(dto.getTotalAllowable()));

        return dto;
    }

    public Map<String, List<ClaimBillingsByDeliveryDateDTO>> billingsByDateOfService(Long branchId, Date startDate, Date endDate, String usePatientBranch) {
        Map<String, List<ClaimBillingsByDeliveryDateDTO>> results = new HashMap<>();
        List<ClaimBillingsByDeliveryDateDTO> withoutFirstClaimSubmissionDate = new ArrayList<>();
        List<ClaimBillingsByDeliveryDateDTO> withFirstClaimSubmissionDate = new ArrayList<>();
        //BigDecimal totalAllowable = BigDecimal.ZERO;
        //BigDecimal totalBillable = BigDecimal.ZERO;
        List<ClaimInsuranceVerificationLCodesDTO> claimInsuranceVerificationLCodesDTOList = getClaimInsuranceVerificationLCodesDTOList(startDate, endDate, branchId, false, false, "true".equals(usePatientBranch));
        for (ClaimInsuranceVerificationLCodesDTO claimInsuranceVerificationLCodesDTO : claimInsuranceVerificationLCodesDTOList) {
            java.util.Date dos = claimInsuranceVerificationLCodesDTO.getClaim().getDateOfService();
            if (!dos.before(startDate) && !dos.after(endDate)) {
                ClaimBillingsByDeliveryDateDTO claimBillingsByDeliveryDateDTO = new ClaimBillingsByDeliveryDateDTO();
                claimBillingsByDeliveryDateDTO.setClaim(claimInsuranceVerificationLCodesDTO.getClaim());
                claimBillingsByDeliveryDateDTO.setTotalAllowable(claimInsuranceVerificationLCodesDTO.getAllowable());
                claimBillingsByDeliveryDateDTO.setTotalBillable(claimInsuranceVerificationLCodesDTO.getBillable());

                java.sql.Date fcsd = claimInsuranceVerificationLCodesDTO.getClaimSubmissionDate();

                claimBillingsByDeliveryDateDTO.setAppliedPaymentDate(appliedPaymentService.getLastAppliedDate(claimInsuranceVerificationLCodesDTO.getClaim().getId()));
                if (fcsd != null) {
                    claimBillingsByDeliveryDateDTO.setFirstClaimSubmissionDate(fcsd);
                    claimBillingsByDeliveryDateDTO.setHasClaimSubmissionDate(true);
                    withFirstClaimSubmissionDate.add(claimBillingsByDeliveryDateDTO);
                    //sb.append("billingsByDateOfService,"+claim.getId() + ","+claimSubmission.getSubmissionDate()+"\n");
                } else {
                    claimBillingsByDeliveryDateDTO.setHasClaimSubmissionDate(false);
                    withoutFirstClaimSubmissionDate.add(claimBillingsByDeliveryDateDTO);
                    //System.out.println("withoutFirstClaimSubmissionDate claim id = " + claimBillingsByDeliveryDateDTO.getClaim().getId());
                }
            }
        }
        //System.out.println(sb);
        results.put("withoutFirstClaimSubmissionDate", withoutFirstClaimSubmissionDate);
        results.put("withFirstClaimSubmissionDate", withFirstClaimSubmissionDate);
        return results;
    }

    public OutstandingBalanceReportDTO getOutstandingBalanceReportDTO(Long branchId, boolean useSubmissionDate, boolean hideNegativeBalance, boolean useBillable) throws CloneNotSupportedException {
        Date startDate = Date.valueOf("1990-01-01");
        Date endDate = Date.valueOf(DateUtil.getLocalDateTimeWithUserTimezoneId().toLocalDate());
        return getOutstandingBalanceReportDTO(startDate, endDate, branchId, useSubmissionDate, hideNegativeBalance, useBillable);
    }

    public OutstandingBalanceReportDTO getOutstandingBalanceReportDTO(Date startDate, Date endDate, Long branchId, boolean useSubmissionDate, boolean hideNegativeBalance, boolean useBillable) throws CloneNotSupportedException {

        //NEED to check requiresClaimSubmission value
        List<ClaimLightWeightDTO> claimLightWeightDTOList = getClaimLightWeightDTOList(startDate, endDate, branchId, useSubmissionDate, true);
        return getOutstandingBalanceReportDTO(endDate, useSubmissionDate, hideNegativeBalance, claimLightWeightDTOList, useBillable);
    }

    public OutstandingBalanceReportDTO getOutstandingBalanceReportDTO(Date endDate, boolean useSubmissionDate, boolean hideNegativeBalance, List<ClaimLightWeightDTO> claimLightWeightDTOList, boolean useBillable) {
        return new OutstandingBalanceReportDTO();

//        OutstandingBalanceReportDTO result = new OutstandingBalanceReportDTO();
//        Map<Long, List<ClaimLightWeightDTO>> claimLightWeightDTOHashMap = new HashMap<>();
//        StringBuilder sbTime = new StringBuilder();
//        try {
//            claimLightWeightDTOHashMap.put(0L, new ArrayList<>()); //this is done for a spot on Patient Balance you do not have to use this data point.
//            for (ClaimLightWeightDTO claimLightWeightDTO : claimLightWeightDTOList) {
//                ArDataDTO dataDTO = processArDataDTOAndProcessAppliedPaymentAndAdjustments(claimLightWeightDTO, Date.valueOf("1990-01-01"), endDate, "all");
//                dataDTO.setAccountsReceivables(calculateAccountReceivables(dataDTO, true));
//                if(!useBillable && dataDTO.getAppliedPaymentsTotals().compareTo(BigDecimal.ZERO) == 0 && dataDTO.getAppliedAdjustmentsTotals().compareTo(BigDecimal.ZERO) == 0
//                        && dataDTO.getArLevelAdjustments().compareTo(BigDecimal.ZERO) == 0 && dataDTO.getContractualAdjustmentAppliedTotals().compareTo(BigDecimal.ZERO) == 0 ){
//                    dataDTO.setAccountsReceivables(dataDTO.getAllowable());
//                }
//                claimLightWeightDTO.setArDataDTO(dataDTO);
//                if (claimLightWeightDTO.getArDataDTO().getErrorMessages().size() > 0) {
//                    result.getErrorMessages().addAll(claimLightWeightDTO.getArDataDTO().getErrorMessages());
//                }
//                //System.out.println("outstanding balance: claimId = " + claimLightWeightDTO.getClaimId() + ",  claimLightWeightDTO.getArDataDTO().getAccountsReceivables() = " + claimLightWeightDTO.getArDataDTO().getAccountsReceivables());
//                BigDecimal billable = claimLightWeightDTO.getArDataDTO().getBillable();
//                BigDecimal paymentTotal = claimLightWeightDTO.getArDataDTO().getAppliedPaymentsTotals();
//                BigDecimal adjustmentTotal = claimLightWeightDTO.getArDataDTO().getAppliedAdjustmentsTotals();
//                boolean paymentsEqualBillable = billable.compareTo(BigDecimal.ZERO) != 0 && paymentTotal.compareTo(BigDecimal.ZERO) != 0 && paymentTotal.compareTo(billable) == 0;
//                boolean adjustmentsEqualBillable = billable.compareTo(BigDecimal.ZERO) != 0 && adjustmentTotal.compareTo(BigDecimal.ZERO) != 0 && adjustmentTotal.compareTo(billable) == 0;
//
//                if (paymentsEqualBillable || adjustmentsEqualBillable) {
////                    System.out.println(new ObjectMapper().writeValueAsString(claimLightWeightDTO));
////                    System.out.println("Billable   : " + billable);
////                    System.out.println("Payments   : " + paymentTotal);
////                    System.out.println("Adjustments: " + adjustmentTotal);
//                    continue;
//                }
//                if (claimLightWeightDTO.getArDataDTO().getAccountsReceivables().compareTo(BigDecimal.ZERO) != 0) {
//                    //Build Light Weight claim dto if AR not Zero
//                    ClaimLightWeightDTO light = claimLightWeightDTO;
//
//                    ClaimLightWeightDTO patient = (ClaimLightWeightDTO) light.clone();
//                    if (light.getPatientBalance() != null && light.getPatientBalance().compareTo(BigDecimal.ZERO) != 0 &&
//                        ((useSubmissionDate && light.getSubmissionDate() != null) || (!useSubmissionDate && light.getDateOfService() != null))) {
//                        if (light.getClaimBalance().compareTo(BigDecimal.ZERO) == 0) {
//                            patient.setInsuranceId(0L);
//                            patient.setClaimBalance(light.getArDataDTO().getAccountsReceivables());
//                            List<ClaimLightWeightDTO> temp = claimLightWeightDTOHashMap.get(0L);
//                            temp.add(patient);
//                            temp.sort(Comparator.comparing(ClaimLightWeightDTO::getAging, Comparator.nullsFirst(Comparator.naturalOrder())));
//                            claimLightWeightDTOHashMap.put(0L, temp);
//                        } else {
//                            if (light.getClaimBalance().subtract(light.getArDataDTO().getAccountsReceivables()).compareTo(BigDecimal.ZERO) >= 0) {
//                                light.setClaimBalance(light.getArDataDTO().getAccountsReceivables());
//                                patient.setClaimBalance(BigDecimal.ZERO);
//                            } else {
//                                BigDecimal diff = light.getArDataDTO().getAccountsReceivables().subtract(light.getClaimBalance());
//                                patient.setClaimBalance(diff);
//                            }
//                            patient.setInsuranceId(0L);
//                            if (patient.getClaimBalance().compareTo(BigDecimal.ZERO) != 0) {
//                                List<ClaimLightWeightDTO> temp = claimLightWeightDTOHashMap.get(0L);
//                                temp.add(patient);
//                                temp.sort(Comparator.comparing(ClaimLightWeightDTO::getAging, Comparator.nullsFirst(Comparator.naturalOrder())));
//                                claimLightWeightDTOHashMap.put(0L, temp);
//                            }
//                        }
//                    } else {
//                        light.setClaimBalance(light.getArDataDTO().getAccountsReceivables());
//                        List<Claim> claims = claimRepository.findByPrescriptionId(light.getPrescriptionId());
//                        claims = claims.stream().filter(c -> !c.getId().equals(light.getClaimId())).collect(Collectors.toList());
//                        for (Claim c1 : claims) {
//                            InsuranceVerification iv = insuranceVerificationService.findByPatientInsuranceIdAndPrescriptionId(c1.getResponsiblePatientInsuranceId(), c1.getPrescriptionId());
//                            if(iv != null){
//                                if (c1.getTotalClaimBalance().compareTo(BigDecimal.ZERO) != 0) {
//                                    light.setClaimId(c1.getId());
//                                    light.setPatientInsuranceId(c1.getResponsiblePatientInsuranceId());
//                                    light.setInsuranceId(c1.getPatientInsurance().getInsuranceCompanyId());
//                                    light.setCarrier_type(iv.getCarrierType());
//                                } else if (c1.getTotalPtResponsibilityBalance().compareTo(BigDecimal.ZERO) != 0) {
//                                    light.setClaimId(c1.getId());
//                                    light.setPatientInsuranceId(0L);
//                                    light.setInsuranceId(0L);
//                                    light.setCarrier_type(iv.getCarrierType());
//                                }
//                            } else {
//                                result.getErrorMessages().add("No Insurance Verification with Patient Insurance Id = " + c1.getResponsiblePatientInsuranceId() + " And Prescription Id = " + c1.getPrescriptionId());
//                            }
//                        }
//                    }
//
//                    if (claimLightWeightDTOHashMap.containsKey(light.getInsuranceId())) {
//                        if (light.getClaimBalance().compareTo(BigDecimal.ZERO) != 0 &&
//                            ((useSubmissionDate && light.getSubmissionDate() != null) || (!useSubmissionDate && light.getDateOfService() != null))) {
//                            List<ClaimLightWeightDTO> temp = claimLightWeightDTOHashMap.get(light.getInsuranceId());
//                            temp.add(light);
//                            temp.sort(Comparator.comparing(ClaimLightWeightDTO::getAging, Comparator.nullsFirst(Comparator.naturalOrder())));
//                            claimLightWeightDTOHashMap.put(light.getInsuranceId(), temp);
//                        }
//                    } else {
//                        if (light.getClaimBalance().compareTo(BigDecimal.ZERO) != 0 &&
//                            ((useSubmissionDate && light.getSubmissionDate() != null) || (!useSubmissionDate && light.getDateOfService() != null))) {
//                            List<ClaimLightWeightDTO> temp = new ArrayList<>();
//                            temp.add(light);
//                            temp.sort(Comparator.comparing(ClaimLightWeightDTO::getAging, Comparator.nullsFirst(Comparator.naturalOrder())));
//                            claimLightWeightDTOHashMap.put(light.getInsuranceId(), temp);
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        for (Map.Entry<Long, List<ClaimLightWeightDTO>> entry : claimLightWeightDTOHashMap.entrySet()) {
//            List<ClaimLightWeightDTO> clwdtoList = entry.getValue();
//            clwdtoList.removeIf(dto -> hideNegativeBalance && dto.getClaimBalance().doubleValue() < 0);
//            OutstandingBalanceEntityDTO outStandingDto = new OutstandingBalanceEntityDTO();
//            if (entry.getKey().equals(0L)) {
//                outStandingDto.setCompanyId(0L);
//                outStandingDto.setCompanyName("Patient Responsibility");
//            } else {
//                InsuranceCompany company = insuranceCompanyRepository.findById(entry.getKey()).get();
//                outStandingDto.setCompanyId(entry.getKey());
//                outStandingDto.setCompanyName(company.getName());
//            }
//            List<ClaimLightWeightDTO> uiList = new ArrayList<>();
//            for (ClaimLightWeightDTO x : clwdtoList) {
//                Long diff = x.getAging();
//                if (x.getAging() >= 0L) {
//                    uiList.add(x);
//                }
//
//                // Get latest task assigned to claim and attach description from task to ClaimLightWeightDTO
//                List<Task> claimTasks = taskRepository.findByClaimId(x.getClaimId());
//                if (claimTasks != null && claimTasks.size() > 0 && claimTasks.get(claimTasks.size() - 1).getDescription() != null) {
//                    x.setDescription(claimTasks.get(claimTasks.size() - 1).getDescription());
//                }
//
//                if (diff == -1) {
//                    outStandingDto.setNoSubmissionBalance(outStandingDto.getNoSubmissionBalance().add(x.getClaimBalance()));
//                    result.setNoSubmissionBalanceTotal(result.getNoSubmissionBalanceTotal().add(x.getClaimBalance()));
//                } else if (diff >= 0 && diff <= 29) {
//                    outStandingDto.setZeroThirtyBalance(outStandingDto.getZeroThirtyBalance().add(x.getClaimBalance()));
//                    outStandingDto.setTotalBalance(outStandingDto.getTotalBalance().add(x.getClaimBalance()));
//                    result.setZeroThirtyBalanceTotal(result.getZeroThirtyBalanceTotal().add(x.getClaimBalance()));
//                    result.setTotalReportBalanceTotal(result.getTotalReportBalanceTotal().add(x.getClaimBalance()));
//                } else if (diff > 29 && diff <= 59) {
//                    outStandingDto.setThirtySixtyBalance(outStandingDto.getThirtySixtyBalance().add(x.getClaimBalance()));
//                    outStandingDto.setTotalBalance(outStandingDto.getTotalBalance().add(x.getClaimBalance()));
//                    result.setThirtySixtyBalanceTotal(result.getThirtySixtyBalanceTotal().add(x.getClaimBalance()));
//                    result.setTotalReportBalanceTotal(result.getTotalReportBalanceTotal().add(x.getClaimBalance()));
//                } else if (diff > 59 && diff <= 89) {
//                    outStandingDto.setSixtyNinetyBalance(outStandingDto.getSixtyNinetyBalance().add(x.getClaimBalance()));
//                    outStandingDto.setTotalBalance(outStandingDto.getTotalBalance().add(x.getClaimBalance()));
//                    result.setSixtyNinetyBalanceTotal(result.getSixtyNinetyBalanceTotal().add(x.getClaimBalance()));
//                    result.setTotalReportBalanceTotal(result.getTotalReportBalanceTotal().add(x.getClaimBalance()));
//                } else if (diff > 89 && diff <= 119) {
//                    outStandingDto.setNinetyOneTwentyBalance(outStandingDto.getNinetyOneTwentyBalance().add(x.getClaimBalance()));
//                    outStandingDto.setTotalBalance(outStandingDto.getTotalBalance().add(x.getClaimBalance()));
//                    result.setNinetyOneTwentyBalanceTotal(result.getNinetyOneTwentyBalanceTotal().add(x.getClaimBalance()));
//                    result.setTotalReportBalanceTotal(result.getTotalReportBalanceTotal().add(x.getClaimBalance()));
//                } else {
//                    outStandingDto.setOneTwentyPlusBalance(outStandingDto.getOneTwentyPlusBalance().add(x.getClaimBalance()));
//                    outStandingDto.setTotalBalance(outStandingDto.getTotalBalance().add(x.getClaimBalance()));
//                    result.setOneTwentyPlusBalanceTotal(result.getOneTwentyPlusBalanceTotal().add(x.getClaimBalance()));
//                    result.setTotalReportBalanceTotal(result.getTotalReportBalanceTotal().add(x.getClaimBalance()));
//                }
//                //System.out.println("outstanding balance: diff = " + diff + "  result.getTotalReportBalanceTotal() = " + result.getTotalReportBalanceTotal());
//
//            }
//            outStandingDto.setClaimLists(uiList);
//            if (outStandingDto.getTotalBalance().compareTo(BigDecimal.ZERO) != 0) {
//                result.getEntityList().add(outStandingDto);
//            }
//        }
//        result.getEntityList().sort(Comparator.comparing(OutstandingBalanceEntityDTO::getCompanyName));
//
//        if (!result.getTotalReportBalanceTotal().equals(BigDecimal.ZERO)) {
//            if (!result.getZeroThirtyBalanceTotal().equals(BigDecimal.ZERO)) {
//                result.setZeroThirtyPercentage(result.getZeroThirtyBalanceTotal().multiply(new BigDecimal("100")).divide(result.getTotalReportBalanceTotal(), RoundingMode.HALF_EVEN).doubleValue());
//            }
//            if (!result.getThirtySixtyBalanceTotal().equals(BigDecimal.ZERO)) {
//                result.setThirtySixtyPercentage(result.getThirtySixtyBalanceTotal().multiply(new BigDecimal("100")).divide(result.getTotalReportBalanceTotal(), RoundingMode.HALF_EVEN).doubleValue());
//            }
//            if (!result.getSixtyNinetyBalanceTotal().equals(BigDecimal.ZERO)) {
//                result.setSixtyNinetyPercentage(result.getSixtyNinetyBalanceTotal().multiply(new BigDecimal("100")).divide(result.getTotalReportBalanceTotal(), RoundingMode.HALF_EVEN).doubleValue());
//            }
//            if (!result.getNinetyOneTwentyBalanceTotal().equals(BigDecimal.ZERO)) {
//                result.setNinetyOneTwentyPercentage(result.getNinetyOneTwentyBalanceTotal().multiply(new BigDecimal("100")).divide(result.getTotalReportBalanceTotal(), RoundingMode.HALF_EVEN).doubleValue());
//            }
//            if (!result.getOneTwentyPlusBalanceTotal().equals(BigDecimal.ZERO)) {
//                result.setOneTwentyPlusPercentage(result.getOneTwentyPlusBalanceTotal().multiply(new BigDecimal("100")).divide(result.getTotalReportBalanceTotal(), RoundingMode.HALF_EVEN).doubleValue());
//            }
//        }
//        return result;
    }

    /**
     * J. Brett Rowell 02/20/2020
     *
     * @param endDate  (Required all Aging periods will be based upon this date)
     * @param branchId (Required if all OL or null)
     * @return ArAgingReportDto
     * <p>
     * Process:
     * Each Period gathers IVLCs for Billable, Allowables, WriteOffs (Sum)
     * We then take all Claims involved and sum payments from applied_payments and APLCs
     * We are keeping up with AR level adjusments in applied_payment.
     * Once all are summed for each period we then create the ArAgingReportDTO
     * <p>
     * NOTE: currentDate Predetermines the start Date for all Buckets
     */
    public ArAgingReportDTO arAgingReport(Date endDate, Long branchId, String dateOption) {
        Date startDate = Date.valueOf("1990-01-01");
        log.info(TenantContext.getCurrentTenant() + "_ar_aging");
        return getArAgingReport(startDate, endDate, branchId, dateOption);
    }

    public ArAgingReportDTO getArAgingReport(Date startDate, Date endDate, Long branchId, String dateOption) {
        List<ClaimLightWeightDTO> claimLightWeightDTOList = getClaimLightWeightDTOList(startDate, endDate, branchId, true, true);
        List<Object[]> arAdjustments = appliedPaymentLCodeRepository.getArAgingArAdjustments(branchId, dateOption, startDate, endDate);
        return getArAgingReportDTO(startDate, endDate, branchId, dateOption, claimLightWeightDTOList, arAdjustments);
    }

    public ArAgingReportDTO getArAgingReportDTO(Date startDate, Date endDate, Long branchId, String dateOption, List<ClaimLightWeightDTO> claimLightWeightDTOList, List<Object[]> arAdjustments) {

        return new ArAgingReportDTO();

        //ArAgingReportDTO result = new ArAgingReportDTO();
       // Map<String, BigDecimal> totalContractualDifferenceMap = populateDeviceTypeMap();

////        System.out.println("SqlConstants.arAgingArAdjustmentsQuery = \n" +
////            SqlConstants.arAgingArAdjustmentsQuery
////                .replaceAll(":branchId", branchId + "")
////                .replaceAll(":dateOption", "'" + dateOption + "'")
////                .replaceAll(":startDate", "'" + startDate + "'")
////                .replaceAll(":endDate", "'" + endDate + "'")
////        );
//
//        BigDecimal sumArAdjustments = BigDecimal.ZERO;
//        Set<Long> arAdjustmentClaimSet = new HashSet<>();
//        Map<Long, BigDecimal> arAdjustmentsMap = new HashMap<>();
//        for (Object[] o : arAdjustments) {
//            arAdjustmentsMap.put(Long.valueOf(o[0].toString()), new BigDecimal(o[1].toString()));
//            sumArAdjustments = sumArAdjustments.add(new BigDecimal(o[1].toString()));
//            arAdjustmentClaimSet.add(Long.valueOf(o[0].toString()));
//        }
//
//        BigDecimal sumArAdjustmentsForClaims = BigDecimal.ZERO;
//        //ArDataDTO tally = new ArDataDTO();
//        Set<Long> prescriptionIdSet = new HashSet<>();
//        for (ClaimLightWeightDTO claimLightWeightDTO : claimLightWeightDTOList) {
//            try {
//                if (prescriptionIdSet.contains(claimLightWeightDTO.getPrescriptionId())) {
//                    log.error("ERROR: ArAgingReport - Duplicate prescription ID = " + claimLightWeightDTO.getPrescriptionId() + " on claim id = " + claimLightWeightDTO.getClaimId());
//                } else {
//                    prescriptionIdSet.add(claimLightWeightDTO.getPrescriptionId());
//                }
//
//                claimLightWeightDTO.setArDataDTO(processArDataDTOAndProcessAppliedPaymentAndAdjustments(claimLightWeightDTO, Date.valueOf("1990-01-01"), endDate, dateOption));
//                //System.out.println("ArAging            : claimId = " + claimLightWeightDTO.getClaimId() + ",  claimLightWeightDTO.getArDataDTO().getAccountsReceivables() = " + claimLightWeightDTO.getArDataDTO().getAccountsReceivables());
//                if (claimLightWeightDTO.getArDataDTO().getErrorMessages().size() > 0) {
//                    result.getErrorMessages().addAll(claimLightWeightDTO.getArDataDTO().getErrorMessages());
//                }
//                claimLightWeightDTO.setDeviceType(claimLightWeightDTO.getDeviceType());
//                if (arAdjustmentsMap.containsKey(claimLightWeightDTO.getClaimId())) {
//                    arAdjustmentClaimSet.remove(claimLightWeightDTO.getClaimId());
//                    sumArAdjustmentsForClaims = sumArAdjustmentsForClaims.add(arAdjustmentsMap.get(claimLightWeightDTO.getClaimId()).setScale(2, RoundingMode.HALF_UP));
//                    if (claimLightWeightDTO.getArDataDTO().getArLevelAdjustments().setScale(2, RoundingMode.HALF_UP).compareTo(arAdjustmentsMap.get(claimLightWeightDTO.getClaimId()).setScale(2, RoundingMode.HALF_UP)) != 0) {
//                        //System.out.println("Replacing " + claimLightWeightDTO.getArDataDTO().getArLevelAdjustments() + " with " + arAdjustmentsMap.get(claimLightWeightDTO.getClaimId()));
//                        claimLightWeightDTO.getArDataDTO().setArLevelAdjustments(arAdjustmentsMap.get(claimLightWeightDTO.getClaimId()));
//                    }
//                } else if (claimLightWeightDTO.getArDataDTO().getArLevelAdjustments().compareTo(BigDecimal.ZERO) != 0) {
//                    //System.out.println("Claim id = " + claimLightWeightDTO.getClaimId() + ". This claim should have an arAdjustment of zero and it does not, arLevelAdjustment = " + claimLightWeightDTO.getArDataDTO().getArLevelAdjustments());
//                }
//                //prescriptionService.printClaimLightWeightDTO(2L, lightDto);
//                Long diff = claimLightWeightDTO.getAging();
//                List<ClaimLightWeightDTO> temp = new ArrayList<>();
//                if (diff >= 0 && diff <= 29) {
//                    claimLightWeightDTO.setBucket("zeroThirty");
//                } else if (diff > 29 && diff <= 59) {
//                    claimLightWeightDTO.setBucket("thirtySixty");
//                } else if (diff > 59 && diff <= 89) {
//                    claimLightWeightDTO.setBucket("sixtyNinety");
//                } else if (diff > 89 && diff <= 119) {
//                    claimLightWeightDTO.setBucket("ninetyOneTwenty");
//                } else if (diff > 119) {
//                    claimLightWeightDTO.setBucket("oneTwentyPlus");
//                }
//
//                for (Map.Entry<String, BigDecimal> entry : claimLightWeightDTO.getArDataDTO().getContractualDiffMap().entrySet()) {
//                    if(entry.getValue().compareTo(BigDecimal.ZERO) != 0 ){
//                        BigDecimal runningTotal = totalContractualDifferenceMap.get(entry.getKey());
//                        totalContractualDifferenceMap.put(entry.getKey(), runningTotal.add(entry.getValue()));
//                    }
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        if (sumArAdjustments.setScale(2, RoundingMode.HALF_UP).compareTo(sumArAdjustmentsForClaims) != 0) {
////            System.out.println("SqlConstants.arAgingArAdjustmentsQuery = \n" + SqlConstants.arAgingArAdjustmentsQuery
////                .replaceAll(":branchId", branchId + "")
////                .replaceAll(":dateOption", "'" + dateOption + "'")
////                .replaceAll(":startDate", "'" + startDate + "'")
////                .replaceAll(":endDate", "'" + endDate + "'")
////            );
////            System.out.println("This is for ar aging quick fix query (Should match Sales Summary): sumArAdjustments = " + sumArAdjustments.setScale(2, RoundingMode.HALF_UP));
////            System.out.println("This is the sum for the matching claims: sumArAdjustmentsForClaims = " + sumArAdjustmentsForClaims);
////            System.out.println("Theses are the claims from the quick fix query that were not matched the ar aging query:  Unused claim ids = " + arAdjustmentClaimSet.size());
//            for (Long c : arAdjustmentClaimSet) {
//                System.out.println("Unused claim id = " + c);
//            }
//        }
//
//        //System.out.println("getArAgingReport tally.getAppliedAdjustmentsTotals() = " + tally.getAppliedAdjustmentsTotals());
//        //System.out.println("getArAgingReport tally.getArLevelAdjustments() = " + tally.getArLevelAdjustments());
//
//        ArDataDTO zeroThirty = processByBucket(claimLightWeightDTOList, "zeroThirty");
//        ArDataDTO thirtySixty = processByBucket(claimLightWeightDTOList, "thirtySixty");
//        ArDataDTO sixtyNinety = processByBucket(claimLightWeightDTOList, "sixtyNinety");
//        ArDataDTO ninetyOneTwenty = processByBucket(claimLightWeightDTOList, "ninetyOneTwenty");
//        ArDataDTO oneTwentyPlus = processByBucket(claimLightWeightDTOList, "oneTwentyPlus");
//
//        ArDataDTO totalArDto = new ArDataDTO();
//        totalArDto = addArDataDtoToExistingArDataDto(totalArDto, zeroThirty);
//        totalArDto = addArDataDtoToExistingArDataDto(totalArDto, thirtySixty);
//        totalArDto = addArDataDtoToExistingArDataDto(totalArDto, sixtyNinety);
//        totalArDto = addArDataDtoToExistingArDataDto(totalArDto, ninetyOneTwenty);
//        totalArDto = addArDataDtoToExistingArDataDto(totalArDto, oneTwentyPlus);
//
//        result.getArDataList().put("zeroThirty", zeroThirty);
//        result.getArDataList().put("thirtySixty", thirtySixty);
//        result.getArDataList().put("sixtyNinety", sixtyNinety);
//        result.getArDataList().put("ninetyOneTwenty", ninetyOneTwenty);
//        result.getArDataList().put("oneTwentyPlus", oneTwentyPlus);
//
//        //Needed for Flot chart
//        Object[][] totalAccountsReceivable = {{0, zeroThirty.getAccountsReceivables()}, {1, thirtySixty.getAccountsReceivables()}, {2, sixtyNinety.getAccountsReceivables()},
//            {3, ninetyOneTwenty.getAccountsReceivables()}, {4, oneTwentyPlus.getAccountsReceivables()}};
//        result.setAccountsReceivablesMap(totalAccountsReceivable);
//
//        result.setTotalBillable(totalArDto.getBillable());
//        result.setTotalContractualAdjustment(totalArDto.getContractualAdjustment());
//        result.setTotalAllowable(totalArDto.getAllowable());
//        result.setTotalContractualDifferences(totalArDto.getContractualAdjustmentDiffTotals());
//        result.setTotalCoOrthotic(totalContractualDifferenceMap.get("orthotic"));
//        result.setTotalCoProsthetic(totalContractualDifferenceMap.get("prosthetic"));
//        result.setTotalCoPedorthic(totalContractualDifferenceMap.get("pedorthic"));
//        result.setTotalCoMisc(totalContractualDifferenceMap.get("misc"));
//        result.setTotalArAdjustments(totalArDto.getArLevelAdjustments());
//        result.setTotalLineAdjustments(totalArDto.getAppliedAdjustmentsTotals());
//        result.setTotalPayments(totalArDto.getAppliedPaymentsTotals());
//        result.setTotalAccountsReceivables(totalArDto.getAccountsReceivables());
////        System.out.println(appliedPaymentLCodeRepository.totalDailyCloseQuery
////                .replaceAll(":dateOption", "'" + dateOption + "'")
////                .replaceAll(":branchId", branchId == null ? "null" : branchId.toString())
////                .replaceAll(":asOfDate", "'"+endDate.toString()+"'")
////        );
//        DailyCloseReportDTO dailyClose = paymentService.dailyClose("all", dateOption, startDate, endDate, branchId);
//        BigDecimal dailyCloseGrandTotal = BigDecimal.ZERO;
//        for (DailyCloseBranchDTO dcb : dailyClose.getBranchCloseDtos()) {
//            dailyCloseGrandTotal = dailyCloseGrandTotal.add(dcb.getBranchTotal());
//        }
//        dailyCloseGrandTotal = dailyCloseGrandTotal.negate();
//        if (result.getTotalPayments() != null && dailyCloseGrandTotal != null) {
//            result.setTotalInvalidSubmission(dailyCloseGrandTotal.subtract(result.getTotalPayments()));
//        }

//        System.out.println("endDate = Fake.getSqlDate(\""+endDate+"\");");
//        System.out.println("arAgingReportDTO = adHocReportService.arAgingReport(endDate, branchId, dateOption);");
//        printCheckComparison(result, "zeroThirty");
//        printCheckComparison(result, "thirtySixty");
//        printCheckComparison(result, "sixtyNinety");
//        printCheckComparison(result, "ninetyOneTwenty");
//        printCheckComparison(result, "oneTwentyPlus");
//        System.out.println("checkComparisonTotal(arAgingReportDTO, \""+
//            result.getTotalBillable() + "\", \"" +
//            result.getTotalContractualAdjustment() + "\", \"" +
//            result.getTotalAllowable() + "\", \"" +
//            result.getTotalContractualDifferences() + "\", \"" +
//            result.getTotalArAdjustments() + "\", \"" +
//            result.getTotalLineAdjustments() + "\", \"" +
//            result.getTotalPayments() + "\", \"" +
//            result.getTotalAccountsReceivables() + "\");");
//        System.out.println("assertEquals(0,arAgingReportDTO.getTotalInvalidSubmission().compareTo(BigDecimal.valueOf(Double.valueOf(\""+result.getTotalInvalidSubmission()+"\"))));\n");

//        return result;
    }

    private void printCheckComparison(ArAgingReportDTO result, String bucket) {
        System.out.println("checkComparison(arAgingReportDTO, \"" +
            result.getArDataList().get(bucket).getBillable().toString() + "\", \"" +
            result.getArDataList().get(bucket).getContractualAdjustment().toString() + "\", \"" +
            result.getArDataList().get(bucket).getAllowable().toString() + "\", \"" +
            result.getArDataList().get(bucket).getContractualAdjustmentDiffTotals().toString() + "\", \"" +
            result.getArDataList().get(bucket).getArLevelAdjustments().toString() + "\", \"" +
            result.getArDataList().get(bucket).getAppliedAdjustmentsTotals().toString() + "\", \"" +
            result.getArDataList().get(bucket).getAppliedPaymentsTotals().toString() + "\", \"" +
            result.getArDataList().get(bucket).getAccountsReceivables().toString() + "\", \"" +
            bucket + "\");");
    }

    public ArDataDTO processByBucket(List<ClaimLightWeightDTO> claimLightWeightDTOList, String bucket) {
        ArDataDTO arDataDTOBucket = new ArDataDTO();
        for (ClaimLightWeightDTO claimLightWeightDTO : claimLightWeightDTOList) {
            if (bucket.equals(claimLightWeightDTO.getBucket())) {
                if (claimLightWeightDTO.getArDataDTO() == null) {
                    System.out.println(bucket + " has null arDataDTO for claim id = " + claimLightWeightDTO.getClaimId());
                } else {
                    arDataDTOBucket = addArDataDtoToExistingArDataDto(arDataDTOBucket, claimLightWeightDTO.getArDataDTO());
                }
            }
        }
        //System.out.println(bucket + " AccountsReceivables = " + arDataDTOBucket.getAccountsReceivables());
        return arDataDTOBucket;
    }

    public ArDataDTO processArDataDTOAndProcessAppliedPaymentAndAdjustments(ClaimLightWeightDTO claimLightWeightDTO, Date startDate, Date endDate, String dateOption) {

        Long prescriptionId = claimLightWeightDTO.getPrescriptionId();
        Long claimId = claimLightWeightDTO.getClaimId();
        BigDecimal billable = claimLightWeightDTO.getTotalBillable();
        BigDecimal allowable = claimLightWeightDTO.getTotalAllowable();

        return processArDataDTOAndProcessAppliedPaymentAndAdjustments(startDate, endDate, dateOption, prescriptionId, claimId, billable, allowable);
    }

    public ArDataDTO processArDataDTOAndProcessAppliedPaymentAndAdjustments(Date startDate, Date endDate, String dateOption, Long prescriptionId, Long claimId, BigDecimal billable, BigDecimal allowable) {
        ArDataDTO result = new ArDataDTO();
        result.setId(claimId);
        result.setBillable(billable);
        result.setAllowable(allowable);
        result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        List<AppliedPaymentLCodesAmountsDTO> prescriptionAplcList = new ArrayList<>(buildPaymentDataForDto(prescriptionId, startDate, endDate, dateOption));
        Map<String, Map<String, BigDecimal>> cdLineMap = getContractualDifferenceAndLineAdjustmentMaps(startDate, endDate, dateOption, prescriptionId);

        Map<String, BigDecimal> cdMap = cdLineMap.get("cd");
        Map<String, BigDecimal> lineMap = cdLineMap.get("line");
        Map<String, BigDecimal> coTotalMap = cdLineMap.get("co");

        if (cdLineMap.get("error").size() > 0) {
            for (Map.Entry<String, BigDecimal> entry : cdLineMap.get("error").entrySet()) {
                result.getErrorMessages().add(entry.getKey());
            }
        }

        result = processAppliedPaymentAndAdjustments(result, prescriptionAplcList);
        result.setAppliedAdjustmentsTotals(sumValueFromMap(lineMap).negate());
        result.setContractualAdjustmentDiffTotals(sumValueFromMap(cdMap));
        result.setContractualDiffMap(cdMap);
        result.setContractualAdjustmentAppliedTotals(sumValueFromMap(coTotalMap));
        if (result.getContractualAdjustmentDiffTotals().compareTo(BigDecimal.ZERO) != 0) {
            result.setAccountsReceivables(calculateAccountReceivables(result, false));
        }
        //result.getErrorMessages().addAll(dto.errorMessages);
        return result;
    }

    public Map<String, Map<String, BigDecimal>> getContractualDifferenceAndLineAdjustmentMaps(Date startDate, Date endDate, String dateOption, Long prescriptionId) {
        DataModel allAppliedPaymentsOnPrescriptionDm = appliedPaymentLCodeService.allAppliedPaymentsOnPrescription(prescriptionId, startDate, endDate, dateOption);
        List<AppliedPayment_L_Code> aplcByPrescriptionList = populateAppliedPaymentLCodeList(allAppliedPaymentsOnPrescriptionDm);
        return processCdAndLaByPrescriptionIdAppliedPaymentLCodes(prescriptionId, aplcByPrescriptionList);
    }

    public ArDataDTO processAppliedPaymentAndAdjustmentsWithAppliedPaymentLCodeList(ArDataDTO result, List<AppliedPayment_L_Code> apLcodeAmountsList) {
        BigDecimal appliedPaymentTotals = result.getAppliedPaymentsTotals() == null ? BigDecimal.ZERO : result.getAppliedPaymentsTotals();
        BigDecimal arLevelAdjustments = result.getArLevelAdjustments() == null ? BigDecimal.ZERO : result.getArLevelAdjustments();
        BigDecimal arLevelWithdrawAdjustment = BigDecimal.ZERO;
        result.setDeviceType("");
        result.setAccountsReceivables(result.getAccountsReceivables());
        //            "CASE WHEN aplc.adjustment != 0.00 and a.withdraw = 1 THEN CASE WHEN a.operation != '+' THEN (aplc.adjustment * -1) ELSE aplc.adjustment END ELSE 0.00  END as arWithdrawAdjustment, \n" +
        // "CASE WHEN aplc.adjustment != 0.00 THEN CASE WHEN a.operation != '+' THEN (aplc.adjustment * -1) ELSE aplc.adjustment END ELSE 0.00 END as arAdjustment, \n" +

        for (AppliedPayment_L_Code aplc : apLcodeAmountsList) {
            appliedPaymentTotals = appliedPaymentTotals.add(aplc.getAmount() == null ? BigDecimal.ZERO : aplc.getAmount().multiply(BigDecimal.valueOf(-1L)));
            BigDecimal aplcArlevelWithdrawAdjustment = BigDecimal.ZERO;
            BigDecimal aplcArLevelAdjustment = BigDecimal.ZERO;
            if (aplc.getAdjustment() != null && aplc.getAdjustment().compareTo(BigDecimal.ZERO) != 0) {
                if (aplc.getOperator().getWithdraw() != null && aplc.getOperator().getWithdraw()) {
                    if (!aplc.getOperator().getOperation().equals("+")) {
                        aplcArlevelWithdrawAdjustment = aplcArlevelWithdrawAdjustment.add(aplc.getAdjustment().multiply(BigDecimal.valueOf(-1L)));
                    } else {
                        aplcArlevelWithdrawAdjustment = aplcArlevelWithdrawAdjustment.add(aplc.getAdjustment());
                    }
                }
                if (!aplc.getOperator().getOperation().equals("+")) {
                    aplcArLevelAdjustment = aplcArLevelAdjustment.add(aplc.getAdjustment().multiply(BigDecimal.valueOf(-1L)));
                } else {
                    aplcArLevelAdjustment = aplcArLevelAdjustment.add(aplc.getAdjustment());
                }
            }
            arLevelWithdrawAdjustment = arLevelWithdrawAdjustment.add(aplcArlevelWithdrawAdjustment);
            //System.out.println("arLevelWithdrawAdjustment = "+arLevelWithdrawAdjustment);
            if (aplcArlevelWithdrawAdjustment.compareTo(BigDecimal.ZERO) == 0) {
                //System.out.println("aplcdto.getArLevelAdjustment() = "+aplcdto.getArLevelAdjustment());
                arLevelAdjustments = arLevelAdjustments.add(aplcArLevelAdjustment);//Already Negated
                //System.out.println("arLevelAdjustments = "+arLevelAdjustments);
            }
        }
        if (apLcodeAmountsList.size() > 0 && apLcodeAmountsList.get(0).getAppliedPayment().getClaim().getPrescription().getDeviceType().getOrthoticOrProsthetic() != null) {
            result.setDeviceType(apLcodeAmountsList.get(0).getAppliedPayment().getClaim().getPrescription().getDeviceType().getOrthoticOrProsthetic());
        }
        result.setAppliedPaymentsTotals(appliedPaymentTotals.subtract(arLevelWithdrawAdjustment.negate()));
        result.setArLevelAdjustments(arLevelAdjustments);
        return result;
    }

    public BigDecimal sumValueFromMap(Map<String, BigDecimal> cdMap) {
        BigDecimal bd = BigDecimal.ZERO;
        for (Map.Entry<String, BigDecimal> entry : cdMap.entrySet()) {
            bd = bd.add(entry.getValue());
        }
        return bd;
    }

    public Map<Long, PrescriptionClaimsDTO> populatePrescriptionClaimsDTOHashMap(List<ClaimLightWeightDTO> claimLightWeightDTOList) {
        Map<Long, PrescriptionClaimsDTO> prescriptionClaimsDTOHashMap = new HashMap<>();
        for (ClaimLightWeightDTO c : claimLightWeightDTOList) {
            PrescriptionClaimsDTO rxClaim = new PrescriptionClaimsDTO();
            if (prescriptionClaimsDTOHashMap.containsKey(c.getPrescriptionId())) {
                rxClaim = prescriptionClaimsDTOHashMap.get(c.getPrescriptionId());
            } else {
                rxClaim.setPrescriptionId(c.getPrescriptionId());
            }

            if (null != c.getCarrier_type() && "primary".equals(c.getCarrier_type())) {
                rxClaim.setPrimaryClaim(c);
            } else if (null != c.getCarrier_type() && "secondary".equals(c.getCarrier_type())) {
                rxClaim.setSecondaryClaim(c);
            } else if (null != c.getCarrier_type() && "tertiary".equals(c.getCarrier_type())) {
                rxClaim.setTertiaryClaim(c);
            } else if (null != c.getCarrier_type() && "quaternary".equals(c.getCarrier_type())) {
                rxClaim.setQuaternaryClaim(c);
            } else if (null != c.getCarrier_type() && "quinary".equals(c.getCarrier_type())) {
                rxClaim.setQuinaryClaim(c);
            } else if (null != c.getCarrier_type() && "senary".equals(c.getCarrier_type())) {
                rxClaim.setSenaryClaim(c);
            } else if (null != c.getCarrier_type() && "septenary".equals(c.getCarrier_type())) {
                rxClaim.setSeptenaryClaim(c);
            } else if (null != c.getCarrier_type() && "octonary".equals(c.getCarrier_type())) {
                rxClaim.setOctonaryClaim(c);
            } else if (null != c.getCarrier_type() && "nonary".equals(c.getCarrier_type())) {
                rxClaim.setNonaryClaim(c);
            } else if (null != c.getCarrier_type() && "denary".equals(c.getCarrier_type())) {
                rxClaim.setDenaryClaim(c);
            } else if (null != c.getCarrier_type() && "other".equals(c.getCarrier_type())) {
                rxClaim.setOther(c);
            } else {
                System.out.println("Odd carrier type, " + c.getCarrier_type() + ", for claim id = " + c.getClaimId());
            }
            //Taking care of patient Balance Data for new data point.
            if (c.getPatientBalance().compareTo(BigDecimal.ZERO) != 0) {
                rxClaim.setPatientBalance(c);
            }
            prescriptionClaimsDTOHashMap.put(c.getPrescriptionId(), rxClaim);
        }
        return prescriptionClaimsDTOHashMap;
    }

    public List<ClaimLightWeightDTO> getClaimLightWeightDTOList(Date startDate, Date endDate, Long branchId, boolean useSubmissionDate, boolean requiresClaimSubmission) {
        List<ClaimLightWeightDTO> claimLightWeightDTOList = new ArrayList<>();
        List<ClaimInsuranceVerificationLCodesDTO> claimInsuranceVerificationLCodesDTOList = getClaimInsuranceVerificationLCodesDTOList(startDate, endDate, branchId, useSubmissionDate, requiresClaimSubmission, false);
        for (ClaimInsuranceVerificationLCodesDTO claimInsuranceVerificationLCodesDTO : claimInsuranceVerificationLCodesDTOList) {
            ClaimLightWeightDTO c = new ClaimLightWeightDTO();
            c.setPatientId(claimInsuranceVerificationLCodesDTO.getClaim().getPrescription().getPatientId());
            c.setPrescriptionId(claimInsuranceVerificationLCodesDTO.getClaim().getPrescriptionId());
            c.setClaimId(claimInsuranceVerificationLCodesDTO.getClaim().getId());
            c.setPatientInsuranceId(claimInsuranceVerificationLCodesDTO.getClaim().getResponsiblePatientInsuranceId());
            c.setInsuranceId(claimInsuranceVerificationLCodesDTO.getClaim().getResponsiblePatientInsurance().getInsuranceCompanyId());
            c.setDateOfService(claimInsuranceVerificationLCodesDTO.getClaim().getDateOfService());
            c.setClaimBalance(claimInsuranceVerificationLCodesDTO.getClaim().getTotalClaimBalance());
            c.setPatientBalance(claimInsuranceVerificationLCodesDTO.getClaim().getTotalPtResponsibilityBalance());
            c.setPatientName(claimInsuranceVerificationLCodesDTO.getClaim().getPrescription().getPatient().getFirstName() + " " + claimInsuranceVerificationLCodesDTO.getClaim().getPrescription().getPatient().getLastName());
            c.setCreatedDate(claimInsuranceVerificationLCodesDTO.getClaim().getCreatedAt());
            c.setPaymentPlan(claimInsuranceVerificationLCodesDTO.getClaim().getPaymentPlan());
            if (claimInsuranceVerificationLCodesDTO.getClaim().getPatientInsurance().getInsuranceCompany().getSelfPay()) {
                c.setClaimBalance(c.getPatientBalance());
                c.setPatientBalance(BigDecimal.ZERO);
            }
            if (c.getPatientBalance() == null || c.getPatientBalance().toString().equals("0.00")) {
                c.setPatientBalance(BigDecimal.ZERO);
            }
            if (c.getClaimBalance() == null || c.getClaimBalance().toString().equals("0.00")) {
                c.setClaimBalance(BigDecimal.ZERO);
            }
            c.setCarrier_type(claimInsuranceVerificationLCodesDTO.getCarrierType());
            c.setTotalAllowable(claimInsuranceVerificationLCodesDTO.getAllowable());
            c.setTotalBillable(claimInsuranceVerificationLCodesDTO.getBillable());

            if (claimInsuranceVerificationLCodesDTO.getClaimSubmissionDate() != null) {
                c.setPrimaryDate(claimInsuranceVerificationLCodesDTO.getClaimSubmissionDate()); //Done to use in calculation
                c.setAging(claimInsuranceVerificationLCodesDTO.getClaimSubmissionDate() != null ? claimInsuranceVerificationLCodesDTO.getClaimSubmissionDate().toLocalDate().until(endDate.toLocalDate(), ChronoUnit.DAYS) : -1);
                c.setSubmissionDate(claimInsuranceVerificationLCodesDTO.getClaimSubmissionDate());
            } else {
                c.setAging(-1L);
            }

            claimLightWeightDTOList.add(c);
        }
        return claimLightWeightDTOList;
    }

    /**
     * @param result             ArDataDTO
     * @param apLcodeAmountsList List AppliedPaymentLCodeAmountsDTO
     * @return ARDataDTO
     * <p>
     * This is a shared method used across multiple financial reports.
     * Any change to this method will change all financials.
     * As a caller of this method it is your responsibility to manipulate the data that fits the calling report.
     * Please do not change without consultation from others.
     * <p>
     * J.Brett Rowell
     */
    private ArDataDTO processAppliedPaymentAndAdjustments(ArDataDTO result, List<AppliedPaymentLCodesAmountsDTO> apLcodeAmountsList) {
        BigDecimal contractualAdjustmentTotals = result.getContractualAdjustmentDiffTotals() == null ? BigDecimal.ZERO : result.getContractualAdjustmentDiffTotals();
        BigDecimal appliedAdjustmentsTotals = result.getAppliedAdjustmentsTotals();
        BigDecimal appliedPaymentTotals = result.getAppliedPaymentsTotals() == null ? BigDecimal.ZERO : result.getAppliedPaymentsTotals();
        BigDecimal arLevelAdjustments = result.getArLevelAdjustments() == null ? BigDecimal.ZERO : result.getArLevelAdjustments();
        BigDecimal arLevelWithdrawAdjustment = BigDecimal.ZERO;
        result.setAccountsReceivables(result.getAccountsReceivables());
        for (AppliedPaymentLCodesAmountsDTO aplcdto : apLcodeAmountsList) {
            appliedPaymentTotals = appliedPaymentTotals.add(aplcdto.getPaymentAmount() == null ? BigDecimal.ZERO : aplcdto.getPaymentAmount());
            arLevelWithdrawAdjustment = arLevelWithdrawAdjustment.add(aplcdto.getArLevelWithdrawAdjustment() == null ? BigDecimal.ZERO : aplcdto.getArLevelWithdrawAdjustment());
            if (aplcdto.getArLevelWithdrawAdjustment() == null || aplcdto.getArLevelWithdrawAdjustment().compareTo(BigDecimal.ZERO) == 0) {
                arLevelAdjustments = arLevelAdjustments.add(aplcdto.getArLevelAdjustment() == null ? BigDecimal.ZERO : aplcdto.getArLevelAdjustment());//Already Negated
            }
            result.setDeviceType(aplcdto.getDeviceType());
        }

        result.setAppliedPaymentsTotals(appliedPaymentTotals.subtract(arLevelWithdrawAdjustment.negate()));
        result.setContractualAdjustmentDiffTotals(contractualAdjustmentTotals);
        result.setAppliedAdjustmentsTotals(appliedAdjustmentsTotals);
        result.setArLevelAdjustments(arLevelAdjustments);
        return result;
    }

    public BillingSummaryDTO paymentAdjustmentAndAppliedAmount(BillingSummaryDTO dto, List<Payment> payments) {
        for (Payment p : payments) {
            List<AppliedPayment> appliedPayments = appliedPaymentService.findByPaymentId(p.getId());
            BigDecimal tempPayment = p.getAmount();
            dto.setPaymentsTotal(dto.getPaymentsTotal().add(tempPayment));
            for (AppliedPayment ap : appliedPayments) {
                List<AppliedPayment_L_Code> aplcs = appliedPaymentLCodeService.findByAppliedPaymentId(ap.getId());
                for (AppliedPayment_L_Code aplc : aplcs) {
                    switch (p.getPaymentType()) {
                        case "patient_check":
                            dto.setPaymentsAppliedPatientChecks(dto.getPaymentsAppliedPatientChecks().add(aplc.getAmount()));
                            tempPayment = tempPayment.subtract(aplc.getAmount());
                            break;

                        case "cash":
                            dto.setPaymentsAppliedPatientCash(dto.getPaymentsAppliedPatientCash().add(aplc.getAmount()));
                            tempPayment = tempPayment.subtract(aplc.getAmount());
                            break;

                        case "patient_credit_card":
                            dto.setPaymentsAppliedPatientCreditCard(dto.getPaymentsAppliedPatientCreditCard().add(aplc.getAmount()));
                            tempPayment = tempPayment.subtract(aplc.getAmount());
                            break;

                        case "adjustment":

                        case "patient_adjustment":
                            break;

                        default:
                            // All Insurance
                            dto.setTotalAppliedInsurance(dto.getTotalAppliedInsurance().add(aplc.getAmount()));
                            tempPayment = tempPayment.subtract(aplc.getAmount());
                            break;
                    }

                    if (aplc.getAdjustment() != null && aplc.getOperator() != null) {
                        String key = aplc.getOperator().getName().concat("(").concat(
                                aplc.getOperator().getOperation()).concat(")");
                        BigDecimal tmp = dto.getAdjustmentsMap().get(key);
                        if ("+".equals(aplc.getOperator().getOperation())) {
                            dto.setAdjustmentsTotal(dto.getAdjustmentsTotal().add(aplc.getAdjustment()));
                            dto.getAdjustmentsMap().put(key, tmp == null ? aplc.getAdjustment() : tmp.add(aplc.getAdjustment()));
                        } else if ("-".equals(aplc.getOperator().getOperation())) {
                            dto.setAdjustmentsTotal(dto.getAdjustmentsTotal().subtract(aplc.getAdjustment()));
                            dto.getAdjustmentsMap().put(key,
                                    tmp == null ? aplc.getAdjustment().multiply(new BigDecimal(1L).negate()) : tmp.subtract(aplc.getAdjustment()));
                        }
                    }

                    if (aplc.getAdjustmentAmount1() != null && aplc.getAdjustmentType1() != null) {
                        String key = aplc.getAdjustmentType1();
                        BigDecimal tmp = dto.getLineAdjustmentsMap().get(key);
                        dto.setLineAdjustmentsTotal(dto.getLineAdjustmentsTotal().subtract(aplc.getAdjustmentAmount1()));
                        dto.getLineAdjustmentsMap().put(key,
                                tmp == null ? aplc.getAdjustmentAmount1().multiply(new BigDecimal(1L).negate()) : tmp.subtract(aplc.getAdjustmentAmount1()));
                    }
                    if (aplc.getAdjustmentAmount2() != null && aplc.getAdjustmentType2() != null) {
                        String key = aplc.getAdjustmentType2();
                        BigDecimal tmp = dto.getLineAdjustmentsMap().get(key);
                        dto.setLineAdjustmentsTotal(dto.getLineAdjustmentsTotal().subtract(aplc.getAdjustmentAmount2()));
                        dto.getLineAdjustmentsMap().put(key,
                                tmp == null ? aplc.getAdjustmentAmount2().multiply(new BigDecimal(1L).negate()) : tmp.subtract(aplc.getAdjustmentAmount2()));
                    }
                    if (aplc.getAdjustmentAmount3() != null && aplc.getAdjustmentType3() != null) {
                        String key = aplc.getAdjustmentType3();
                        BigDecimal tmp = dto.getLineAdjustmentsMap().get(key);
                        dto.setLineAdjustmentsTotal(dto.getLineAdjustmentsTotal().subtract(aplc.getAdjustmentAmount3()));
                        dto.getLineAdjustmentsMap().put(key,
                                tmp == null ? aplc.getAdjustmentAmount3().multiply(new BigDecimal(1L).negate()) : tmp.subtract(aplc.getAdjustmentAmount3()));
                    }
                    if (aplc.getAdjustmentAmount4() != null && aplc.getAdjustmentType4() != null) {
                        String key = aplc.getAdjustmentType4();
                        BigDecimal tmp = dto.getLineAdjustmentsMap().get(key);
                        dto.setLineAdjustmentsTotal(dto.getLineAdjustmentsTotal().subtract(aplc.getAdjustmentAmount4()));
                        dto.getLineAdjustmentsMap().put(key,
                                tmp == null ? aplc.getAdjustmentAmount4().multiply(new BigDecimal(1L).negate()) : tmp.subtract(aplc.getAdjustmentAmount4()));
                    }
                }
            }
            switch (p.getPaymentType()) {
                case "patient_check":
                    dto.setPaymentsUnappliedPatientChecks(dto.getPaymentsUnappliedPatientChecks().add(tempPayment));
                    break;
                case "cash":
                    dto.setPaymentsUnappliedPatientCash(dto.getPaymentsUnappliedPatientCash().add(tempPayment));
                    break;
                case "patient_credit_card":
                    dto.setPaymentsUnappliedPatientCreditCard(dto.getPaymentsUnappliedPatientCreditCard().add(tempPayment));
                    break;
                case "adjustment":
                case "patient_adjustment":
                    break;
                default:
                    dto.setTotalUnappliedInsurance(dto.getTotalUnappliedInsurance().add(tempPayment));
                    break;
            }
        }
        return dto;
    }

    public SalesTaxReportResultsDTO salesTaxReport(Date startDate, Date endDate) {
        SalesTaxReportResultsDTO results = new SalesTaxReportResultsDTO();

        // Get list of all active branches
        List<Branch> branches = branchService.findAllByActiveTrue().stream().filter(branch -> branch.getUseSalesTax()).collect(Collectors.toList());

        for (Branch branch : branches) {
            SalesTaxReportDTO dto = new SalesTaxReportDTO();

            // Get sum of sales tax from all PrescriptionLCodes within the date range
            List<Object[]> queryResults = insuranceVerificationLCodeService.getSalesTaxTotals(startDate, endDate, branch.getId(), "%");

            // This line changes the sales tax from a percentage to a decimal in order to use it in our calculations
            BigDecimal salesTaxPercentage = branch.getSalesTax() != null ? branch.getSalesTax().scaleByPowerOfTen(-2) : BigDecimal.ZERO;

            // Extract results from query and transform them to BigDecimals
            BigDecimal billable = new BigDecimal(queryResults.get(0)[0].toString());
            BigDecimal allowable = new BigDecimal(queryResults.get(0)[1].toString());
            BigDecimal totalSalesTax = new BigDecimal(queryResults.get(0)[3].toString());

            dto.setBranch(branch);
            dto.setBillable(billable);
            dto.setAllowable(allowable);
            dto.setSalesTaxPercentage(salesTaxPercentage);
            dto.setTotalSalesTax(totalSalesTax);

            results.getDtoList().add(dto);
            results.setTotalBillable(results.getTotalBillable().add(billable));
            results.setTotalAllowable(results.getTotalAllowable().add(allowable));
            results.setGrossSalesTax(results.getGrossSalesTax().add(totalSalesTax));
        }

        return results;
    }

    private BigDecimal calculateAccountReceivables(ArDataDTO obj, boolean useSumOfContractual) {
        obj.setBillable(obj.getBillable() == null ? BigDecimal.ZERO : obj.getBillable());
        BigDecimal result = obj.getBillable();
        if(useSumOfContractual){
            result = result.add(obj.getContractualAdjustmentAppliedTotals()); //This was added due to the way the front end handles these calculations
        } else {
            result = result.add(obj.getContractualAdjustment()); //Already Negated
            result = result.add(obj.getContractualAdjustmentDiffTotals());
        }
        result = result.add(obj.getArLevelAdjustments()); //True SUM
        result = result.add(obj.getAppliedAdjustmentsTotals()); //True SUM
        result = result.add(obj.getAppliedPaymentsTotals()); // Already Negated.
//        System.out.println("Calculate accounts receivables: " +
//            " billable = " + obj.getBillable() +
//            " contractual adjustment = " + obj.getContractualAdjustment() +
//            " contractualAdjustmentDiffTotals = " + obj.getContractualAdjustmentDiffTotals() +
//            " arLevelAdjustments = " + obj.getArLevelAdjustments() +
//            " appliedAdjustmentsTotals = " + obj.getAppliedAdjustmentsTotals() +
//            " appliedPaymentsTotals = " + obj.getAppliedPaymentsTotals() +
//            " Total = " + result
//        );
        return result;
    }

    public List<AppliedPaymentLCodesAmountsDTO> buildPaymentDataForDto(Long prescriptionId, Date startDate, Date
            endDate, String dateOption) {
        List<AppliedPaymentLCodesAmountsDTO> result = new ArrayList<>();

//        System.out.println(
//                appliedPaymentLCodeRepository.allAppliedPaymentsTotalsQuery
//                        .replaceAll(":prescriptionId", prescriptionId+"")
//                        .replaceAll(":startDate", startDate.toString())
//                        .replaceAll(":endDate", endDate.toString())
//                        .replaceAll(":dateOption", dateOption.toString())
//        );
        List<Object[]> appliedPaymentLCodesObjects = appliedPaymentLCodeRepository.getAllAppliedPaymentsTotals(prescriptionId, startDate, endDate, dateOption);
        for (Object[] aplco : appliedPaymentLCodesObjects) {
            result.add(new AppliedPaymentLCodesAmountsDTO(aplco));
        }
        return result;
    }

    private ArDataDTO processPrescriptionArDto(PrescriptionClaimsDTO rxDto, Date endDate, String dateOption) {
        ArDataDTO result = new ArDataDTO();
        ClaimLightWeightDTO primaryDto = rxDto.getPrimaryClaim();
        ClaimLightWeightDTO secondaryDto = rxDto.getSecondaryClaim();
        ClaimLightWeightDTO tertiaryDto = rxDto.getTertiaryClaim();
        ClaimLightWeightDTO quaternaryDto = rxDto.getQuaternaryClaim();
        ClaimLightWeightDTO quinaryDto = rxDto.getQuinaryClaim();
        ClaimLightWeightDTO senaryDto = rxDto.getSenaryClaim();
        ClaimLightWeightDTO septenaryDto = rxDto.getSeptenaryClaim();
        ClaimLightWeightDTO octonaryDto = rxDto.getOctonaryClaim();
        ClaimLightWeightDTO nonaryDto = rxDto.getNonaryClaim();
        ClaimLightWeightDTO denaryDto = rxDto.getDenaryClaim();
        ClaimLightWeightDTO otherDto = rxDto.getOther();

        if (primaryDto != null) {
            result.setId(primaryDto.getClaimId());
            result.setBillable(primaryDto.getTotalBillable());
            result.setAllowable(primaryDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        }
        if (primaryDto == null && secondaryDto != null) {
            result.setId(secondaryDto.getClaimId());
            result.setBillable(secondaryDto.getTotalBillable());
            result.setAllowable(secondaryDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        } else if (secondaryDto != null) {
            result.setId(secondaryDto.getClaimId());
        }
        if (primaryDto == null && secondaryDto == null && tertiaryDto != null) {
            result.setId(tertiaryDto.getClaimId());
            result.setBillable(tertiaryDto.getTotalBillable());
            result.setAllowable(tertiaryDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        } else if (tertiaryDto != null) {
            result.setId(tertiaryDto.getClaimId());
        }
        if (primaryDto == null && secondaryDto == null && tertiaryDto == null && quaternaryDto != null) {
            result.setId(quaternaryDto.getClaimId());
            result.setBillable(quaternaryDto.getTotalBillable());
            result.setAllowable(quaternaryDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        } else if (quaternaryDto != null) {
            result.setId(quaternaryDto.getClaimId());
        }
        if (primaryDto == null && secondaryDto == null && tertiaryDto == null && quaternaryDto == null && quinaryDto != null) {
            result.setId(quinaryDto.getClaimId());
            result.setBillable(quinaryDto.getTotalBillable());
            result.setAllowable(quinaryDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        } else if (quinaryDto != null) {
            result.setId(quinaryDto.getClaimId());
        }
        if (primaryDto == null && secondaryDto == null && tertiaryDto == null && quaternaryDto == null && quinaryDto == null
                && senaryDto != null) {
            result.setId(senaryDto.getClaimId());
            result.setBillable(senaryDto.getTotalBillable());
            result.setAllowable(senaryDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        } else if (senaryDto != null) {
            result.setId(senaryDto.getClaimId());
        }
        if (primaryDto == null && secondaryDto == null && tertiaryDto == null && quaternaryDto == null && quinaryDto == null
                && senaryDto == null && septenaryDto != null) {
            result.setId(septenaryDto.getClaimId());
            result.setBillable(septenaryDto.getTotalBillable());
            result.setAllowable(septenaryDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        } else if (septenaryDto != null) {
            result.setId(septenaryDto.getClaimId());
        }
        if (primaryDto == null && secondaryDto == null && tertiaryDto == null && quaternaryDto == null && quinaryDto == null
                && senaryDto == null && septenaryDto == null && octonaryDto != null) {
            result.setId(octonaryDto.getClaimId());
            result.setBillable(octonaryDto.getTotalBillable());
            result.setAllowable(octonaryDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        } else if (octonaryDto != null) {
            result.setId(octonaryDto.getClaimId());
        }
        if (primaryDto == null && secondaryDto == null && tertiaryDto == null && quaternaryDto == null && quinaryDto == null
                && senaryDto == null && septenaryDto == null && octonaryDto == null && nonaryDto != null) {
            result.setId(nonaryDto.getClaimId());
            result.setBillable(nonaryDto.getTotalBillable());
            result.setAllowable(nonaryDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        } else if (nonaryDto != null) {
            result.setId(nonaryDto.getClaimId());
        }
        if (primaryDto == null && secondaryDto == null && tertiaryDto == null && quaternaryDto == null && quinaryDto == null
                && senaryDto == null && septenaryDto == null && octonaryDto == null && nonaryDto == null && denaryDto != null) {
            result.setId(denaryDto.getClaimId());
            result.setBillable(denaryDto.getTotalBillable());
            result.setAllowable(denaryDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        } else if (denaryDto != null) {
            result.setId(denaryDto.getClaimId());
        }
        if (primaryDto == null && secondaryDto == null && tertiaryDto == null && quaternaryDto == null && quinaryDto == null
                && senaryDto == null && septenaryDto == null && octonaryDto == null && nonaryDto == null && denaryDto == null
                && otherDto != null) {
            result.setId(otherDto.getClaimId());
            result.setBillable(otherDto.getTotalBillable());
            result.setAllowable(otherDto.getTotalAllowable());
            result.setContractualAdjustment(result.getBillable().subtract(result.getAllowable()).multiply(BigDecimal.valueOf(-1)));
        } else if (otherDto != null) {
            result.setId(otherDto.getClaimId());
        }
        List<AppliedPaymentLCodesAmountsDTO> prescriptionAplcList = new ArrayList<>(buildPaymentDataForDto(rxDto.getPrescriptionId(), Date.valueOf("1990-01-01"), endDate, dateOption));

        result = processAppliedPaymentAndAdjustments(result, prescriptionAplcList);

        if (result.getContractualAdjustmentDiffTotals().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal tempAllowable = result.getBillable().add(result.getContractualAdjustmentDiffTotals());
            BigDecimal tempContractualDiffTotal = result.getAllowable().subtract(tempAllowable);
            result.setContractualAdjustmentDiffTotals(tempContractualDiffTotal.negate());
            result.setAccountsReceivables(calculateAccountReceivables(result, false));
        }
        return result;
    }

    private ArDataDTO addArDataDtoToExistingArDataDto(ArDataDTO existing, ArDataDTO arDataDTO) {
        ArDataDTO result = existing;
        result.setBillable(result.getBillable().add(arDataDTO.getBillable()));
        result.setAllowable(result.getAllowable().add(arDataDTO.getAllowable()));
        result.setContractualAdjustment(result.getContractualAdjustment().add(arDataDTO.getContractualAdjustment()));
        result.setArLevelAdjustments(result.getArLevelAdjustments().add(arDataDTO.getArLevelAdjustments()));
        result.setAppliedPaymentsTotals(result.getAppliedPaymentsTotals().add(arDataDTO.getAppliedPaymentsTotals()));
        result.setAppliedAdjustmentsTotals(result.getAppliedAdjustmentsTotals().add(arDataDTO.getAppliedAdjustmentsTotals()));
        result.setContractualAdjustmentDiffTotals(result.getContractualAdjustmentDiffTotals().add(arDataDTO.getContractualAdjustmentDiffTotals()));
        result.setAccountsReceivables(calculateAccountReceivables(result, false));
        return result;
    }

    @Deprecated
    public Map<Long, PrescriptionClaimsDTO> processPrescriptionClaimDtoARAging
            (Map<Long, PrescriptionClaimsDTO> rxDtoMap, Date asOfDate, String dateOption) {
        Map<Long, PrescriptionClaimsDTO> result = new HashMap<>();
        for (Map.Entry<Long, PrescriptionClaimsDTO> e : rxDtoMap.entrySet()) {
            String type = null;
            PrescriptionClaimsDTO temp = e.getValue();
            ArDataDTO ardto = processPrescriptionArDto(temp, asOfDate, dateOption);
            if (temp.getPrimaryClaim() != null) {
                temp.setPrimaryClaim(overwriteLightWeightWithPrimaryData(temp.getPrimaryClaim(), temp.getPrimaryClaim(), ardto));
                type = "primary";
            }
            if (temp.getSecondaryClaim() != null) {
                temp.setSecondaryClaim(overwriteLightWeightWithPrimaryData(temp.getSecondaryClaim(), temp.getPrimaryClaim(), ardto));
                type = "secondary";
            }
            if (temp.getTertiaryClaim() != null) {
                temp.setTertiaryClaim(overwriteLightWeightWithPrimaryData(temp.getTertiaryClaim(), temp.getPrimaryClaim(), ardto));
                type = "tertiary";
            }
            if (temp.getQuaternaryClaim() != null) {
                temp.setQuaternaryClaim(overwriteLightWeightWithPrimaryData(temp.getQuaternaryClaim(), temp.getPrimaryClaim(), ardto));
                type = "quaternary";
            }
            if (temp.getQuinaryClaim() != null) {
                temp.setQuinaryClaim(overwriteLightWeightWithPrimaryData(temp.getQuinaryClaim(), temp.getPrimaryClaim(), ardto));
                type = "quinary";
            }
            if (temp.getSenaryClaim() != null) {
                temp.setSenaryClaim(overwriteLightWeightWithPrimaryData(temp.getSenaryClaim(), temp.getPrimaryClaim(), ardto));
                type = "senary";
            }
            if (temp.getSeptenaryClaim() != null) {
                temp.setSeptenaryClaim(overwriteLightWeightWithPrimaryData(temp.getSeptenaryClaim(), temp.getPrimaryClaim(), ardto));
                type = "septenary";
            }
            if (temp.getOctonaryClaim() != null) {
                temp.setOctonaryClaim(overwriteLightWeightWithPrimaryData(temp.getOctonaryClaim(), temp.getPrimaryClaim(), ardto));
                type = "octonary";
            }
            if (temp.getNonaryClaim() != null) {
                temp.setNonaryClaim(overwriteLightWeightWithPrimaryData(temp.getNonaryClaim(), temp.getPrimaryClaim(), ardto));
                type = "nonary";
            }
            if (temp.getDenaryClaim() != null) {
                temp.setDenaryClaim(overwriteLightWeightWithPrimaryData(temp.getDenaryClaim(), temp.getPrimaryClaim(), ardto));
                type = "denary";
            }
            if (temp.getOther() != null) {
                temp.setOther(overwriteLightWeightWithPrimaryData(temp.getOther(), temp.getPrimaryClaim(), ardto));
                type = "other";
            }
            if (temp.getPatientBalance() != null) {
                temp.setPatientBalance(overwriteLightWeightWithPrimaryData(temp.getPatientBalance(), temp.getPrimaryClaim(), ardto));
            }
            temp.setClaimWithBalance(type);
            temp.setArDataDTO(ardto);
            result.put(e.getKey(), temp);
        }
        return result;
    }

    private ClaimLightWeightDTO overwriteLightWeightWithPrimaryData(ClaimLightWeightDTO existing, ClaimLightWeightDTO primary, ArDataDTO dto) {
        ClaimLightWeightDTO result = existing;
        if (primary != null) {
            existing.setSubmissionDate(primary.getSubmissionDate());
            existing.setAging(primary.getAging());
        }
        existing.setArDataDTO(dto);
        return result;

    }

    public List<DailySalesOutstandingWidgetDTO> dailySalesOutstandingWidget(Date startDate, List<Long> branchIds) {
        List<DailySalesOutstandingWidgetDTO> results = new ArrayList<>();
        LocalDate startLocalDate = startDate.toLocalDate();
        LocalDate today = LocalDate.now();
        // ChronoUnit.DAYS.between() is EXCLUSIVE of the end date, so we add 1 to account for today
        BigDecimal numberOfDaysBetween = BigDecimal.valueOf(ChronoUnit.DAYS.between(startLocalDate, today.plusDays(1)));
        for (Long branchId : branchIds) {
            DailySalesOutstandingWidgetDTO dto = new DailySalesOutstandingWidgetDTO();
            Branch branch = branchService.findOne(branchId);
            ArAgingReportDTO beginningARDTO = arAgingReport(Date.valueOf(startLocalDate), branchId, "payment");
            BigDecimal beginningAR = beginningARDTO.getTotalAccountsReceivables().setScale(2, RoundingMode.HALF_UP);
            ArAgingReportDTO endingARDTO = arAgingReport(Date.valueOf(today), branchId, "payment");
            BigDecimal endingAR = endingARDTO.getTotalAccountsReceivables().setScale(2, RoundingMode.HALF_UP);
            BigDecimal totalCreditSales = endingARDTO.getTotalAllowable().subtract(beginningARDTO.getTotalAllowable()).setScale(2, RoundingMode.HALF_UP);
            BigDecimal dailySalesOutstanding = calculateDailySalesOutstanding(beginningAR, endingAR, totalCreditSales, numberOfDaysBetween);

            dto.setBranchName(branch.getName());
            dto.setDaysOutstanding(dailySalesOutstanding);
            results.add(dto);
        }
        return results;
    }

    public BigDecimal calculateDailySalesOutstanding(BigDecimal beginningAR, BigDecimal endingAR,
                                                     BigDecimal totalCreditSales, BigDecimal numberOfDays) {
        if (totalCreditSales.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal averageAR = beginningAR.add(endingAR).divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);
        BigDecimal result = averageAR.divide(totalCreditSales, 5, RoundingMode.HALF_UP).multiply(numberOfDays).setScale(2, RoundingMode.HALF_UP);
        return result;
    }

    public List<ReferringPhysicianBillingTotalDTO> billingsByReferringPhysician(java.sql.Date startDate, java.sql.Date endDate, Long branchId, String usePatientBranch) {
        // Transcend has requested for this report to be changed to a detil view so I'm going to just comment out the
        // aggregation code instead of deleting it so we can come back and change it if needed. - Chris
        // Map<String, ReferringPhysicianBillingTotalDTO> totalsMap = new HashMap<>();

        List<ReferringPhysicianBillingTotalDTO> dtoList = new ArrayList<>();

        List<ClaimInsuranceVerificationLCodesDTO> claimInsuranceVerificationLCodesDTOList = getClaimInsuranceVerificationLCodesDTOList(startDate, endDate, branchId, true, true, "true".equals(usePatientBranch));
        for (ClaimInsuranceVerificationLCodesDTO claimInsuranceVerificationLCodesDTO : claimInsuranceVerificationLCodesDTOList) {
            Physician physician = claimInsuranceVerificationLCodesDTO.getClaim().getPrescription().getReferringPhysician();
            if (physician != null && claimInsuranceVerificationLCodesDTO.getHasFirstClaimSubmission()) {
                ArIvlcPeriodTotals ivlcTotal = insuranceVerificationLCodeService.getIvlcTotalsByClaimId(claimInsuranceVerificationLCodesDTO.getClaim().getId());
                ReferringPhysicianBillingTotalDTO dto = new ReferringPhysicianBillingTotalDTO();
                dto.setClaim(claimInsuranceVerificationLCodesDTO.getClaim());
                dto.setTreatingPractitioner(userService.getUserById(claimInsuranceVerificationLCodesDTO.getClaim().getPrescription().getTreatingPractitionerId()));
                dto.setReferringPhysician(physician);
                dto.setAddress(physician.getStreetAddress() + ", " + physician.getCity() + ", " + physician.getState());
                dto.setBillingTotal(ivlcTotal.getBillable());
                dto.setAllowableTotal(ivlcTotal.getAllowable());
                dto.setNpi(physician.getNpi());
                dto.setPhoneNumbers(phoneNumberRepository.getByPersonIdAndPersonType(physician.getId(), "physician"));
                dtoList.add(dto);
            }
        }
        return dtoList;
    }

    public String salesDetailExport() throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("GL Account,Claim#,Rx#,Device Type,Practitioner,Patient,Insurance,Service Date,Submission Date,Amount,Credit/Debit,Primary Care Physician,Referring Physician,Therapist,Facility\n");
        Map<String, SalesDetailDTO> salesDetailMap = salesDetail(DateUtil.getDate("2000-01-01", Constants.DF_YYYY_MM_DD),
                DateUtil.getDate(DateUtil.getStringDate(DateUtil.getCurrentDateDB()), Constants.DF_YYYY_MM_DD), 0L, "false");

        String salesTaxAccount = glAccountRepository.findByName("sales_tax").getAccount();
        String salesContractAccount = glAccountRepository.findByName("sales_contract").getAccount();
        String salesBillableAccount = glAccountRepository.findByName("billable").getAccount();
        String orthoticDtAccount = glAccountRepository.findByName("orthotic").getAccount();
        String prostheticDtAccount = glAccountRepository.findByName("prosthetic").getAccount();
        String pedorthicsDtAccount = glAccountRepository.findByName("pedorthic").getAccount();
        String miscDtAccount = glAccountRepository.findByName("misc").getAccount();
        for (ClaimsSummaryDTO dto : salesDetailMap.get("orthotic").getClaimsSummaryDTOList()) {
            String branchGL = dto.getClaimsSummarySlimClaimDataDTO().getGlAccount();
            String sta = salesTaxAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", orthoticDtAccount);
            String sca = salesContractAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", orthoticDtAccount);
            String sba = salesBillableAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", orthoticDtAccount);
            sb.append(buildSalesDetailEntry(sta, sca, sba, dto));
        }
        for (ClaimsSummaryDTO dto : salesDetailMap.get("prosthetic").getClaimsSummaryDTOList()) {
            String branchGL = dto.getClaimsSummarySlimClaimDataDTO().getGlAccount();
            String sta = salesTaxAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", prostheticDtAccount);
            String sca = salesContractAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", prostheticDtAccount);
            String sba = salesBillableAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", prostheticDtAccount);
            sb.append(buildSalesDetailEntry(sta, sca, sba, dto));
        }
        for (ClaimsSummaryDTO dto : salesDetailMap.get("pedorthic").getClaimsSummaryDTOList()) {
            String branchGL = dto.getClaimsSummarySlimClaimDataDTO().getGlAccount();
            String sta = salesTaxAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", pedorthicsDtAccount);
            String sca = salesContractAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", pedorthicsDtAccount);
            String sba = salesBillableAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", pedorthicsDtAccount);
            sb.append(buildSalesDetailEntry(sta, sca, sba, dto));
        }
        for (ClaimsSummaryDTO dto : salesDetailMap.get("misc").getClaimsSummaryDTOList()) {
            String branchGL = dto.getClaimsSummarySlimClaimDataDTO().getGlAccount();
            String sta = salesTaxAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", miscDtAccount);
            String sca = salesContractAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", miscDtAccount);
            String sba = salesBillableAccount.replaceAll("&b&", branchGL).replaceAll("&dt&", miscDtAccount);
            sb.append(buildSalesDetailEntry(sta, sca, sba, dto));
        }
        return sb.toString();
    }

    public String buildSalesDetailEntry(String salesTaxAccount, String salesContractAccount, String
            salesBillableAccount, ClaimsSummaryDTO dto) {
        StringBuilder sb = new StringBuilder();
        sb.append("%account%").append(",");
        sb.append(dto.getClaimsSummarySlimClaimDataDTO().getClaimId()).append(",");
        sb.append(dto.getClaimsSummarySlimClaimDataDTO().getPrescriptionId()).append(",");
        sb.append(dto.getClaimsSummarySlimClaimDataDTO().getOrthoticOrProsthetic()).append(",");
        if (dto.getClaimsSummarySlimClaimDataDTO().getTreatingPractitionerId() != null) {
            FullName fn = new FullName(dto.getClaimsSummarySlimClaimDataDTO().getTreatingPractitionerFirstName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getTreatingPractitionerMiddleName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getTreatingPractitionerLastName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getTreatingPractitionerCredentials());
            sb.append(StringUtil.formatName(fn, "FMLC", false)).append(",");
        } else {
            sb.append(" ").append(",");
        }

        FullName fn = new FullName(dto.getClaimsSummarySlimClaimDataDTO().getPatientFirstName(), dto.getClaimsSummarySlimClaimDataDTO().getPatientMiddleName(), dto.getClaimsSummarySlimClaimDataDTO().getPatientLastName().replaceAll(", ", " "));
        sb.append(StringUtil.formatName(fn, "FML", false)).append(",");
        sb.append(dto.getClaimsSummarySlimClaimDataDTO().getClaimPatientInsuranceCompanyName().replace(",", " ")).append(",");
        sb.append(DateUtil.getStringDate(dto.getClaimsSummarySlimClaimDataDTO().getDateOfService(), Constants.DF_YYYY_MM_DD)).append(",");
        sb.append(DateUtil.getStringDate(dto.getSubmissionDate(), Constants.DF_YYYY_MM_DD)).append(",");
        sb.append("%amount%").append(",");
        sb.append("%cd%").append(",");

        if (dto.getClaimsSummarySlimClaimDataDTO().getPrimaryCarePhysicianId() != null) {
            FullName pcpFullName = new FullName(dto.getClaimsSummarySlimClaimDataDTO().getPrimaryCarePhysicianFirstName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getPrimaryCarePhysicianMiddleName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getPrimaryCarePhysicianLastName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getPrimaryCarePhysicianCredentials());
            sb.append(StringUtil.formatName(pcpFullName, "FMLC", false)).append(",");
        } else {
            sb.append(" ").append(",");
        }

        if (dto.getClaimsSummarySlimClaimDataDTO().getReferringPhysicianId() != null) {
            FullName referringPhysicianFullName = new FullName(dto.getClaimsSummarySlimClaimDataDTO().getReferringPhysicianFirstName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getReferringPhysicianMiddleName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getReferringPhysicianLastName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getPrimaryCarePhysicianCredentials());
            sb.append(StringUtil.formatName(referringPhysicianFullName, "FMLC", false)).append(",");
        } else {
            sb.append(" ").append(",");
        }

        if (dto.getClaimsSummarySlimClaimDataDTO().getTherapistId() != null) {
            FullName therapistFullName = new FullName(dto.getClaimsSummarySlimClaimDataDTO().getTherapistFirstName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getTherapistMiddleName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getTherapistLastName(),
                    dto.getClaimsSummarySlimClaimDataDTO().getTherapistCredentials());
            sb.append(StringUtil.formatName(therapistFullName, "FMLC", false)).append(",");
        } else {
            sb.append(" ").append(",");
        }
        sb.append(dto.getClaimsSummarySlimClaimDataDTO().getFacilityName() != null ? dto.getClaimsSummarySlimClaimDataDTO().getFacilityName() : "").append("\n");

        BigDecimal salesTax = dto.getArDataDTO().getSalesTax();
        String tax = salesTax.compareTo(BigDecimal.ZERO) == 0 ? "" : sb.toString()
                .replaceAll("%account%", salesTaxAccount)
                .replaceAll("%amount%", salesTax.toString())
                .replaceAll("%cd%", NumberUtil.compare(salesTax, "=", BigDecimal.ZERO) ? "" : NumberUtil.compare(salesTax, ">", BigDecimal.ZERO) ? "credit" : "debit");

        BigDecimal contractual = dto.getArDataDTO().getContractualAdjustment();
        String contract = sb.toString()
                .replaceAll("%account%", salesContractAccount)
                .replaceAll("%amount%", contractual.toString())
                .replaceAll("%cd%", NumberUtil.compare(contractual, "=", BigDecimal.ZERO) ? "" : NumberUtil.compare(contractual, ">", BigDecimal.ZERO) ? "credit" : "debit");

        BigDecimal billable = dto.getArDataDTO().getBillable();
        String bill = sb.toString()
            .replaceAll("%account%", salesBillableAccount)
            .replaceAll("%amount%", billable.toString())
            .replaceAll("%cd%", NumberUtil.compare(billable, "=", BigDecimal.ZERO) ? "" : NumberUtil.compare(billable, ">", BigDecimal.ZERO) ? "credit" : "debit");

        return tax.concat(contract).concat(bill);
    }


    public List<AppliedPayment_L_Code> populateAppliedPaymentLCodeList(DataModel dm) {
        HashMap<Long, List<AppliedPayment_L_Code>> appliedPaymentLCodeMap = new HashMap<>();
        List<AppliedPayment_L_Code> appliedPaymentLCodeList = new ArrayList<>();
        for (Object[] row : dm.getData()) {
            AppliedPayment appliedPayment = new AppliedPayment();
            appliedPayment.setId(dm.getLong("ap.idasap_id", row));
            appliedPayment.setAdjustmentApplied(dm.getBigDecimal("ap.adjustment_applied", row));
            appliedPayment.setAmountApplied(dm.getBigDecimal("ap.amount_applied", row));
            appliedPayment.setAppliedBy(dm.getLong("ap.applied_by", row));
            appliedPayment.setAppliedDate(dm.getSqlDate("ap.applied_date", row));
            appliedPayment.setAutoPostMessage(dm.getString("ap.auto_post_message", row));
            appliedPayment.setClaimId(dm.getLong("ap.claim_id", row));
            appliedPayment.setIcn(dm.getString("ap.icn", row));
            appliedPayment.setNextResponsibility(dm.getLong("ap.next_responsibility", row));
            appliedPayment.setPaymentId(dm.getLong("ap.payment_id", row));
            appliedPayment.setTaskNotes(dm.getString("ap.task_notes", row));

            AppliedPayment_L_Code appliedPaymentLCode = new AppliedPayment_L_Code();
            appliedPaymentLCode.setId(dm.getLong("aplc.idasaplc_id", row));
            appliedPaymentLCode.setAdjustment(dm.getBigDecimal("aplc.adjustment", row));
            appliedPaymentLCode.setAdjustmentType(dm.getLong("aplc.adjustment_type", row));
            appliedPaymentLCode.setAllowable(dm.getBigDecimal("aplc.allowable", row));
            appliedPaymentLCode.setAmount(dm.getBigDecimal("aplc.amount", row));
            appliedPaymentLCode.setAppliedPaymentId(dm.getLong("aplc.applied_payment_id", row));
            appliedPaymentLCode.setAppliedPayment(appliedPayment);
            appliedPaymentLCode.setEndingBalance(dm.getBigDecimal("aplc.ending_balance", row));
            appliedPaymentLCode.setPrescriptionLCodeId(dm.getLong("aplc.prescription_l_code_id", row));
            appliedPaymentLCode.setStartingBalance(dm.getBigDecimal("aplc.starting_balance", row));
            appliedPaymentLCode.setAdjustmentType1(dm.getString("aplc.adjustment_type1", row));
            appliedPaymentLCode.setAdjustmentAmount1(dm.getBigDecimal("aplc.adjustment_amount1", row));
            appliedPaymentLCode.setAdjustmentType2(dm.getString("aplc.adjustment_type2", row));
            appliedPaymentLCode.setAdjustmentAmount2(dm.getBigDecimal("aplc.adjustment_amount2", row));
            appliedPaymentLCode.setAdjustmentType3(dm.getString("aplc.adjustment_type3", row));
            appliedPaymentLCode.setAdjustmentAmount3(dm.getBigDecimal("aplc.adjustment_amount3", row));
            appliedPaymentLCode.setAdjustmentType4(dm.getString("aplc.adjustment_type4", row));
            appliedPaymentLCode.setAdjustmentAmount4(dm.getBigDecimal("aplc.adjustment_amount4", row));
            appliedPaymentLCode.setAdjustmentType5(dm.getString("aplc.adjustment_type5", row));
            appliedPaymentLCode.setAdjustmentAmount5(dm.getBigDecimal("aplc.adjustment_amount5", row));

            Payment payment = new Payment();
            payment.setPayerType(dm.getString("p.payer_type", row));
            appliedPayment.setPayment(payment);

            String operation = dm.getString("a.operation", row);
            if (operation != null) {
                Adjustment adjustment = new Adjustment();
                adjustment.setOperation(operation);
                adjustment.setWithdraw(dm.getBoolean("a.withdraw", row));
                appliedPaymentLCode.setOperator(adjustment);
            }

            Claim claim = new Claim();
            claim.setId(dm.getLong("ap.claim_id", row));
            Prescription prescription = new Prescription();
            DeviceType deviceType = new DeviceType();
            deviceType.setOrthoticOrProsthetic(dm.getString("dt.orthotic_or_prosthetic", row));
            prescription.setDeviceType(deviceType);
            claim.setPrescription(prescription);
            appliedPayment.setClaim(claim);

            appliedPaymentLCodeList.add(appliedPaymentLCode);
        }
        return appliedPaymentLCodeList;
    }

    public PurchasingHistoryReport getPurchasingHistoryReport(Long branchId, Long vendorId, Long itemId,
                                                              String partNumber, Date startDate, Date endDate) {
        PurchasingHistoryReport result = new PurchasingHistoryReport();
        List<PurchaseOrder_Item> purchaseOrderItems;
        if (branchId != 0 || vendorId != null || itemId != null || !StringUtil.isBlank(partNumber) || (startDate != null && endDate != null)) {
            Specification<PurchaseOrder_Item> spec = PurchaseOrderItemSpecs.search(branchId, vendorId, itemId, partNumber, startDate, endDate);
            purchaseOrderItems = purchaseOrderItemService.findAll(spec);
        } else {
            purchaseOrderItems = purchaseOrderItemService.findAll();
        }
        List<PurchasingHistoryReportRow> rows = new ArrayList<>();
        BigDecimal totalItemCost = BigDecimal.ZERO;
        Long totalQuantity = 0L;
        for (PurchaseOrder_Item purchaseOrderItem : purchaseOrderItems) {
            PurchasingHistoryReportRow row = new PurchasingHistoryReportRow();
            row.setBranchId(purchaseOrderItem.getBranchId());
            row.setBranchName(purchaseOrderItem.getBranchId() != null ? purchaseOrderItem.getBranch().getName() : null);
            row.setVendorName(purchaseOrderItem.getVendorId() != null ? purchaseOrderItem.getVendor().getName() : null);
            row.setVendorId(purchaseOrderItem.getVendorId());
            row.setManufacturerName(purchaseOrderItem.getItemId() != null && purchaseOrderItem.getItem().getManufacturerId() != null ? purchaseOrderItem.getItem().getManufacturer().getName() : null);
            row.setManufacturerId(purchaseOrderItem.getItemId() != null ? purchaseOrderItem.getItem().getManufacturerId() : null);
            row.setPartNumber(purchaseOrderItem.getItemId() != null ? purchaseOrderItem.getItem().getPartNumber() : null);
            row.setItemName(purchaseOrderItem.getItemId() != null ? purchaseOrderItem.getItem().getName() : null);
            row.setItemId(purchaseOrderItem.getItemId());
            row.setItemDescription(purchaseOrderItem.getItemId() != null ? purchaseOrderItem.getItem().getDescription() : null);
            row.setLCodeCategoryName(purchaseOrderItem.getItemId() != null && purchaseOrderItem.getItem().getLCodeCategoryId() != null ? purchaseOrderItem.getItem().getLCodeCategory().getCategory() : null);
            row.setLCodeCategoryId(purchaseOrderItem.getItemId() != null ? purchaseOrderItem.getItem().getLCodeCategoryId() : null);
            row.setPurchaseOrderItemType(purchaseOrderItem.getType());
            row.setItemCost(purchaseOrderItem.getItemCost());
            row.setQuantity(purchaseOrderItem.getQuantity());
            row.setTotalCost(purchaseOrderItem.getItemCost() != null && purchaseOrderItem.getQuantity() != null ? purchaseOrderItem.getItemCost().multiply(BigDecimal.valueOf(row.getQuantity())) : null);
            if (row.getItemCost() == null) row.setItemCost(BigDecimal.ZERO);
            if (row.getQuantity() == null) row.setQuantity(0L);
            if (row.getTotalCost() == null) row.setTotalCost(BigDecimal.ZERO);

            String purchaseOrderNumberWithPrefix = null;
            if(purchaseOrderItem.getPurchaseOrderId() != null  && purchaseOrderItem.getBranch() != null && purchaseOrderItem.getBranch().getPurchaseOrderPrefix() != null)
                purchaseOrderNumberWithPrefix = purchaseOrderItem.getBranch().getPurchaseOrderPrefix() + ' ' + purchaseOrderItem.getPurchaseOrderId();
            else if(purchaseOrderItem.getPurchaseOrderId() != null)
                purchaseOrderNumberWithPrefix = purchaseOrderItem.getPurchaseOrderId().toString();
            row.setOrderNumber(purchaseOrderNumberWithPrefix);

            row.setRxId(purchaseOrderItem.getPrescriptionId());
            row.setItemStatus(purchaseOrderItem.getStatus());
            if (purchaseOrderItem.getPurchaseOrderId() != null) {
            	PurchaseOrder order = purchaseOrderItem.getPurchaseOrder();
            	purchaseOrderService.loadForeignKeys(order);

            	row.setBranchAddress(order.getStreetAddress());
            	row.setBranchCity(order.getCity());
            	row.setBranchState(order.getState());
            	row.setBranchZip(order.getZipcode());

            	row.setAddCharges(order.getAdditionalCharges() != null ? order.getAdditionalCharges() : BigDecimal.ZERO);
            	row.setDiscount(order.getDiscount() != null ? order.getDiscount() : BigDecimal.ZERO);
            	row.setShippingCharges(order.getShippingCharges() != null ? order.getShippingCharges() : BigDecimal.ZERO);
            	row.setSalesTax(order.getSalesTax() != null ? order.getSalesTax() : BigDecimal.ZERO);
            	row.setGrandTotal(order.getTotalCost() != null ? order.getTotalCost() : BigDecimal.ZERO); // order.getSubTotal()

            	row.setDateCreated(Date.valueOf(order.getCreatedAt().toLocalDateTime().toLocalDate()));
            	row.setDateOrdered(order.getOrderedAt());

                row.setDeliveryLocation(order.getLocationId());
            	row.setOrderStatus(order.getStatus());
            	if (order.getOrderedById() != null && order.getOrderedBy() != null)
            		row.setOrderedByUser(order.getOrderedBy().getFirstAndLastName());

            	if (purchaseOrderItem.getPatientId() != null) {
            		row.setPatientId(purchaseOrderItem.getPatientId());
            		if (purchaseOrderItem.getPatient() != null)
            			row.setPatientName(purchaseOrderItem.getPatient().getFirstAndLastName());
            	}
            }
            if (purchaseOrderItem.getItemId() != null && purchaseOrderItem.getItem() != null) {
            	Item item = purchaseOrderItem.getItem();
                row.setSkuNumber(item.getSku());
                if (item.getDeviceTypes() != null && item.getDeviceTypes().size() > 0)
                	row.setDeviceTypeName(item.getDeviceTypes().get(0).getName());
            }
            row.setPatientName(purchaseOrderItem.getPatient() != null ? purchaseOrderItem.getPatient().getFirstAndLastName() : null);
            row.setPatientId(purchaseOrderItem.getPatientId());
            row.setDeviceTypeName(purchaseOrderItem.getPrescriptionId() != null ? purchaseOrderItem.getPrescription().getDeviceType().getName() : null);
            row.setInsuranceCompanyName(purchaseOrderItem.getPrescriptionId() != null &&
                    purchaseOrderItem.getPrescription().getPatientInsuranceId() != null &&
                    purchaseOrderItem.getPrescription().getPatientInsurance().getInsuranceCompanyId() != null ?
                    purchaseOrderItem.getPrescription().getPatientInsurance().getInsuranceCompany().getName() : null);
            String lCodeNames = null;
            if (purchaseOrderItem.getItemId() != null && purchaseOrderItem.getItem().getLCodes() != null) {
                List<String> names = purchaseOrderItem.getItem().getLCodes()
                        .stream().map(L_Code::getName).collect(Collectors.toList());
                lCodeNames = String.join(", ", names);
                if (purchaseOrderItem.getQuantity() != null)
                    totalQuantity = totalQuantity + purchaseOrderItem.getQuantity();
                if (purchaseOrderItem.getItemCost() != null && purchaseOrderItem.getQuantity() != null)
                    totalItemCost = totalItemCost.add(purchaseOrderItem.getItemCost().multiply(BigDecimal.valueOf(row.getQuantity())));
            }
            row.setLCodeNames(lCodeNames);
            row.setPractitionerId(purchaseOrderItem.getPractitionerId());
            row.setPractitionerName(purchaseOrderItem.getPractitionerId() != null && purchaseOrderItem.getPractitioner() != null ?
                    purchaseOrderItem.getPractitioner().getFirstAndLastName() : null);
            rows.add(row);
        }
        result.setData(rows);
        result.setTotalItemCost(totalItemCost);
        result.setTotalQuantity(totalQuantity);
        return result;
    }

}
