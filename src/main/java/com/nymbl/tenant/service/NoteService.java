package com.nymbl.tenant.service;

import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.service.VersionExceptionLockInterface;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.interfaces.INote;
import com.nymbl.tenant.interfaces.repository.INoteRepository;
import com.nymbl.tenant.model.Note;
import com.nymbl.tenant.model.PatientIntake;
import com.nymbl.tenant.model.SystemSetting;
import com.nymbl.tenant.model.UserRole;
import com.nymbl.tenant.model.interfaces.NoteInfo;
import com.nymbl.tenant.repository.NoteRepository;
import com.nymbl.tenant.repository.UserRoleRepository;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.nymbl.config.Constants.DF_YYYY_MM_DD_HH_MM;
import static com.nymbl.config.utils.OptimisticLockingUtil.SAVED;

/**
 * Created by Bradley Moore on 10/19/2017.
 */
@Service
public class NoteService extends AbstractTableService<Note, Long> implements VersionExceptionLockInterface<Note> {

    private final NoteRepository noteRepository;
    private final UserService userService;
    private final UserRoleRepository userRoleRepository;
    private final NotificationService notificationService;
    private final PrescriptionService prescriptionService;
    private final SystemSettingService systemSettingService;
    private final AppointmentService appointmentService;
    private final INoteRepository noteIRepository;

    @Autowired
    private OptimisticLockingUtil logUtils;


    @Autowired
    public NoteService(NoteRepository noteRepository,
                       UserService userService,
                       UserRoleRepository userRoleRepository,
                       NotificationService notificationService,
                       @Lazy PrescriptionService prescriptionService,
                       SystemSettingService systemSettingService,
                       @Lazy AppointmentService appointmentService, INoteRepository noteIRepository) {
        super(noteRepository);
        this.noteRepository = noteRepository;
        this.userService = userService;
        this.userRoleRepository = userRoleRepository;
        this.notificationService = notificationService;
        this.prescriptionService = prescriptionService;
        this.systemSettingService = systemSettingService;
        this.appointmentService = appointmentService;
        this.noteIRepository = noteIRepository;
    }

    public List<Note> getByAppointmentId(Long appointmentId) {
        List<Note> results = noteRepository.getByAppointmentId(appointmentId);
        for (Note o : results)
            loadForeignKeys(o);
        return results;
    }

    public List<Note> getByPatientIdForPatientProfile(Long patientId) {
        SystemSetting useNoteSubjectSetting = systemSettingService.findBySectionAndField("general", "use_note_subject");
        Boolean useNoteSubject = useNoteSubjectSetting.getValue().equals("Y");
        List<Note> notes = getByPatientId(patientId, useNoteSubject, !useNoteSubject);
        Collections.reverse(notes);
        List<Note> results = orderNotes(notes, true);
        return results;
    }

    public List<Note> getByPatientId(Long patientId, Boolean nullifyNote, Boolean showBodyPreview) {
        List<Note> results = noteRepository.getByPatientId(patientId);
        for (Note o : results) {
            loadForeignKeys(o);
            if (nullifyNote) {
                o.setNote(null);
            } else if (showBodyPreview) {
                String body = o.getNote();
                String parsedBody = body != null ? Jsoup.parse(body).text() : "";
                int endIndex = parsedBody.length() > 200 ? 200 : parsedBody.length();
                o.setNote(parsedBody);
                o.setNote(parsedBody.substring(0, endIndex));
            }
        }
        return results;
    }

    public List<Note> getByPrescriptionId(Long prescriptionId) {
        List<Note> results = noteRepository.getByPrescriptionId(prescriptionId);
        for (Note o : results)
            loadForeignKeys(o);
        return results;
    }

    public List<Note> getByAdvanceBeneficiaryNoticeId(Long advanceBeneficiaryNoticeId) {
        List<Note> results = noteRepository.getByAdvanceBeneficiaryNoticeId(advanceBeneficiaryNoticeId);
        for (Note o : results)
            loadForeignKeys(o);
        return results;
    }

    public List<Note> getByPrescriptionIdAndNoteType(Long prescriptionId, String noteType) {
        List<Note> results = noteRepository.getByPrescriptionIdAndNoteType(prescriptionId, noteType);
        for (Note o : results) {
            loadForeignKeys(o);
        }
        return results;
    }

    public List<Note> getByCreatedAtBetween(Date startDate, Date endDate) {
        List<Note> results = noteRepository.getByCreatedAtBetween(startDate, endDate);
        for (Note o : results)
            loadForeignKeys(o);
        return results;
    }

    public List<Note> getByUserIdAndCosignedIsNullAndPrescriptionIdIsNotNullAndCreatedAtBetween(Long userId, Date startDate, Date endDate, Long branchId) {
        List<Long> userIdList = new ArrayList<>();

        // Only users with the "Care Extender" role need to have their notes approved. So if a user is not provided then
        // the report looks for all care extenders in the company.

        if (userId == null) {

            // This method was previously using the incorrect role Id. So instead of hardcoding the ID I'm hardcoding
            // the "Care Extender" role name and using a new getActiveUsersByRoleNameAndCompany method. This way the
            // report still pulls users that are care extenders even if the role id changes on nymbl_master.

            String roleName = "Care Extender";
            List<User> temp = userService.getActiveUsersByRoleNameAndCompany(roleName, userService.getCurrentCompany().getId());
            userIdList = temp.stream().map(User::getId).collect(Collectors.toList());
        } else {
            userIdList.add(userId);
        }

        // Run query to get results list
        List<Note> results = noteRepository.getByUserIdAndCosignedIsNullAndPrescriptionIdIsNotNullAndCreatedAtBetween(userIdList, startDate, endDate, branchId, "clinical");

        for (Note o : results) {
            loadForeignKeys(o);
        }

        return results;
    }

    public List<Note> getByUserIdAndCreatedAtBetweenAndBranchIdAndNoteType(Long userId, Date startDate, Date endDate, Long branchId, String noteType) {
        String dateStart = DateUtil.getStringDate(startDate, DF_YYYY_MM_DD_HH_MM);
        String dateEnd = DateUtil.getStringDate(DateUtil.getEndOfDay(endDate), DF_YYYY_MM_DD_HH_MM);
//        Instant start = Instant.now();
//        System.out.println("noteRepository.getByUserIdAndCreatedAtBetweenAndBranchIdAndNoteTypeQuery = \n" + noteRepository.getByUserIdAndCreatedAtBetweenAndBranchIdAndNoteTypeQuery
//            .replaceAll(":userId", userId + "")
//            .replaceAll(":startDate", "'" + dateStart + "'")
//            .replaceAll(":endDate", "'" + dateEnd + "'")
//            .replaceAll(":branchId", branchId + "")
//            .replaceAll(":noteType", "'"+noteType + "'")
//        );
        List<Note> results = noteRepository.getByUserIdAndCreatedAtBetweenAndBranchIdAndNoteType(userId, dateStart, dateEnd, branchId, noteType);
//        Instant end1 = Instant.now();
//        System.out.println("results - Time taken: "+ Duration.between(start, end1).toMillis() +" milliseconds");
        results.forEach(n -> {
            if (n.getNote() != null) {
                n.setNote(n.getNote().replaceAll("<.+?>", " "));
            }

            if (n.getNoteType().equals("general")) {
                n.setNoteType("General");
            } else if (n.getNoteType().equals("clinical")) {
                n.setNoteType("Clinical");
            } else if (n.getNoteType().equals("billing")) {
                n.setNoteType("Billing");
            } else if (n.getNoteType().equals("patient_summary")) {
                n.setNoteType("RX Summary");
            } else if (n.getNoteType().equals("billing_ar")) {
                n.setNoteType("AR Note");
            } else if (n.getNoteType().equals("complaint")) {
                n.setNoteType("Complaint");
            }
        });
//        Instant end2 = Instant.now();
//        System.out.println("Update Note Type - Time taken: "+ Duration.between(end1, end2).toMillis() +" milliseconds");

        for (Note o : results) {
            loadForeignKeys(o);
        }
//        System.out.println("Load foreign keys - Time taken: "+ Duration.between(end2, Instant.now()).toMillis() +" milliseconds");
//        System.out.println("Get Data - Time taken: "+ Duration.between(start, Instant.now()).toMillis() +" milliseconds");

        return results;
    }

    public Map<String, Object> saveNote(Note note, String action) {
        if (note.getPrescriptionId() != null && note.getPrescriptionId() == 0) {
            note.setPrescriptionId(null);
        }

        // Load prescription and users tied to note
        loadForeignKeys(note);

        // Get current user to check roles
        User currentUser = userService.getCurrentUser();
        Long currentUserId = note.getUserId();
        if (null != currentUser) {
            currentUserId = currentUser.getId();
        }
        List<UserRole> userRoles = userRoleRepository.findByUserId(currentUserId);
        Boolean isResident = userRoles.stream().anyMatch(o -> o.getRoleId().equals(5L));

        // could be supervisor on appt or TP on Rx
        Boolean isUserTreatingPractitionerOnNote = note.getTreatingPractitioner() != null
                && currentUserId.equals(note.getTreatingPractitionerId());

        // Set resident_id if user saving note is not already set
        if (isResident && !isUserTreatingPractitionerOnNote) {
            note.setResidentId(currentUserId);
        }

        note.setPublishedAt(determinePublishedAt(note));
        if (action.equals("sign")) {
            note.setUserSignedAt(new Timestamp(Calendar.getInstance().getTimeInMillis()));
        }
        if (action.equals("unsign")) {
            note.setUserSignedAt(null);
        }
        if (action.equals("publish") && note.getNoteType().equals("clinical")
                && (note.getCosigned() == null || !note.getCosigned())) {
            note.setCosigned(true);
        }
        if (action.equals("unpublish") && note.getNoteType().equals("clinical")
                && (note.getCosigned() == null || note.getCosigned())) {
            note.setCosigned(false);
        }
        if (note.getChildType() == null && note.getParentId() != null) {
            note.setChildType("addendum");
        }
        if (isResident
                && !isUserTreatingPractitionerOnNote
                && note.getUserSignedAt() != null
                && action.equals("sign")
                && note.getNoteType().equals("clinical")
                && !note.getPublished()
                && note.getTreatingPractitioner() != null
                && note.getTreatingPractitioner().getNotificationTypes() != null
                && (note.getCosigned() == null || !note.getCosigned())) {
            notificationService.createApproveNoteNotification(note);
        }

        return saveForVersion(note);
    }

    public Note save(Note note) { // TODO: Look into deprecating this
        if (note.getId() == null && note.getCreatedById() == null) {
            note.setCreatedById(userService != null && userService.getCurrentUser() != null ? userService.getCurrentUser().getId() : 1L);
        }
        note.setUpdatedById(userService != null && userService.getCurrentUser() != null ? userService.getCurrentUser().getId() : 1L);

        return super.save(note);
    }

    public Timestamp determinePublishedAt(Note note) {
        Note existingNote = note.getId() != null ? findOne(note.getId()) : null;

        // Quick check for published boolean that all notes may not have (even though they should)
        if (existingNote != null && existingNote.getPublished() == null) {
            existingNote.setPublished(false);
        }
        if (note.getPublished() == null) {
            note.setPublished(false);
        }

        Timestamp publishedAt = null;
        // If note already exists and is not published, and the new note IS published, update publishedAt
        if (existingNote != null && !existingNote.getPublished() && note.getPublished()) {
            publishedAt = new Timestamp(Calendar.getInstance().getTimeInMillis());
            // If note already exists and is published, and the new note IS published, publishedAt stays the same
        } else if (existingNote != null && existingNote.getPublished() && note.getPublished()) {
            publishedAt = note.getPublishedAt();
            // If note does NOT already exist, and the new note IS published, update publishedAt
        } else if (existingNote == null && note.getPublished()) {
            publishedAt = new Timestamp(Calendar.getInstance().getTimeInMillis());
        }
        return publishedAt;
    }

    public Note createGeneralNoteFromPatientIntake(PatientIntake intake, Long patientId) {
        Note note = new Note();
        note.setSubject("Patient Intake Note");
        note.setNote(intake.getNote());
        note.setNoteType("general");
        note.setPublished(true);
        note.setPublishedAt(new Timestamp(Calendar.getInstance().getTimeInMillis()));
        note.setUserId(1L);
        note.setPatientId(patientId);
        return note;
    }

    public List<Note> orderNotes(List<Note> notes, Boolean reverseChildNoteOrder) {
        List<Note> orderedResults = new ArrayList<>();
        for (Note note : notes) {
            if (note.getParentId() == null) {
                orderedResults.add(note);
            }
        }
        if (reverseChildNoteOrder)
            Collections.reverse(notes);
        for (Note note : notes) {
            if (note.getParentId() != null) {
                Long parentId = note.getParentId();
                for (int i = 0; i < orderedResults.size(); i++) {
                    if (parentId.equals(orderedResults.get(i).getId())) {
                        int insertIndex = 1;
                        while ((i + insertIndex < orderedResults.size()) && (orderedResults.get(i + insertIndex).getParentId() != null)) {
                            insertIndex++;
                        }
                        orderedResults.add(i + insertIndex, note);
                        continue;
                    }
                }
            }
        }
        return orderedResults;
    }

    public List<INote> getInterfaceByPrescriptionIdAndNoteType(Long prescriptionId, String noteType) {
        return noteIRepository.getByPrescriptionIdAndNoteType(prescriptionId, noteType);
    }

    public List<INote> getInterfaceByClaimId(Long claimId) {
        return noteIRepository.getByClaimId(claimId);
    }

    public List<INote> getInterfaceByClaimIdAndNoteType(Long claimId, String noteType) {
        return noteIRepository.getByClaimIdAndNoteType(claimId, noteType);
    }

    public void loadForeignKeys(Note o) {
        if (o != null) {
            if (o.getUserId() != null) {
                o.setUser(userService.getUserById(o.getUserId()));
            }
            if (o.getResidentId() != null) {
                o.setResident(userService.getUserById(o.getResidentId()));
            }
            if (o.getTreatingPractitionerId() != null) {
                o.setTreatingPractitioner(userService.getUserById(o.getTreatingPractitionerId()));
            }
            if (o.getPrescriptionId() != null) {
                o.setPrescription(prescriptionService.findOne(o.getPrescriptionId()));
            }
            if (o.getCreatedById() != null) {
                o.setCreatedBy(userService.getUserById(o.getCreatedById()));
            }
            if (o.getUpdatedById() != null) {
                o.setUpdatedBy(userService.getUserById(o.getUpdatedById()));
            }
            if (o.getAppointmentId() != null) {
                o.setAppointment(appointmentService.findOne(o.getAppointmentId()));
            }
        }
    }

    @Override
    public Map<String, Object> saveForVersion(Note note) {
        if (note.getId() == null && note.getCreatedById() == null) {
            note.setCreatedById(userService != null && userService.getCurrentUser() != null ? userService.getCurrentUser().getId() : 1L);
        }

        note.setUpdatedById(userService != null && userService.getCurrentUser() != null ? userService.getCurrentUser().getId() : 1L);

        Map<String, Object> respMap = new HashMap<>();
        String strLockingMessage = "This note has been edited by another user.  Please reload/re-open to get the latest version and re-save with your changes.";

        NoteInfo currentNote = findOne(note.getId());

        if (null != currentNote && currentNote.getVersion().intValue() != note.getVersion().intValue()) {
            respMap = getAuditDetails(note.getId(), logUtils.checkVersionLock(note, currentNote, strLockingMessage, Note.class, currentNote.getId()));
        }
        else
        {
            respMap.put(SAVED, super.save(note));
        }

        return respMap;
    }
}
