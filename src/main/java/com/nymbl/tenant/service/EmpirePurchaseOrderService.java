package com.nymbl.tenant.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nymbl.config.Constants;
import com.nymbl.config.dto.DeviceTypeDTO;
import com.nymbl.config.dto.PrescriptionPurchaseOrdersDTO;
import com.nymbl.config.dto.PurchaseOrderDTO;
import com.nymbl.config.security.AuthoritiesConstants;
import com.nymbl.config.security.TokenUtils;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.model.Company;
import com.nymbl.master.model.User;
import com.nymbl.master.service.CompanyService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.ErrorMessage;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.PurchaseOrder_ItemRepository;
import io.sentry.Sentry;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpirePurchaseOrderService {
    public static final String EMPIRE_MEDICAL_AUTHENTICATION_ALERT = "Something went wrong with your Empire Medical authentication.  Please reauthenticate with Empire Medical.  Go to Shop Retail Suppliers and click the Empire Medical logo.";
    private final UserService userService;

    private final TokenUtils tokenUtils;

    private final PurchaseOrderService purchaseOrderService;

    private final PrescriptionService prescriptionService;

    private final ItemByManufacturerService itemByManufacturerService;

    private final EmpireRestCallService empireRestCallService;

    private final CompanyService companyService;

    private final SystemSettingService systemSettingService;

    private final Map<Long, String> userEmpireAccessTokenMap = new HashMap<>();

    private final Map<Long, LocalDateTime> userAccessTokenExpirationMap = new HashMap<>();

    @Value("${empire.token.url}")
    private String empireTokenUrl;

    @Value("${empire.client.id}")
    private String empireClientId;

    @Value("${empire.client.secret}")
    private String empireClientSecret;

    @Value("${empire.redirect.url}")
    private String empireRedirectUrl;

    @Value("${empire.order.lines.url}")
    private String empireOrderLinesUrl;

    @Value("${empire.website.url}")
    private String empireWebsite;

    @Value("${empire.get.token.retries}")
    private String empireGetTokenRetries;

    @Value("${empire.get.token.retry.thread.sleep}")
    private String empireGetTokenRetryThreadSleep;

    @Value("${empire.minus.seconds.from.access.token.availability.for.is.should.refresh}")
    private String empireMinusSecondsFromAccessTokenAvailabilityForIsShouldRefresh;

    public EmpirePurchaseOrderService(UserService userService,
                                      TokenUtils tokenUtils,
                                      PurchaseOrderService purchaseOrderService,
                                      PurchaseOrder_ItemRepository purchaseOrder_itemRepository,
                                      PrescriptionService prescriptionService,
                                      EmpireRestCallService empireRestCallService,
                                      CompanyService companyService,
                                      SystemSettingService systemSettingService,
                                      ItemByManufacturerService itemByManufacturerService) {
        this.userService = userService;
        this.tokenUtils = tokenUtils;
        this.purchaseOrderService = purchaseOrderService;
        this.prescriptionService = prescriptionService;
        this.empireRestCallService = empireRestCallService;
        this.companyService = companyService;
        this.systemSettingService = systemSettingService;
        this.itemByManufacturerService = itemByManufacturerService;
    }

    public List<PrescriptionPurchaseOrdersDTO> getEmpireAndOtherPrescriptionPurchaseOrdersDTO(Long patientId, boolean getArchived) {
        List<Prescription> prescriptions = prescriptionService.findByPatientId(patientId, getArchived);
        List<PrescriptionPurchaseOrdersDTO> prescriptionPurchaseOrdersDTOS = purchaseOrderService.profileLoad(patientId);
        Map<Long, PrescriptionPurchaseOrdersDTO> prescriptionPurchaseOrdersDTOMap = new HashMap<>();
        for (PrescriptionPurchaseOrdersDTO prescriptionPurchaseOrdersDTO : prescriptionPurchaseOrdersDTOS) {
            prescriptionPurchaseOrdersDTOMap.put(prescriptionPurchaseOrdersDTO.getPrescriptionId(), prescriptionPurchaseOrdersDTO);
        }
        List<PrescriptionPurchaseOrdersDTO> results = new ArrayList<>();
        boolean isCustomPurchaseOrderEnabled = "1".equals(systemSettingService.findBySectionAndField("purchasing", "enable_custom_po").getValue());
        for (Prescription prescription : prescriptions) {
            List<PurchaseOrderDTO> purchaseOrderDTOs = getPurchaseOrderDTOsFromEmpirePurchaseOrders(prescription.getId());
            if (prescriptionPurchaseOrdersDTOMap.containsKey(prescription.getId())) {
                purchaseOrderDTOs.addAll(prescriptionPurchaseOrdersDTOMap.get(prescription.getId()).getPurchaseOrderDTOs());
            }
            PrescriptionPurchaseOrdersDTO dto = new PrescriptionPurchaseOrdersDTO();
            dto.setDeviceTypeName(prescription.getDeviceType().getName());
            dto.setPatientId(patientId);
            if (prescription.getPatient() != null) {
                dto.setPatientFirstName(prescription.getPatient().getFirstName());
                dto.setPatientLastName(prescription.getPatient().getLastName());
            }
            if (null != prescription.getDeviceType()) {
                DeviceTypeDTO deviceTypeDTO = new DeviceTypeDTO();
                deviceTypeDTO.setDeviceType(prescription.getDeviceType());
                dto.setDeviceTypeDTO(deviceTypeDTO);
            }
            dto.setPrescriptionId(prescription.getId());
            dto.setActive(prescription.getActive());
            dto.setPrescriptionDate(prescription.getPrescriptionDate());
            dto.setPurchaseOrderDTOs(purchaseOrderDTOs);
            dto.setBranchId(prescription.getBranchId());
            dto.setIsCustomPurchaseOrderEnabled(isCustomPurchaseOrderEnabled);
            dto.setArchived(prescription.getArchived());
            results.add(dto);
        }
        results.sort(Comparator.comparing(PrescriptionPurchaseOrdersDTO::getPrescriptionId).reversed());
        return results;
    }

    public List<PurchaseOrderDTO> getPurchaseOrderDTOsFromEmpireAndNymblPurchaseOrders(Long prescriptionId) {
        List<PurchaseOrderDTO> allPurchaseOrderDTOList = new ArrayList<>();
        allPurchaseOrderDTOList.addAll(purchaseOrderService.getPurchaseOrderDtosByPrescriptionId(prescriptionId));
        allPurchaseOrderDTOList.addAll(getPurchaseOrderDTOsFromEmpirePurchaseOrders(prescriptionId));
        return allPurchaseOrderDTOList;
    }

    public List<PurchaseOrderDTO> getPurchaseOrderDTOsFromEmpirePurchaseOrders(Long prescriptionId) {
        getTokenMapIfAccessTokenHasExpiredElseReturnNull();
        String empireAccessToken = getEmpireAccessToken();

        if (empireAccessToken == null) {
            return new ArrayList<>();
        } else {
            Prescription prescription = prescriptionService.findOne(prescriptionId);
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("job_number", userService.getCurrentCompany().getId() + ":" + prescriptionId);
            List<EmpirePurchaseOrder> empirePurchaseOrderList = empirePurchaseOrderETL(empireAccessToken, paramMap);
            boolean valid = !(empirePurchaseOrderList != null && userService.getCurrentCompany() != null && userService.getCurrentCompany().getId() != null && prescriptionId != null && userService.getCurrentUser() != null && userService.getCurrentUser().getUsername() != null);
            if (valid) {
                String message = "Problem getting Purchase Order DTOs from Empire Purchase Order: ";
                if (empirePurchaseOrderList == null) {
                    message = message.concat("empirePurchaseOrderList is NULL. ");
                    empirePurchaseOrderList = new ArrayList<>();
                }
                if (userService.getCurrentCompany() == null || userService.getCurrentCompany().getId() == null) {
                    message = message.concat("CurrentCompany is NULL. ");
                } else {
                    message = message.concat("CurrentCompany = " + userService.getCurrentCompany().getName() + ". ");
                }
                if (prescriptionId == null) {
                    message = message.concat("prescriptionId is NULL. ");
                } else {
                    message = message.concat("prescriptionId = " + prescriptionId + ". ");
                }
                if (userService.getCurrentUser() == null || userService.getCurrentUser().getUsername() == null) {
                    message = message.concat("CurrentUser is NULL");
                } else {
                    message = message.concat("CurrentUser = " + userService.getCurrentUser().getUsername() + ".");
                }
                Sentry.captureMessage(message);
            }

            Long itemId = 99999L;
            Map<Long, List<PurchaseOrder_Item>> purchaseOrder_itemMap = getEmpirePurchaseOrderPurchaseOrderItemListMap(prescriptionId, prescription, empirePurchaseOrderList, itemId);
            Map<Long, PurchaseOrder> purchaseOrderMap = new HashMap<>();
            for (EmpirePurchaseOrder empirePurchaseOrder : empirePurchaseOrderList) {
                if (!purchaseOrderMap.containsKey(empirePurchaseOrder.getOrder_number()) && !empirePurchaseOrder.getJob_number().contains(EMPIRE_MEDICAL_AUTHENTICATION_ALERT)) {
                    PurchaseOrder purchaseOrder = new PurchaseOrder();
                    try {
                        purchaseOrder.setId(empirePurchaseOrder.getOrder_number());
                        purchaseOrder.setStatus(empirePurchaseOrder.getOrder_status_name());//Reverse camel case?
                        purchaseOrder.setDiscount(BigDecimal.ZERO);
                        Date dateNeeded = getEpoDate(empirePurchaseOrder.getDate_needed());
                        purchaseOrder.setExpectedAt(dateNeeded);
                        purchaseOrder.setNote(empirePurchaseOrder.getCustomer_note());
                        if (empirePurchaseOrder.getUser_ordered_timestamp() != null) {
                            purchaseOrder.setOrderedAt(new java.sql.Date(empirePurchaseOrder.getUser_ordered_timestamp().getTime()));
                        }
                        purchaseOrder.setOrderedBy(userService.getCurrentUser());
                        purchaseOrder.setOrderedById(userService.getCurrentUser().getId());
                        String referenceNumber = null;
                        if (empirePurchaseOrder.getOrder_number() != null) {
                            referenceNumber = empirePurchaseOrder.getOrder_number().toString();
                        }
                        purchaseOrder.setReferenceNumber(referenceNumber);
                        purchaseOrder.setSubTotal(empirePurchaseOrder.getPrice());
                        purchaseOrder.setTotalCost(empirePurchaseOrder.getPrice());
                        purchaseOrder.setAttn(empirePurchaseOrder.getShipping_address().getName());
                        purchaseOrder.setStreetAddress(empirePurchaseOrder.getShipping_address().getStreet_address());
                        purchaseOrder.setCity(empirePurchaseOrder.getShipping_address().getCity());
                        purchaseOrder.setState(empirePurchaseOrder.getShipping_address().getState());
                        purchaseOrder.setZipcode(empirePurchaseOrder.getShipping_address().getPostalcode());
                        purchaseOrder.setCountry("US");
                        purchaseOrder.setStatus(empirePurchaseOrder.getOrder_status_name());
                        Vendor vendor = new Vendor();
                        vendor.setName("Empire");
                        purchaseOrder.setVendor(vendor);
                    } catch (Exception e) {
                        log.error(String.valueOf(e));
                    }
                    purchaseOrderMap.put(empirePurchaseOrder.getOrder_number(), purchaseOrder);
                } else if (empirePurchaseOrder.getJob_number().contains(EMPIRE_MEDICAL_AUTHENTICATION_ALERT)) {
                    PurchaseOrder purchaseOrder = new PurchaseOrder();
                    purchaseOrder.setStatus(empirePurchaseOrder.getJob_number());
                    Vendor vendor = new Vendor();
                    vendor.setName("Empire");
                    purchaseOrder.setVendor(vendor);
                    purchaseOrderMap.put(0L, purchaseOrder);
                }
            }
            List<PurchaseOrderDTO> purchaseOrderDTOList = new ArrayList<>();
            for (Long key : purchaseOrderMap.keySet()) {
                PurchaseOrder purchaseOrder = purchaseOrderMap.get(key);
                if (purchaseOrder_itemMap.get(purchaseOrder.getId()) != null && purchaseOrder_itemMap.get(purchaseOrder.getId()).size() > 1) {
                    Set<String> statusSet = new HashSet<>();
                    for (PurchaseOrder_Item purchaseOrder_item : purchaseOrder_itemMap.get(purchaseOrder.getId())) {
                        statusSet.add(purchaseOrder_item.getStatus());
                    }
                    if (statusSet.size() > 1) {
                        purchaseOrder.setStatus("Mixed");
                    }
                }
                PurchaseOrderDTO purchaseOrderDTO = new PurchaseOrderDTO();
                purchaseOrderDTO.setPurchaseOrder(purchaseOrder);
                purchaseOrderDTO.setPurchaseOrderItems(purchaseOrder_itemMap.get(purchaseOrder.getId()));
                purchaseOrderDTO.setIsThirdPartyVendor(true);
                purchaseOrderDTOList.add(purchaseOrderDTO);
            }
            return purchaseOrderDTOList;
        }
    }

    private Date getEpoDate(java.sql.Timestamp dateRequested) {
        Date dateNeeded = null;
        if (dateRequested != null) {
            dateNeeded = DateUtil.getDate(dateRequested.toString(), Constants.DF_YYYY_MM_DD);
        }
        return dateNeeded;
    }

    private String getEmpireAccessToken() {
        String empireAccessToken = null;
        User user = userService.getCurrentUser();
        if (userEmpireAccessTokenMap.containsKey(user.getId())) {
            empireAccessToken = userEmpireAccessTokenMap.get(user.getId());
        }
//        if (userEmpireAccessTokenMap.size() > 1) {
//            StringBuilder sb = new StringBuilder();
//            for (Map.Entry<Long, String> entry : userEmpireAccessTokenMap.entrySet()) {
//                sb.append("User ID = " + entry.getKey() + ", Empire access token = " + entry.getValue() + "\n");
//            }
//            log.error("Empire: userEmpireAccessTokenMap contains more than one record:\n" + sb);
//        }
        return empireAccessToken;
    }

    public Map<Long, List<PurchaseOrder_Item>> getEmpirePurchaseOrderPurchaseOrderItemListMap(Long prescriptionId, Prescription prescription, List<EmpirePurchaseOrder> empirePurchaseOrderList, Long itemId) {
        Map<Long, List<PurchaseOrder_Item>> purchaseOrder_itemMap = new HashMap<>();
        if (empirePurchaseOrderList != null) {
            for (EmpirePurchaseOrder empirePurchaseOrder : empirePurchaseOrderList) {
                itemId++;
                // vendor
                Vendor vendor = new Vendor();
                vendor.setId(0L);
                vendor.setName(empirePurchaseOrder.getManufacturer_name());
                // Manufacturer item
                ItemByManufacturer ibm = new ItemByManufacturer();
                ibm.setId(itemId);
                ibm.setName(empirePurchaseOrder.getDescription());
                ibm.setDescription(empirePurchaseOrder.getDescription());
                ibm.setManufacturer(vendor);
                ibm.setManufacturerId(vendor.getId());
                ibm.setPartNumber(empirePurchaseOrder.getPart_number());
                // Vendor item
                Item item = new Item();
                item.setId(itemId);
                item.setItemByManufacturer(ibm);
                item.setItemByManufacturerId(ibm.getId());
                item.setVendorId(vendor.getId());
                item.setVendor(vendor);
                item.setSku(null);
                item.setPrice(empirePurchaseOrder.getPrice());
                item.setActive(true);

                PurchaseOrder_Item purchaseOrder_item = new PurchaseOrder_Item();
                purchaseOrder_item.setId(empirePurchaseOrder.getOrder_line_id());
                purchaseOrder_item.setStatus(empirePurchaseOrder.getOrder_status_name());
                purchaseOrder_item.setItemCost(empirePurchaseOrder.getPrice());
                purchaseOrder_item.setQuantity(empirePurchaseOrder.getQuantity());
                purchaseOrder_item.setPatientId(prescription.getPatientId());
                purchaseOrder_item.setPrescriptionId(prescriptionId);
                purchaseOrder_item.setPurchaseOrderId(empirePurchaseOrder.getOrder_number());
                purchaseOrder_item.setVendor(vendor);
                purchaseOrder_item.setPractitionerId(userService.getCurrentUser().getId());
                purchaseOrder_item.setItem(item);
                purchaseOrder_item.setItemId(item.getId());
                Date dateNeeded = getEpoDate(empirePurchaseOrder.getDate_needed());
                purchaseOrder_item.setDateNeeded(dateNeeded);
                java.sql.Date dateExpected = getEpoDate(empirePurchaseOrder.getDate_expected());
                purchaseOrder_item.setDateExpected(dateExpected);
                purchaseOrder_item.setTrackingNumbers(empirePurchaseOrder.getTracking_numbers());
                purchaseOrder_item.setSerialNumber(empirePurchaseOrder.getSerial_numbers());
                if (purchaseOrder_itemMap.containsKey(purchaseOrder_item.getPurchaseOrderId())) {
                    purchaseOrder_itemMap.get(purchaseOrder_item.getPurchaseOrderId()).add(purchaseOrder_item);
                } else {
                    List<PurchaseOrder_Item> purchaseOrder_itemList = new ArrayList<>();
                    purchaseOrder_itemList.add(purchaseOrder_item);
                    purchaseOrder_itemMap.put(purchaseOrder_item.getPurchaseOrderId(), purchaseOrder_itemList);
                }
            }
        }
        return purchaseOrder_itemMap;
    }

    public Page<EmpirePurchaseOrder> search(@RequestParam(name = "appendParameters", required = false) Map<String, String> appendParameters, Pageable pageable, HttpServletResponse response) {
        getTokenMapIfAccessTokenHasExpiredElseReturnNull();
        String empireAccessToken = getEmpireAccessToken();
        int total = 0;
        int lastPage = 1;
        if (empireAccessToken != null) {
            try {
                String empireOrderLinesUrlPage = empireOrderLinesUrl + "?";
                if (appendParameters != null && appendParameters.size() > 0) {
                    StringBuilder sb = getParamString(appendParameters);
                    empireOrderLinesUrlPage += sb.toString();
                }

                String body = empireRestCallService.getEmpireOrderLinesPageDataBody(empireAccessToken, empireOrderLinesUrlPage);
                ObjectMapper mapper = new ObjectMapper();
                JsonNode root = mapper.readTree(body);
                JsonNode totalRecords = root.path("meta").path("total");
                //System.out.println("String.valueOf(totalRecords) = "+String.valueOf(totalRecords));
                JsonNode lastPageNode = root.path("meta").path("last_page");
                lastPage = Integer.parseInt(String.valueOf(lastPageNode));
                total = Integer.parseInt(String.valueOf(totalRecords));
            } catch (JsonProcessingException e) {
                log.error("Empire JSON Processing exception: " + StringUtil.getEmpireHtmlError(e));
            }
        }
        if (appendParameters.get("page").equals("0")) {
            appendParameters.put("page", lastPage + "");
        }
        List<EmpirePurchaseOrder> empirePurchaseOrderList = empirePurchaseOrderETL(empireAccessToken, appendParameters);


        empirePurchaseOrderList = empirePurchaseOrderList.stream().map(e ->
                {
                    if (e.getJob_number() != null) {
                        Long companyId = null;
                        Long prescriptionId = null;
                        Long purchaseOrderId = null;
                        try {
                            if (e.getJob_number().contains(":")) {
                                String[] values = e.getJob_number().split(":");
                                companyId = Long.parseLong(values[0]);
                                prescriptionId = Long.parseLong(values[1]);
                            } else {
                                purchaseOrderId = Long.parseLong(e.getJob_number());
                            }
                        } catch (Exception ex) {
                        }
                        Prescription prescription = null;
                        PurchaseOrder purchaseOrder = null;
                        if (prescriptionId != null) {
                            prescription = prescriptionService.findOne(prescriptionId);
                        } else if (purchaseOrderId != null) {
                            purchaseOrder = purchaseOrderService.findOne(purchaseOrderId);
                        }
                        List<User> userList = new ArrayList<>();
                        if (companyId != null) {
                            userList = userService.getActiveUsersForCompanyByCompanyId(companyId);
                            if (!userList.contains(userService.getCurrentUser())) {
                                Company company = companyService.findOne(companyId);
                                String companyName = "";
                                if (company != null) {
                                    companyName = company.getName();
                                }
                                log.error("Empire: User should not have an order for this company: prescriptionId = " + prescriptionId +
                                        ", companyId = " + companyId +
                                        ", company name = " + companyName +
                                        ", current company id = " + userService.getCurrentCompany().getId() +
                                        ", current company name = " + userService.getCurrentCompany().getName() +
                                        ", current user name = " + userService.getCurrentUser().getUsername() +
                                        ", current user last name = " + userService.getCurrentUser().getLastName() +
                                        ", current user first name = " + userService.getCurrentUser().getFirstName() +
                                        ", current user phone number = " + userService.getCurrentUser().getPhoneNumber() +
                                        ", EPO user ordered timestamp = " + e.getUser_ordered_timestamp());
                            }
                        }
                        if (prescription != null && companyId != null && userService.getCurrentCompany().getId().toString().equals(companyId.toString())) {
                            e.setJob_number(prescription.getPatient().getId() + ";" + prescription.getPatient().getLastName() + ", " + prescription.getPatient().getFirstName() + ";" + prescriptionId);
                        } else if (purchaseOrder != null) {
                            e.setJob_number("This is a Purchase Order Id: " + e.getJob_number());
                        } else if (prescriptionId != null && companyId != null) {
                            log.error("Missing prescription or company ID mismatch: prescriptionId = " + prescriptionId + ",  companyId = " + companyId + ",  userService.getCurrentCompany().getId() = " + userService.getCurrentCompany().getId() + ", userService.getCurrentUser().getUsername() = " + userService.getCurrentUser().getUsername());
                            getEmpireOrderHistoryAppLink(e, ";There is no match for this Company ID, " + companyId + ", and Prescription ID, " + prescriptionId);
                        } else if (e.getJob_number().contains(EMPIRE_MEDICAL_AUTHENTICATION_ALERT)) {
                            //log.info("Empire: Authentication Expired for " + userService.getCurrentUser().getUsername() + " at company " + userService.getCurrentCompany().getName());
                        } else {
                            getEmpireOrderHistoryAppLink(e, ";This needs a valid Nymbl ID (<Company ID>:<Prescription ID>), current value is, " + e.getJob_number());
                        }
                    } else if (!e.getIs_for_stock()) {
                        getEmpireOrderHistoryAppLink(e, ";This needs a valid Nymbl ID (<Company ID>:<Prescription ID>), there is no current value");
                    } else if (e.getIs_for_stock()) {
                        e.setJob_number("For stock only");
                    }
                    return e;
                }
        ).sorted(Comparator.comparing(EmpirePurchaseOrder::getOrder_line_id).reversed()).collect(Collectors.toList());

        int pageNumber = pageable.getPageNumber() > 0 ? pageable.getPageNumber() - 1 : 0;
        Pageable newPageable = PageRequest.of(pageNumber, pageable.getPageSize());
        return new PageImpl<EmpirePurchaseOrder>(empirePurchaseOrderList, newPageable, total);
    }

    public void getEmpireOrderHistoryAppLink(EmpirePurchaseOrder e, String message) {
        String baseUrl = empireOrderLinesUrl.substring(0, empireOrderLinesUrl.indexOf("my"));
        getTokenMapIfAccessTokenHasExpiredElseReturnNull();
        String empireAccessToken = getEmpireAccessToken();
        String url = baseUrl + "oauth/bridge/order-history?access_token=" + empireAccessToken + "&facility_id=" + empireClientId + "&line_id=" + e.getOrder_line_id() + "&page=1";
        e.setJob_number(url + message);
    }

    public Map<String, String> appendParametersAndValues(String orderNumber,
                                                         String partNumber,
                                                         String isForStock,
                                                         String orderStatusName,
                                                         String prescriptionId,
                                                         String manufacturerName,
                                                         String userOrderedTimestampAfter,
                                                         String userOrderedTimestampBefore,
                                                         String dateNeededAfter,
                                                         String dateNeededBefore,
                                                         String dateReceivedAfter,
                                                         String dateReceivedBefore,
                                                         String rmaReturnNumber,
                                                         String vendorConfirmationNumber,
                                                         String costCenter,
                                                         String trackingNumber,
                                                         String returnTrackingNumbers,
                                                         String page,
                                                         String pageSize
    ) {
        Map<String, String> paramMap = new HashMap<>();
        appendParameterAndValue(paramMap, "order_number", orderNumber);
        appendParameterAndValue(paramMap, "part_number", partNumber);
        appendParameterAndValue(paramMap, "is_for_stock", isForStock);
        appendParameterAndValue(paramMap, "order_status_name", orderStatusName.toLowerCase());
        appendParameterAndValue(paramMap, "job_number", prescriptionId);

        afterMidNight(paramMap, "user_ordered_timestamp_after", userOrderedTimestampAfter);
        beforeThisDay(paramMap, "user_ordered_timestamp_before", userOrderedTimestampBefore);

        afterMidNight(paramMap, "date_needed_after", dateNeededAfter);
        beforeThisDay(paramMap, "date_needed_before", dateNeededBefore);

        afterMidNight(paramMap, "date_received_after", dateReceivedAfter);
        beforeThisDay(paramMap, "date_received_before", dateReceivedBefore);

        appendParameterAndValue(paramMap, "cost_center", costCenter);
        appendParameterAndValue(paramMap, "manufacturer_name", manufacturerName);
        appendParameterAndValue(paramMap, "rma_return_number", rmaReturnNumber);
        appendParameterAndValue(paramMap, "tracking_number", trackingNumber);
        appendParameterAndValue(paramMap, "return_tracking_numbers", returnTrackingNumbers);
        appendParameterAndValue(paramMap, "page", page);
        appendParameterAndValue(paramMap, "per_page", pageSize);
        return paramMap;
    }

    public void afterMidNight(Map<String, String> paramMap, String parameterName, String parameterValue) {
        if (parameterValue != null && !parameterValue.equals("") && parameterValue.length() == "1970-01-01T00:00:00.000Z".length()) {
            parameterValue = parameterValue.substring(0, 10) + "T23:59:59.000Z";
        }
        appendParameterAndValue(paramMap, parameterName, parameterValue);
    }

    public void beforeThisDay(Map<String, String> paramMap, String parameterName, String parameterValue) {
        if (parameterValue != null && !parameterValue.equals("") && parameterValue.length() == "1970-01-01T00:00:00.000Z".length()) {
            parameterValue = parameterValue.substring(0, 10) + "T00:00:00.000Z";
        }
        appendParameterAndValue(paramMap, parameterName, parameterValue);
    }

    public void appendParameterAndValue(Map<String, String> parmMap, String paramName, String paramValue) {
        if (paramValue != null && !"".equals(paramValue.trim())) {
            paramValue = paramValue.replace(" ", "%20");
            //sb.append("&" + paramName + "=" + paramValue);
            parmMap.put(paramName, paramValue);
        }
    }

    public String getAccessTokenAndRefreshToken(String code) {
        User user = userService.getCurrentUser();

        Map<String, String> paramsMap = new HashMap<>();
        if (code != null) {
            try {
                String body = empireRestCallService.useAuthCode(code, empireClientId, empireClientSecret, empireRedirectUrl, empireTokenUrl);
                paramsMap = getParamsMapFromJsonString(body);
                if (paramsMap.containsKey("access_token") && paramsMap.containsKey("refresh_token")) {
                    //System.out.println("empire refresh token = \n" + paramsMap.get("refresh_token"));
//                    log.info("Empire: user = " + user.getUsername() +
//                        ", company = " + user.getCompany().getName() +
//                        ", code = " + code +
//                        ", refresh_token = " + paramsMap.get("refresh_token") +
//                        ", access_token = " + paramsMap.get("access_token"));
                    updateUserEmpireAccessTokenMap(user, paramsMap.get("access_token"));
                    updateUserAccessTokenExpirationMap(user, LocalDateTime.now().plusSeconds(900));
                    user.setEmpireRefreshToken(paramsMap.get("refresh_token"));
                    userService.save(user);
                } else {
                    log.error("getAccessTokenAndRefreshToken: code = " + code + ", username = " + user.getUsername());
                    log.error("getAccessTokenAndRefreshToken: empireClientId = " + empireClientId + ", username = " + user.getUsername());
                    log.error("getAccessTokenAndRefreshToken: empireClientSecret = " + empireClientSecret + ", username = " + user.getUsername());
                    log.error("getAccessTokenAndRefreshToken: empireRedirectUrl = " + empireRedirectUrl + ", username = " + user.getUsername());
                    log.error("getAccessTokenAndRefreshToken: empireTokenUrl = " + empireTokenUrl + ", username = " + user.getUsername());
                    log.error("getAccessTokenAndRefreshToken: paramsMap size = " + paramsMap.size() + ", username = " + user.getUsername());
                    for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
                        log.error("getAccessTokenAndRefreshToken: key = " + entry.getKey() + ", value = " + entry.getValue() + ", username = " + user.getUsername());
                    }
                    if (!paramsMap.containsKey("access_token")) {
                        log.error("getAccessTokenAndRefreshToken: missing access token, username = " + user.getUsername());
                    }
                    if (!paramsMap.containsKey("refresh_token")) {
                        log.error("getAccessTokenAndRefreshToken: missing refresh token, username = " + user.getUsername());
                    }
                }
            } catch (IOException e) {
                log.error("getAccessTokenAndRefreshToken: Exception while trying to retrieve access token and refresh token, username = " + user.getUsername());
                user.setEmpireRefreshToken(null);
                userService.save(user);
                empireErrorMessage(user, ErrorMessage.getFullInfo(e));
            }
        } else {
            log.error("getAccessTokenAndRefreshToken: code is null, username = " + user.getUsername());
            user.setEmpireRefreshToken(null);
            userService.save(user);
        }
        return user.getEmpireRefreshToken();
    }

    public Map<String, String> getAccessTokenFromRefreshToken(User user) {
        String empireRefreshToken = user.getEmpireRefreshToken();
        Map<String, String> paramsMap = new HashMap<>();
        if (empireRefreshToken != null) {
            String body = "";
            try {
                body = empireRestCallService.getAccessTokenFromRefreshToken(empireRefreshToken, empireClientId, empireClientSecret, empireTokenUrl);
                if ("Error.".equals(body)) {
                    removeRefreshTokenAndResetTempValues(user, "Empire refresh token is bad, resetting it to null in the database (body is Error).");
                } else {
                    paramsMap = getParamsMapFromJsonString(body);
                    if (paramsMap.containsKey("access_token")) {
                        String empireAccessToken = paramsMap.get("access_token");
                        updateUserEmpireAccessTokenMap(user, empireAccessToken);
                        updateUserAccessTokenExpirationMap(user, LocalDateTime.now().plusSeconds(900));
                        user.setEmpireRefreshToken(paramsMap.get("refresh_token"));
                        userService.save(user);
                        return paramsMap;
                    } else if (paramsMap.containsKey("error_description") && "The refresh token is invalid.".equals(paramsMap.get("error_description"))) {
                        removeRefreshTokenAndResetTempValues(user, "Empire refresh token is bad, resetting it to null in the database (paramsMap error description).");
                    }
                }
            } catch (IOException e) {
                //System.out.println("Refresh token = " + empireRefreshToken);
                //System.out.println("body = \n" + body);
                empireErrorMessage(user, ErrorMessage.getFullInfo(e));
            }
        } else {
            empireErrorMessage(user, "Empire refresh token is null");
        }
        return null;
    }

    private void updateUserEmpireAccessTokenMap(User user, String empireAccessToken) {
//        try {
//            log.info("Empire: Update empireAccessToken: username = " + user.getUsername() + ", user ID =" + user.getId() + ", empireAccessToken = " + empireAccessToken +
//                ", refresh_token = " + user.getEmpireRefreshToken());
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }
        userEmpireAccessTokenMap.put(user.getId(), empireAccessToken);
    }

    private void updateUserAccessTokenExpirationMap(User user, LocalDateTime accessTokenExpiration) {
//        try {
//            log.info("Empire: Update accessTokenExpiration: username = " + user.getUsername() + ", user ID =" + user.getId() + ", accessTokenExpiration = " + accessTokenExpiration);
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }
        userAccessTokenExpirationMap.put(user.getId(), accessTokenExpiration);
    }

    public void empireErrorMessage(User user, String s) {
        log.error(user.getCompany().getName() + ":" + user.getUsername() + ":" + empireTokenUrl + ":" + s);
    }

    @SuppressWarnings("unchecked")
    public Map<String, String> getParamsMapFromJsonString(String body) throws IOException {
        Map<String, String> paramsMap;
        ObjectMapper objectMapper = new ObjectMapper();
        paramsMap = objectMapper.readValue(body.getBytes(), HashMap.class);
        return paramsMap;
    }

    public void removeRefreshTokenAndResetTempValues(User user, String message) {
        updateUserEmpireAccessTokenMap(user, null);
        updateUserAccessTokenExpirationMap(user, LocalDateTime.now());
        user.setEmpireRefreshToken(null);
        userService.save(user);
        empireErrorMessage(user, message);
    }

    public List<EmpirePurchaseOrder> empirePurchaseOrderETL(String empireAccessToken, Map<String, String> appendParameters) {

        if (empireAccessToken == null) {
            userService.getCurrentUser().setEmpireRefreshToken(null);
            userService.save(userService.getCurrentUser());
            List<EmpirePurchaseOrder> empirePurchaseOrderList = new ArrayList<>();
            EmpirePurchaseOrder empirePurchaseOrder = new EmpirePurchaseOrder();
            empirePurchaseOrder.setJob_number(EMPIRE_MEDICAL_AUTHENTICATION_ALERT);
            empirePurchaseOrderList.add(empirePurchaseOrder);
            return empirePurchaseOrderList;
        } else {
            //System.out.println("empirePurchaseOrderETL");
            String empireOrderLinesUrlPage = empireOrderLinesUrl + "?";
            if (appendParameters != null && appendParameters.size() > 0) {
                StringBuilder sb = getParamString(appendParameters);
                empireOrderLinesUrlPage += sb.toString();
            }

            //System.out.println("empire url = "+empireOrderLinesUrlPage);
            String body = empireRestCallService.getEmpireOrderLinesPageDataBody(empireAccessToken, empireOrderLinesUrlPage);
            if (body.contains("Please enable JavaScript to use this application")) {
                getAccessTokenFromRefreshToken();
                body = empireRestCallService.getEmpireOrderLinesPageDataBody(getUserEmpireAccessTokenMap().get(userService.getCurrentUser().getId()), empireOrderLinesUrlPage + "&page=1");
                if (body.contains("Please enable JavaScript to use this application")) {
                    log.info("Empire: access token expired: username = " + userService.getCurrentUser().getUsername() + ", nulling refresh token = " + userService.getCurrentUser().getEmpireRefreshToken());
                    userService.getCurrentUser().setEmpireRefreshToken(null);
                    userService.save(userService.getCurrentUser());
                    List<EmpirePurchaseOrder> empirePurchaseOrderList = new ArrayList<>();
                    EmpirePurchaseOrder empirePurchaseOrder = new EmpirePurchaseOrder();
                    empirePurchaseOrder.setJob_number(EMPIRE_MEDICAL_AUTHENTICATION_ALERT);
                    empirePurchaseOrderList.add(empirePurchaseOrder);
                    return empirePurchaseOrderList;
                }
            }
            List<EmpirePurchaseOrder> empirePurchaseOrderList = processJsonStringIntoEmpirePurchaseOrderList(empireAccessToken, empireOrderLinesUrlPage, body);
            //System.out.println(response.getBody());
            //System.out.println("list size = "+empirePurchaseOrderList.size());
            return empirePurchaseOrderList;
        }
    }

    public StringBuilder getParamString(Map<String, String> appendParameters) {
        StringBuilder sb = new StringBuilder();
        appendParameters.forEach((key, value) -> {
            sb.append("&" + key + "=" + value);
        });
        return sb;
    }

    public List<EmpirePurchaseOrder> processJsonStringIntoEmpirePurchaseOrderList(String accessToken, String empireOrderLinesUrlPage, String body) {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = null;
        List<EmpirePurchaseOrder> empirePurchaseOrderList = null;
        try {
            //System.out.println("body = \n" + body);
            root = mapper.readTree(body);
            empirePurchaseOrderList = populateEmpirePurchaseOrderListFromJsonResponseBody(body);

            JsonNode links = root.path("links");
            JsonNode next = links.path("next");

            if (next != null && !"null".equals(String.valueOf(next))) {
                String temp = String.valueOf(next);
                String pageParam = "&page=";
                if (temp.contains("?page=")) {
                    pageParam = "?page=";
                }
                String nextPage = temp.substring(temp.indexOf(pageParam));
                JsonNode last = links.path("last");
                temp = String.valueOf(last);
                String lastPage = temp.substring(temp.indexOf(pageParam));
                JsonNode meta = root.path("meta");
                JsonNode total = meta.path("total");
                //System.out.println("total = "+String.valueOf(total));
                String nP = nextPage.substring(pageParam.length());
                String lP = lastPage.substring(pageParam.length());
                Integer nextPagePosition = Integer.parseInt(nP.replace("\"", ""));
                Integer lastPagePosition = Integer.parseInt(lP.replace("\"", ""));
                //Integer remainder = Integer.parseInt(String.valueOf(total)) % perPage;
                //System.out.println("total = "+String.valueOf(total) +" pages = "+lastPagePosition+" remainder = "+remainder);
//                for (Integer i = nextPagePosition; i <= lastPagePosition; i++) {
//                    if(empireOrderLinesUrlPage.contains("?")) {
//                        pageParam = "&page=";
//                    }
//                    System.out.println("empire 2 url = "+empireOrderLinesUrlPage+ pageParam + i);
//                    body = empireRestCallService.getEmpireOrderLinesPageDataBody(accessToken, empireOrderLinesUrlPage+ pageParam + i);
//                    empirePurchaseOrderList.addAll(populateEmpirePurchaseOrderListFromJsonResponseBody(body));
//                    //System.out.println("loop empirePurchaseOrderList size = "+ empirePurchaseOrderList.size());
//                }
            }
            //System.out.println("List<EmpirePurchaseOrder> size = " + empirePurchaseOrderList.size());
        } catch (IOException e) {
            empireErrorMessage(userService.getCurrentUser(), StringUtil.getEmpireHtmlError(e));
        }
        return empirePurchaseOrderList;
    }

    public List<EmpirePurchaseOrder> populateEmpirePurchaseOrderListFromJsonResponseBody(String body) {
        List<EmpirePurchaseOrder> empirePurchaseOrderList = null;
        JsonNode data = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(body);
            data = root.path("data");
            empirePurchaseOrderList = mapper.readValue(String.valueOf(data), new TypeReference<List<EmpirePurchaseOrder>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Error - Empire object serialization failure on data:\n" + String.valueOf(data) + "\n" + StringUtil.getExceptionAsString(e));
        }
        return empirePurchaseOrderList;
    }

    public void getTokenMapIfAccessTokenHasExpiredElseReturnNull() {
        if (isShouldRefreshTokenNow()) {
            //log.info("Empire: "+userService.getCurrentUser().getCompany().getName() + ":" + userService.getCurrentUser().getUsername() + ":Empire refreshing access token");
            getAccessTokenFromRefreshToken();
        }
    }

    public boolean isShouldRefreshTokenNow() {
        Long minusSeconds = 20L;
        try {
            minusSeconds = Long.parseLong(empireMinusSecondsFromAccessTokenAvailabilityForIsShouldRefresh);
        } catch (Exception e) {
            empireErrorMessage(userService.getCurrentUser(), ErrorMessage.getFullInfo(e));
        }
        String empireAccessToken = getEmpireAccessToken();
        return userService.getCurrentUser().getEmpireRefreshToken() == null
                || empireAccessToken == null
                || (empireAccessToken != null && getSecondsUntilAccessTokenExpires() < minusSeconds);
    }

    public Map<String, String> getAccessTokenFromRefreshToken() {
        User user = userService.getCurrentUser();
        if (user.getEmpireRefreshToken() == null) {
            updateUserEmpireAccessTokenMap(user, null);
            updateUserAccessTokenExpirationMap(user, LocalDateTime.now());
            return null;
        }
        Map<String, String> accessTokenFromRefreshTokenMap = getAccessTokenFromRefreshToken(user);
        if (accessTokenFromRefreshTokenMap == null) {
            int empireRetries = 8;
            try {
                empireRetries = Integer.parseInt(empireGetTokenRetries);
            } catch (Exception e) {
                empireErrorMessage(user, ErrorMessage.getFullInfo(e));
            }
            for (int i = 0; i < Integer.parseInt(empireGetTokenRetries); i++) {
                int empireSleepThread = 300;
                try {
                    empireSleepThread = Integer.parseInt(empireGetTokenRetryThreadSleep);
                } catch (Exception e) {
                    empireErrorMessage(user, ErrorMessage.getFullInfo(e));
                }
                try {
                    //System.out.println("tokenMap is null: try it = " + (i + 1));
                    Thread.sleep(Integer.parseInt(empireGetTokenRetryThreadSleep));
                    accessTokenFromRefreshTokenMap = getAccessTokenFromRefreshToken(user);
                    if (accessTokenFromRefreshTokenMap != null) {
                        return accessTokenFromRefreshTokenMap;
                    }
                } catch (InterruptedException e) {
                    empireErrorMessage(user, ErrorMessage.getFullInfo(e));
                }
            }
            user.setEmpireRefreshToken(null);
            userService.save(user);
        } else {
            return accessTokenFromRefreshTokenMap;
        }
        return null;
    }

    public Long getSecondsUntilAccessTokenExpires() {
        if (getUserAccessTokenExpirationMap().get(userService.getCurrentUser().getId()) != null) {
            return ChronoUnit.SECONDS.between(LocalDateTime.now(), getUserAccessTokenExpirationMap().get(userService.getCurrentUser().getId()));
        }
        return 0L;
    }

    public void getRefreshedEmpireRefreshToken() {
        getTokenMapIfAccessTokenHasExpiredElseReturnNull();
    }

    public void setEmpireOrderLinesUrl(String empireOrderLinesUrl) {
        this.empireOrderLinesUrl = empireOrderLinesUrl;
    }

    public List<String> retrieveEmpireRedirectUrl(Long patientId, Long prescriptionId, String patientFirstName, String patientLastName, HttpServletRequest request, HttpServletResponse response) {
        String url = request.getRequestURL().toString();
        String uri = request.getRequestURI();
        String baseUrl = url.substring(0, url.indexOf(uri));

        User user = userService.getCurrentUser();

        Prescription prescription = prescriptionService.findOne(prescriptionId);
        Company c = companyService.findByKey(TenantContext.getCurrentTenant());
        Long companyId = c.getId();
        String partUrl;
        getTokenMapIfAccessTokenHasExpiredElseReturnNull();
        String empireAccessToken = getEmpireAccessToken();
        if (isUserCompanyAccessValid(user, companyId) && prescription != null && prescription.getPatientId().compareTo(patientId) == 0) {
            partUrl = "/%23/app/purchasing/empire-orders&prescription_id=" + companyId + ":" + prescriptionId + "&patient_first_name=" + patientFirstName + "&patient_last_name=" + patientLastName + "&patient_id=" + patientId + "&access_token=" + empireAccessToken;
        } else {
            String message = ":Empire shop now mismatch: User company id=" + user.getCompanyId() +
                    ", passed parm=" + companyId;
            if (prescription == null) {
                message += ", prescription for prescriptionId passed parm, " + prescriptionId + ", is null";
            } else {
                message += ", prescription patient id=" + prescription.getPatientId() + ", passed parm=" + patientId;
            }
            log.error(userService.getCurrentUser().getCompany().getName() + ":" + userService.getCurrentUser().getUsername() + message);
            partUrl = "/%23/app/purchasing/empire-orders" + "&access_token=" + empireAccessToken;
        }
        empireAccessTokenAddCookie(response);
        String encodedParams = "redirect_url=" + baseUrl + partUrl;
        String returnUrl = empireWebsite + "/oauth/bridge/order-form?" + encodedParams;
        //log.info("Empire: "+userService.getCurrentUser().getCompany().getName() + ":" + userService.getCurrentUser().getUsername() + ":refresh token=" + userService.getCurrentUser().getEmpireRefreshToken() + ":Empire shop now request:" + returnUrl);
        List<String> results = new ArrayList<>();
        results.add(returnUrl);
        return results;
    }

    private boolean isUserCompanyAccessValid(User user, Long companyId) {
        List<Long> userCompanies = user.getCompanies().stream()
                .map(Company::getId)
                .toList();
        return companyId != null && user.getCompanyId().compareTo(companyId) == 0
                || userCompanies.contains(companyId);
    }

    public void empireAccessTokenAddCookie(HttpServletResponse response) {
        String empireAccessToken = getEmpireAccessToken();
        if (empireAccessToken != null) {
            Cookie xt = tokenUtils.tokenCookie(AuthoritiesConstants.XSRF_TOKEN, empireAccessToken);
            response.addCookie(xt);
        }
    }

    public Map<Long, String> getUserEmpireAccessTokenMap() {
        return userEmpireAccessTokenMap;
    }

    public Map<Long, LocalDateTime> getUserAccessTokenExpirationMap() {
        return userAccessTokenExpirationMap;
    }
}
