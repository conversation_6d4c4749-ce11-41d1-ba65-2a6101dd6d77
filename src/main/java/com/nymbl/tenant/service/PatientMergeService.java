package com.nymbl.tenant.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;

@Slf4j
@Service
public class PatientMergeService {

    private final PatientService patientService;
    private final PatientInsuranceService patientInsuranceService;
    private final PrescriptionService prescriptionService;

    private final FileService fileService;
    private final NoteService noteService;
    private final PaymentService paymentService;
    private final PurchaseOrder_ItemService purchaseOrder_ItemService;
    private final TaskService taskService;
    private final AppointmentService appointmentService;
    private final CriticalMessageService criticalMessageService;

    @Autowired
    public PatientMergeService(
    				PatientService patientService,
    				PatientInsuranceService patientInsuranceService,
		    	    PrescriptionService prescriptionService,
		    	    FileService fileService,
		    	    NoteService noteService,
		    	    PaymentService paymentService,
		    	    PurchaseOrder_ItemService purchaseOrder_ItemService,
		    	    TaskService taskService,
		    	    AppointmentService appointmentService,
		    	    CriticalMessageService criticalMessageService) {

    	this.patientService = patientService;
        this.patientInsuranceService = patientInsuranceService;
        this.prescriptionService = prescriptionService;
        this.fileService = fileService;
        this.noteService = noteService;
        this.paymentService = paymentService;
        this.purchaseOrder_ItemService = purchaseOrder_ItemService;
        this.taskService = taskService;
        this.appointmentService = appointmentService;
        this.criticalMessageService = criticalMessageService;
    }

    public void mergePatient(Long fromPatientId, Long toPatientId) {

    	boolean[] isPatientActive = new boolean[] {true};
    	patientService.findPatientById(fromPatientId)
    		.ifPresent(patient -> {
    			if (patient.getActive()) {
        			patient.setActive(false);
        			patient.setFirstName("(DUPLICATE) " + patient.getFirstName());

        			String mergePatientMessage = "Merged into patient #" + toPatientId;
        			patient.setCriticalMessage(patient.getCriticalMessage() != null ?
        					mergePatientMessage + patient.getCriticalMessage() : mergePatientMessage);
        			patientService.save(patient);
    			}
    			else {
    				isPatientActive[0] = false;
    			}
    		});

    	if (isPatientActive[0]) {
        	patientService.findPatientById(toPatientId)
    		.ifPresent(patient -> {
    			patient.setCriticalMessage(patient.getCriticalMessage() != null ?
    					patient.getCriticalMessage() + String.format(" Patient #%d was merged in.", fromPatientId) : 
    					String.format("Patient #%d was merged in.", fromPatientId));
    			patientService.save(patient);
    		});
    	}

    	patientInsuranceService.findByPatientId(fromPatientId)
    		.forEach(insurance -> {
    			insurance.setActive(false);
    			insurance.setPatientId(toPatientId);
    			patientInsuranceService.save(insurance);
    		});

    	prescriptionService.findAllByPatientId(fromPatientId).stream()
			.peek(prescription -> prescription.setPatientId(toPatientId)).forEach(prescriptionService::save);

    	fileService.findByPatientId(fromPatientId).stream()
			.peek(file -> file.setPatientId(toPatientId)).forEach(fileService::save);

    	noteService.getByPatientId(fromPatientId, false, false).stream()
			.peek(note -> note.setPatientId(toPatientId)).forEach(noteService::save);

    	paymentService.findByPatientId(fromPatientId).stream()
			.peek(payment -> payment.setPatientId(toPatientId)).forEach(paymentService::save);

    	purchaseOrder_ItemService.findAllByPatientId(fromPatientId).stream()
			.peek(item -> item.setPatientId(toPatientId)).forEach(purchaseOrder_ItemService::save);

    	taskService.findByPatientId(fromPatientId).stream()
			.peek(task -> task.setPatientId(toPatientId)).forEach(taskService::save);

    	appointmentService.getByPatientId(fromPatientId, true).stream()
			.peek(appointment -> appointment.setPatientId(toPatientId)).forEach(appointmentService::save);

    	if (isPatientActive[0]) {
    		criticalMessageService.getByPatientId(fromPatientId).stream()
    			.peek(message -> message.setPatientId(toPatientId)).forEach(criticalMessageService::save);

            criticalMessageService.save(criticalMessageService.createCriticalMessage(
            		MessageFormat.format("Patient #{0,number,#} was merged into patient #{1,number,#}. Patient #{0,number,#} was deactivated.", fromPatientId, toPatientId),
        			null,
        			fromPatientId));
    	}
    }
}
