package com.nymbl.tenant.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

@Getter
@Setter
@Entity
@Table(name = "inventory_item")
@Audited
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id", "branchId", "inCart", "itemId", "lowStock", "needNotification", "ordered", "preferredStock", "quantity"
})
@JsonIgnoreProperties(ignoreUnknown = true)
public class InventoryItem extends ModelStub {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    @Column(name = "branch_id")
    private Long branchId;

    @NotAudited
    @Transient
    private Long inCart;

    @Column(name = "item_id")
    private Long itemId;

    @Column(name = "low_stock")
    private Long lowStock;

    @Transient
    @NotAudited
    private boolean needNotification = false;

    @NotAudited
    @Transient
    private Long oldId;

    @NotAudited
    @Transient
    private Long oldLowStock;

    @NotAudited
    @Transient
    private Long oldPreferredStock;

    @NotAudited
    @Transient
    private Long oldQuantity;

    @NotAudited
    @Transient
    private Long ordered;

    @NotAudited
    @Transient
    private Long backordered;

    @Column(name = "preferred_stock")
    private Long preferredStock;

    @Column(name = "quantity")
    private Long quantity;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "branch_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "FK_inventoryitem_branchid"))
    private Branch branch;

    @NotAudited
    @ManyToOne
    @JoinColumn(name = "item_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "FK_inventoryitem_itemid"))
    private Item item;
}
