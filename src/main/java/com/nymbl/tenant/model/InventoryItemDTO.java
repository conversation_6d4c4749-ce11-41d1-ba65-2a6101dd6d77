package com.nymbl.tenant.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "inventory_item")
@Audited
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "id", "branchId", "branchName", "inCart", "itemByManufacturerId", "itemCategoryId", "itemCategoryName", "itemDescription",
        "itemId", "itemName", "itemPartNumber", "itemPrice", "itemSku", "lowStock", "manufacturerId", "manufacturerName",
        "ordered", "backordered", "preferredStock", "quantity", "vendorId", "vendorName"
})
@JsonIgnoreProperties(ignoreUnknown = true)
public class InventoryItemDTO extends ModelStub {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    @Column(name = "branch_id")
    private Long branchId;

    @Column(name = "branch_name")
    private String branchName;

    @Column(name = "in_cart")
    private Long inCart;

    @Column(name = "item_by_manufacturer_id")
    private Long itemByManufacturerId;

    @Column(name = "item_category_id")
    private Long itemCategoryId;

    @Column(name = "item_category_name")
    private String itemCategoryName;

    @Column(name = "item_description")
    private String itemDescription;

    @Column(name = "item_id")
    private Long itemId;

    @Column(name = "item_name")
    private String itemName;

    @Column(name = "item_part_number")
    private String itemPartNumber;

    @Column(name = "item_price")
    private BigDecimal itemPrice;

    @Column(name = "item_sku")
    private String itemSku;

    @Column(name = "low_stock")
    private Long lowStock;

    @Column(name = "manufacturer_id")
    private Long manufacturerId;

    @Column(name = "manufacturer_name")
    private String manufacturerName;

    @Column(name = "ordered")
    private Long ordered;

    @Column(name = "backordered")
    private Long backordered;

    @Column(name = "preferred_stock")
    private Long preferredStock;

    @Column(name = "quantity")
    private Long quantity;

    @Column(name = "vendor_id")
    private Long vendorId;

    @Column(name = "vendor_name")
    private String vendorName;
}
