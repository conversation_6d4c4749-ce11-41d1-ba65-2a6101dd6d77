package com.nymbl.tenant.repository;

import com.nymbl.config.SQLConstantsReports;
import com.nymbl.config.SqlConstants;
import com.nymbl.config.dto.reports.*;
import com.nymbl.tenant.model.GeneralLedger;
import com.nymbl.tenant.model.GeneralLedgerUnion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 06/17/2021.
 */
public interface GeneralLedgerRepository extends JpaRepository<GeneralLedger, Long>, JpaSpecificationExecutor<GeneralLedger> {

    String truncateLedgerTableForHistoryPull = "TRUNCATE TABLE general_ledger";

    @Modifying
    @Transactional
    @Query(value = truncateLedgerTableForHistoryPull, nativeQuery = true)
    void truncateGeneralLedgerTable();

    @Modifying
    @Transactional
    @Query(value = SQLConstantsReports.gLImportSQL, nativeQuery = true)
    void importGeneralLedgerHistory();

    /**
     * If this query is updated you must update
     * GeneralLedgerService.writeMultitenantInsertStatement()
     */
    @Query(value = SQLConstantsReports.multitenantPreparedStatment, nativeQuery = true)
    List<Object[]> getAllLedgerEntriesWithMultitenantData();

//    @Query(value = SQLConstantsReports.rxSalesSummary, nativeQuery = true)
//    ArrayList<IRxSales> getRxSalesSummaryReport(Long branchId, Long start, Long end);

    @Query(value = SQLConstantsReports.rxSalesSummary2, nativeQuery = true)
    ArrayList<IRxSales> getRxSalesSummaryReport(Long branchId, Long start, Long end);

//    @Query(value = SQLConstantsReports.rxLCodeSalesSummary, nativeQuery = true)
//    ArrayList<IRxLCodeSales> getRxSalesByLCodeSummaryReport(Long branchId, Long start, Long end);

    @Query(value = SQLConstantsReports.rxLCodeSalesSummary2, nativeQuery = true)
    ArrayList<IRxLCodeSales> getRxSalesByLCodeSummaryReport(Long branchId, Long start, Long end);

    @Query(value = SQLConstantsReports.hcpcDetailReport, nativeQuery = true)
    ArrayList<IHcpcDetail> getHcpcDetailReport(Long branchId, Long start, Long end);

    @Query(value = SQLConstantsReports.hcpcDetailTotals, nativeQuery = true)
    List<Object[]> hcpcDetailTotals(Long branchId, Long start, Long end);


    @Query(value = SQLConstantsReports.paymentsAdjustmentsReport, nativeQuery = true)
    ArrayList<IDailyClose> getDailyCloseReport(Long branchId, Long start, Long end, Boolean isSuperAdmin);

    // TODO why is this in the GL repo?
    @Query(value = SQLConstantsReports.zeroDollarPayments, nativeQuery = true)
    ArrayList<IPayments> getZeroDollarPayments(Long branchId, Date startDate, Date endDate);

    @Query(value = SQLConstantsReports.accountsReceivable, nativeQuery = true)
    ArrayList<IAccountsReceivable> getAccountReceivableReport(Long branchId, Long end);

    @Query(value = SQLConstantsReports.outstandingBalancesReport, nativeQuery = true)
    ArrayList<IOutstandingBalance> getOutstandingBalancesReport(Long branchId, Long end);

    @Query(value = SQLConstantsReports.accountsReceivableTotals, nativeQuery = true)
    List<Object[]> getAccountReceivableReportTotals(Long branchId, Long end);

    @Query(value = SQLConstantsReports.adjustmentsReport, nativeQuery = true)
    ArrayList<IAdjustments> getAdjustmentReport(Long branchId, Long start, Long end);


    @Query(value = SQLConstantsReports.practitionerAppointmentReport, nativeQuery = true)
    List<IPractitionerAppointments> getPractitionerAppointmentReports(Long branchId, Long userId, OffsetDateTime start, OffsetDateTime end);

    /**
     * This is not necessarily best of practice.
     * This query actually checks the existince of
     * an entry in general_ledger_static table.
     *
     * @param year
     * @param period
     * @return
     */
    @Query(value = "SELECT EXISTS (" +
            "SELECT id FROM general_ledger_static " +
            "WHERE gl_year = :year " +
            "AND gl_period = :period " +
            "AND active = true) ", nativeQuery = true)
    BigInteger isStaticGeneralLedgerEntryPresentByYearAndPeriod(Long year, Long period);

    @Modifying
    @Transactional
    @Query(value = SQLConstantsReports.populateStaticGeneralLedgerByPeriod, nativeQuery = true)
    void populateStaticGeneralLedgerByPeriod(Long year, Long period);

    @Modifying
    @Transactional
    @Query(value = SQLConstantsReports.setAllStaticGeneralLedgerTransactionInactiveByPeriod, nativeQuery = true)
    void markStaticGeneralLedgerEntriesFalseByPeriod(Long year, Long period);

    @Query(name = "getClaimsActivityReport", nativeQuery = true)
    List<ClaimsActivity> getClaimsActivityReport(Long branchId, Long start, Long end);

    String gl_columns = "`gl`.`id`, `gl`.`category_id`, `gl`.`gl_date`, `gl`.`gl_applied_date`, `gl`.`gl_year`, `gl`.`gl_period`, `gl`.`gl_account`, `gl`.`category`, `gl`.`sub_category`, `gl`.`amount`, `gl`.`abs_amount`, `gl`.`patient_id`, `gl`.`prescription_id`, `gl`.`claim_id`, `gl`.`branch_id`, `gl`.`patient_branch_id`, `gl`.`prescription_branch_id`, `gl`.`facility_id`, `gl`.`prescription_l_code_id`, `gl`.`l_code_id`, `gl`.`insurance_verification_id`, `gl`.`insurance_verification_l_code_id`, `gl`.`payment_id`, `gl`.`applied_payment_id`, `gl`.`applied_payment_l_code_id`, `gl`.`payer_type`, `gl`.`payment_type`, `gl`.`check_number`, `gl`.`insurance_company_id`, `gl`.`carrier_type`, `gl`.`patient_insurance_id`, `gl`.`device_type_id`, `gl`.`device_type`, `gl`.`treating_practitioner_id`, `gl`.`primary_care_physician_id`, `gl`.`therapist_id`, `gl`.`referring_physician_id`, `gl`.`claim_submission_date`, `gl`.`date_of_service`, `gl`.`prescription_date`, `gl`.`payment_date`, `gl`.`deposit_date`, `gl`.`applied_date`, `gl`.`rx_active`, `gl`.`patient_active`, `gl`.`last_updated`";

    public static String gl_union_columns = "`gl`.`category_id`, `gl`.`gl_date`, `gl`.`gl_applied_date`, `gl`.`gl_year`, `gl`.`gl_period`, `gl`.`gl_account`, `gl`.`category`, `gl`.`sub_category`, `gl`.`amount`, `gl`.`abs_amount`, `gl`.`patient_id`, `gl`.`prescription_id`, `gl`.`claim_id`, `gl`.`branch_id`, `gl`.`patient_branch_id`, `gl`.`prescription_branch_id`, `gl`.`facility_id`, `gl`.`prescription_l_code_id`, `gl`.`l_code_id`, `gl`.`insurance_verification_id`, `gl`.`insurance_verification_l_code_id`, coalesce(`gl`.`payment_id`,0) as payment_id, coalesce(`gl`.`applied_payment_id`,0) as applied_payment_id, coalesce(`gl`.`applied_payment_l_code_id`,0) as applied_payment_l_code_id, `gl`.`payer_type`, `gl`.`payment_type`, `gl`.`check_number`, `gl`.`insurance_company_id`, `gl`.`carrier_type`, `gl`.`patient_insurance_id`, `gl`.`device_type_id`, `gl`.`device_type`, `gl`.`treating_practitioner_id`, `gl`.`primary_care_physician_id`, `gl`.`therapist_id`, `gl`.`referring_physician_id`, `gl`.`claim_submission_date`, `gl`.`date_of_service`, `gl`.`prescription_date`, `gl`.`payment_date`, `gl`.`deposit_date`, `gl`.`applied_date`, `gl`.`rx_active`, `gl`.`patient_active`";

    @Query(value = "SELECT " + gl_columns + " from `general_ledger` `gl` join `gl_period` `p` on ((`p`.`year` = `gl`.`gl_year`) and (`p`.`period` = `gl`.`gl_period`))" +
            "WHERE `gl`.`claim_id` = :claimId and ((`p`.`status` = 'open') or ((`gl`.`gl_year` is null) and (`gl`.`gl_period` is null))) UNION ALL " +
            "SELECT " + gl_columns + " from `general_ledger_static` `gl` where `gl`.`claim_id` = :claimId AND `gl`.`active` is true", nativeQuery = true)
    List<GeneralLedger> findByClaimId(@Param("claimId") Long claimId);

    @Query(value = "SELECT " + gl_columns + " from `general_ledger` `gl` join `gl_period` `p` on ((`p`.`year` = `gl`.`gl_year`) and (`p`.`period` = `gl`.`gl_period`))" +
            "WHERE `gl`.`claim_id` IN :claimIds and ((`p`.`status` = 'open') or ((`gl`.`gl_year` is null) and (`gl`.`gl_period` is null))) UNION ALL " +
            "SELECT " + gl_columns + " from `general_ledger_static` `gl` where `gl`.`claim_id` IN :claimIds AND `gl`.`active` is true", nativeQuery = true)
    List<GeneralLedger> findByClaimIdIn(@Param("claimIds") List<Long> claimIds);

    @Query(value = "SELECT " + gl_columns + " from `general_ledger` `gl` join `gl_period` `p` on ((`p`.`year` = `gl`.`gl_year`) and (`p`.`period` = `gl`.`gl_period`))" +
            "WHERE ((`p`.`status` = 'open') or ((`gl`.`gl_year` is null) and (`gl`.`gl_period` is null))) UNION ALL " +
            "SELECT " + gl_columns + " from `general_ledger_static` `gl` where `gl`.`active` is true", nativeQuery = true)
    List<GeneralLedger> findAllGeneralLedgerTransactions();

    public static final String allGLUnionQuery = "SELECT " + gl_union_columns + " from `general_ledger` `gl` UNION SELECT " + gl_union_columns + " from `general_ledger_static` `gl` where active IS TRUE";

    @Query(name = "getGeneralLedgerUnionTransactions", nativeQuery = true)
    List<GeneralLedgerUnion> getGeneralLedgerUnionTransactions();

    @Query(value = "SELECT SUM(amount) AS ar FROM general_ledger WHERE claim_id = :id AND category = 'Accounts Receivable'", nativeQuery = true)
    List<Object[]> getArTotalForClaimId(Long id);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM multitenant.general_ledger WHERE tenant = :tenant", nativeQuery = true)
    void removeStaleTenantData(@Param("tenant") String tenant);

    @Modifying
    @Transactional
    @Query(value = SqlConstants.populateMultiTenantGeneralLedger, nativeQuery = true)
    void importGeneralLedgerData(String tenant,
                                 Long categoryId,
                                 java.sql.Date glDate,
                                 java.sql.Date glAppliedDate,
                                 Long glYear,
                                 Long glPeriod,
                                 String glAccount,
                                 String category,
                                 String subCategory,
                                 BigDecimal amount,
                                 BigDecimal absAmount,
                                 Long patientId,
                                 String patient,
                                 Long prescriptionId,
                                 Long claimId,
                                 Long branchId,
                                 String branch,
                                 Long patientBranchId,
                                 String patientBranch,
                                 Long prescriptionBranchId,
                                 String prescriptionBranch,
                                 Long facilityId,
                                 String facility,
                                 Long prescriptionLCodeId,
                                 Long lCodeId,
                                 String lCode,
                                 Long insuranceVerificationId,
                                 Long insuranceVerificationLCodeId,
                                 Long paymentId,
                                 Long appliedPaymentId,
                                 Long appliedPaymentLCodeId,
                                 String payerType,
                                 String paymentType,
                                 String checkNumber,
                                 Long insuranceCompanyId,
                                 String insuranceCompany,
                                 String carrierType,
                                 Long patientInsuranceId,
                                 Long deviceTypeId,
                                 String deviceTypeCategory,
                                 String deviceType,
                                 Long treatingPractitionerId,
                                 String treatingPractitioner,
                                 Long primaryCarePhysicianId,
                                 String primaryCarePhysician,
                                 Long therapistId,
                                 String therapist,
                                 Long referringPhysicianId,
                                 String referringPhysician,
                                 java.sql.Date claimSubmissionDate,
                                 java.sql.Date dateOfService,
                                 java.sql.Date prescriptionDate,
                                 java.sql.Date paymentDate,
                                 java.sql.Date depositDate,
                                 java.sql.Date appliedDate,
                                 Boolean rxActive,
                                 Boolean patientActive,
                                 Timestamp lastUpdated);

    List<GeneralLedger> findAllByPaymentId(Long paymentId);

//    @Transactional
//    @Modifying
//    @Query(value = SQLGeneralLedgerBuildRepository.generLedgerSalesInsert, nativeQuery = true)
//    void populateLiveGeneralLedgerSales();
//
//    @Transactional
//    @Modifying
//    @Query(value = SQLGeneralLedgerBuildRepository.generalLedgerPaymentsInsert, nativeQuery = true)
//    void populateLiveGeneralLedgerPayments();
//
//    @Transactional
//    @Modifying
//    @Query(value = SQLGeneralLedgerBuildRepository.generalLedgerLineAdjustmentsInsert, nativeQuery = true)
//    void populateLiveGeneralLedgerLineAdjustments();
//
//    @Transactional
//    @Modifying
//    @Query(value = SQLGeneralLedgerBuildRepository.generalLedgerArAdjustmentsInsert, nativeQuery = true)
//    void populateLiveGeneralLedgerArAdjustments();

//    @Transactional
//    @Modifying
//    @Query(value = SQLGeneralLedgerBuildRepository.deleteGeneralLedgerSales, nativeQuery = true)
//    void deleteGeneralLedgerSales();

//    @Transactional
//    @Modifying
//    @Query(value = SQLGeneralLedgerBuildRepository.deleteGeneralLedgerPayments, nativeQuery = true)
//    void deleteGeneralLedgerPayments();
//
//    @Transactional
//    @Modifying
//    @Query(value = SQLGeneralLedgerBuildRepository.deleteGeneralLedgerLineAdjustments, nativeQuery = true)
//    void deleteGeneralLedgerLineAdjustments();
//
//    @Transactional
//    @Modifying
//    @Query(value = SQLGeneralLedgerBuildRepository.deleteGeneralLedgerArAdjustments, nativeQuery = true)
//    void deleteGeneralLedgerArAdjustments();


    @Modifying
    @Transactional
    @Query(value = category1, nativeQuery = true)
    Integer importGeneralLedgerHistoryC1();

    @Modifying
    @Transactional
    @Query(value = category2, nativeQuery = true)
    Integer importGeneralLedgerHistoryC2();

    @Modifying
    @Transactional
    @Query(value = category3, nativeQuery = true)
    Integer importGeneralLedgerHistoryC3();

    @Modifying
    @Transactional
    @Query(value = category4, nativeQuery = true)
    Integer importGeneralLedgerHistoryC4();

    @Modifying
    @Transactional
    @Query(value = category5, nativeQuery = true)
    Integer importGeneralLedgerHistoryC5();

    @Modifying
    @Transactional
    @Query(value = category6, nativeQuery = true)
    Integer importGeneralLedgerHistoryC6();

    @Modifying
    @Transactional
    @Query(value = category7, nativeQuery = true)
    Integer importGeneralLedgerHistoryC7();

    @Modifying
    @Transactional
    @Query(value = category8, nativeQuery = true)
    Integer importGeneralLedgerHistoryC8();

    @Modifying
    @Transactional
    @Query(value = category9, nativeQuery = true)
    Integer importGeneralLedgerHistoryC9();

    @Modifying
    @Transactional
    @Query(value = category10, nativeQuery = true)
    Integer importGeneralLedgerHistoryC10();

    @Modifying
    @Transactional
    @Query(value = category11, nativeQuery = true)
    Integer importGeneralLedgerHistoryC11();

    @Modifying
    @Transactional
    @Query(value = category12, nativeQuery = true)
    Integer importGeneralLedgerHistoryC12();

    @Modifying
    @Transactional
    @Query(value = category13, nativeQuery = true)
    Integer importGeneralLedgerHistoryC13();

    @Modifying
    @Transactional
    @Query(value = category14, nativeQuery = true)
    Integer importGeneralLedgerHistoryC14();

    @Modifying
    @Transactional
    @Query(value = category15, nativeQuery = true)
    Integer importGeneralLedgerHistoryC15();

    @Modifying
    @Transactional
    @Query(value = category16, nativeQuery = true)
    Integer importGeneralLedgerHistoryC16();

    @Modifying
    @Transactional
    @Query(value = category17, nativeQuery = true)
    Integer importGeneralLedgerHistoryC17();

    @Modifying
    @Transactional
    @Query(value = category18, nativeQuery = true)
    Integer importGeneralLedgerHistoryC18();

    @Modifying
    @Transactional
    @Query(value = category19, nativeQuery = true)
    Integer importGeneralLedgerHistoryC19();

    @Modifying
    @Transactional
    @Query(value = category20, nativeQuery = true)
    Integer importGeneralLedgerHistoryC20();

    @Modifying
    @Transactional
    @Query(value = category21, nativeQuery = true)
    Integer importGeneralLedgerHistoryC21();

    @Modifying
    @Transactional
    @Query(value = category22, nativeQuery = true)
    Integer importGeneralLedgerHistoryC22();

    @Modifying
    @Transactional
    @Query(value = category23, nativeQuery = true)
    Integer importGeneralLedgerHistoryC23();

    @Modifying
    @Transactional
    @Query(value = category24, nativeQuery = true)
    Integer importGeneralLedgerHistoryC24();

    String insertWithFields = """
            INSERT INTO general_ledger (
            `category_id`, `gl_date`, `gl_applied_date`,`gl_year`, `gl_period`,`gl_account`, `category`, `sub_category`, `amount`,
            `abs_amount`, `patient_id`, `prescription_id`, `claim_id`, `branch_id`, `patient_branch_id`, `prescription_branch_id`, `facility_id`, `prescription_l_code_id`, `l_code_id`,
            `insurance_verification_id`, `insurance_verification_l_code_id`, `payment_id`, `applied_payment_id`,
            `applied_payment_l_code_id`, `payer_type`, `payment_type`, `check_number`, `insurance_company_id`, `carrier_type`, `patient_insurance_id`,
            `device_type_id`, `device_type`, `treating_practitioner_id`, `primary_care_physician_id`, `therapist_id`,
            `referring_physician_id`, `claim_submission_date`, `date_of_service`, `prescription_date`, `payment_date`, `deposit_date`,
            `applied_date`, `rx_active`, `patient_active`)
            SELECT x.category_id, x.gl_date, x.gl_applied_date,
            CASE WHEN x.gl_applied_date IS NOT NULL AND (SELECT period.year FROM gl_period period WHERE unix_timestamp(x.gl_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(period.end_date) AND unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) > 0)
            THEN (SELECT period.year FROM gl_period period WHERE unix_timestamp(x.gl_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(period.end_date) AND unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) LIMIT 1 )
            ELSE (SELECT period.year FROM gl_period period WHERE unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) ORDER BY (period.year * 100 + period.period) DESC LIMIT 1)
            END AS gl_year,
            CASE WHEN x.gl_applied_date IS NOT NULL AND (SELECT period.year FROM gl_period period WHERE unix_timestamp(x.gl_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(period.end_date) AND unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) > 0)
            THEN (SELECT period.period  FROM gl_period period WHERE unix_timestamp(x.gl_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(period.end_date) AND unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) LIMIT 1 )
            ELSE (SELECT period.period  FROM gl_period period WHERE unix_timestamp(x.gl_applied_date) BETWEEN unix_timestamp(period.start_date) AND unix_timestamp(COALESCE(period.closed_date, '2030-01-01')) ORDER BY (period.year * 100 + period.period) DESC LIMIT 1)
            END AS gl_period,
            x.gl_account, x.category, x.sub_category, x.amount, x.abs_amount, x.patient_id, x.prescription_id, x.claim_id, x.billing_branch_id, x.patient_branch_id, x.prescription_branch_id,
            x.facility_id, x.plc_id, x.l_code_id,x.insurance_verification_id, x.ivlc_id, x.payment_id, x.ap_id, x.aplc_id,
            x.payer_type, x.payment_type, x.check_number, x.insurance_company_id, x.carrier_type, x.patient_insurance, x.device_type_id, x.device_type,
            x.treating_practitioner_id, x.primary_care_physician_id,  x.therapist_id, x.referring_physician_id, x.claim_submission_date,
            x.date_of_service, x.prescription_date, x.payment_date, x.deposit_date, x.applied_date, x.rx_active, x.pt_active
            FROM
            """;


    //***SALES / TOTAL CHARGES***
    //Billable/Sales
    String category1 = insertWithFields + """
                (SELECT DISTINCT 1 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'billable')  , '&b&', COALESCE(b.code, '000') ), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account,
                'Charge' as category, 'Billable' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id,
                primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type,
                null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
                rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date,
                c.date_of_service as date_of_service, rx.prescription_date, null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date,
                COALESCE(ivlc.total_charge, 0.00) * - 1 as amount , ABS(COALESCE(ivlc.total_charge, 0.00)) as abs_amount, rx.active as rx_active, pt.active as pt_active
                FROM insurance_verification_l_code ivlc
                JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id
                JOIN prescription rx ON rx.id = iv.prescription_id
                JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id
                JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id
                JOIN device_type dt ON rx.device_type_id = dt.id
                JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id
                AND pin.id = (
                SELECT iv1.patient_insurance_id
                FROM insurance_verification iv1
                WHERE iv1.prescription_id = rx.id
                ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC,
                iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1)
                LEFT JOIN (
                    SELECT rx1.id, MIN(submission_date) as subDate , MIN(c1.id) as primary_claim
                    FROM claim_submission cs1
                    INNER JOIN claim c1 ON cs1.claim_id = c1.id
                    INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id
                    GROUP BY rx1.id
                ) as x ON x.id = rx.id
                JOIN patient pt ON rx.patient_id = pt.id
                LEFT JOIN branch b ON c.billing_branch_id = b.id) as x
            """;

    //Sales Tax (Negative)
    String category2 = insertWithFields + """
                (SELECT DISTINCT 2 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_tax')  , '&b&', COALESCE(b.code, '000') ), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account,
                'Tax' as category, 'Sales Tax' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id,
                primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type,
                null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
                rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date,
                c.date_of_service as date_of_service, rx.prescription_date, null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date,
                COALESCE(ivlc.sales_tax, 0.00) * -1 as amount, ABS(COALESCE(ivlc.sales_tax, 0.00)) as abs_amount, rx.active as rx_active, pt.active as pt_active
                FROM insurance_verification_l_code ivlc
                JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id
                JOIN prescription rx ON rx.id = iv.prescription_id
                JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id
                JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id
                JOIN device_type dt ON rx.device_type_id = dt.id
                JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id
                AND pin.id = (
                SELECT iv1.patient_insurance_id FROM insurance_verification iv1
                WHERE iv1.prescription_id = rx.id
                ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC, iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1)
                LEFT JOIN(
                SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim
                FROM claim_submission cs1
                INNER JOIN claim c1 ON cs1.claim_id = c1.id
                INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id
                GROUP BY rx1.id) as x ON x.id = rx.id
                JOIN patient pt ON rx.patient_id = pt.id
                LEFT JOIN branch b ON c.billing_branch_id = b.id
                WHERE ivlc.use_sales_tax = 1 and ivlc.sales_tax IS NOT NULL) as x
            """;

    //*** CASH ***
    //Payments
    String category3 = insertWithFields + """
            (SELECT DISTINCT 3 as category_id, (SELECT x.account FROM gl_account x WHERE x.name = 'unapplied') as gl_account,
            'Payments' as category, 'Unapplied' as sub_category, p.patient_id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id ,  null as facility_id, CASE WHEN p.prescription_id IS NULL THEN c.prescription_id ELSE p.prescription_id END as prescription_id,
            p.claim_id as claim_id, null as plc_id, null as l_code_id, null as ivlc_id, p.id as payment_id, null as ap_id, null as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, null as insurance_company_id, null as carrier_type, null as patient_insurance, null as insurance_verification_id, null as device_type_id, null as device_type,
            null as treating_practitioner_id, null as primary_care_physician_id, null as therapist_id, null as referring_physician_id, null as claim_submission_date,
            null as date_of_service, null as prescription_date, p.date as payment_date, p.deposit_date as deposit_date, null as applied_date, p.date as gl_date, p.date as gl_applied_date,
            COALESCE(p.amount, 0.00) * -1 as amount, ABS(COALESCE(p.amount, 0.00)) as abs_amount, null as rx_active, null as pt_active
            FROM payment p
            LEFT JOIN claim c ON p.claim_id = c.id
            LEFT JOIN patient pt ON p.patient_id = pt.id
            LEFT JOIN prescription rx ON c.prescription_id = rx.id
            WHERE p.adjustment = 0.00 and p.adjustment_id IS NULL) as x
            """;


    //Payment Reversal  -- 
    String category4 = insertWithFields + """
            (SELECT DISTINCT 4 as category_id, (SELECT x.account FROM gl_account x WHERE x.name = 'unapplied') as gl_account,
            'Payments' as category, 'Applied' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id,
            c.id as claim_id, plc.id as plc_id, plc.l_code_id, null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date,
            c.date_of_service as date_of_service, rx.prescription_date, p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.amount, 0.00) as amount, ABS(COALESCE(aplc.amount, 0.00)) as abs_amount, rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            WHERE p.adjustment = 0.00 and p.adjustment_id IS NULL) as x
            """;

    String category5 = insertWithFields + """
            (SELECT DISTINCT 5 as category_id,
            CASE WHEN c.billing_branch_id IS NOT NULL
            THEN (REPLACE((SELECT x.account FROM gl_account x WHERE x.type = 'payment_type' AND x.name = p.payment_type), '&b&', COALESCE(b.code, '000')))
            WHEN c.billing_branch_id IS NULL AND pt.primary_branch_id IS NOT NULL
            THEN (REPLACE((SELECT x.account FROM gl_account x WHERE x.type = 'payment_type' AND x.name = p.payment_type), '&b&', COALESCE(bp.code, '000')))
            ELSE REPLACE((SELECT x.account FROM gl_account x WHERE x.type = 'payment_type' AND x.name = p.payment_type), '&b&', '00')
            END as gl_account ,
            'Cash' as category, p.payment_type as sub_category, p.patient_id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , null as facility_id, CASE WHEN p.prescription_id IS NULL THEN c.prescription_id ELSE p.prescription_id END as prescription_id,
            p.claim_id as claim_id, null as plc_id, null as l_code_id, null as ivlc_id, p.id as payment_id, null as ap_id, null as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, null as insurance_company_id, null as carrier_type, null as patient_insurance, null as insurance_verification_id, null as device_type_id, null as device_type,
            null as treating_practitioner_id, null as primary_care_physician_id, null as therapist_id, null as referring_physician_id, null as claim_submission_date,
            null as date_of_service, null as prescription_date, p.date as payment_date, p.deposit_date as deposit_date, null as applied_date, p.date as gl_date, p.date as gl_applied_date,
            COALESCE(p.amount, 0.00) as amount, ABS(COALESCE(p.amount, 0.00)) as abs_amount, null as rx_active, null as pt_active
            FROM payment p
            LEFT JOIN claim c ON p.claim_id = c.id
            LEFT JOIN patient pt ON p.patient_id = pt.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            LEFT JOIN branch bp ON bp.id = pt.primary_branch_id
            LEFT JOIN prescription rx ON c.prescription_id = rx.id
            WHERE p.adjustment = 0.00 and p.adjustment_id IS NULL) as x
            """;

    //*** AR ADJUSTMENTS ***
    String category6 = insertWithFields + """
            (SELECT DISTINCT 6 as category_id, REPLACE(REPLACE((SELECT x.gl_account FROM adjustment x WHERE x.name = adj.name AND x.id = aplc.adjustment_type)  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account,
            'Adjustments' as category, adj.name as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id,  dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            CASE WHEN adj.operation = '+' THEN COALESCE(aplc.adjustment, 0.00) * -1 ELSE COALESCE(aplc.adjustment, 0.00) END as amount, ABS(COALESCE(aplc.adjustment, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN adjustment adj ON aplc.adjustment_type = adj.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment <> 0.00 AND aplc.adjustment IS NOT NULL) as x
            """;

    //*** LINE ADJUSTMENTS *** --
    //CO-45 ESTIMATION
    String category7 = insertWithFields + """
            (SELECT DISTINCT 7 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account,
            'Line Adjustments' as category, 'CO-45 Estimate' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id,
            primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type,
            null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date,
            (COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00)) as amount, ABS((COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00))) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM insurance_verification_l_code ivlc
            JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id
            JOIN prescription rx ON rx.id = iv.prescription_id
            JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id
            JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id
            AND pin.id = (
            SELECT iv1.patient_insurance_id FROM insurance_verification iv1
            WHERE iv1.prescription_id = rx.id
            ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC,
            iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1)
            LEFT JOIN(
            SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim
            FROM claim_submission cs1
            INNER JOIN claim c1 ON cs1.claim_id = c1.id
            INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id
            GROUP BY rx1.id) as x ON x.id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id) as x
            """;

    //CO-45 REVERSAL
    String category8 = insertWithFields + """
            (SELECT DISTINCT 8 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account,
            'Line Adjustments' as category, 'CO-45 Reversal' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id,
            primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, x.payment_id as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type,
            x.check_number as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            subPayment_date as payment_date, null as deposit_date, subApplied_date as applied_date, subPayment_date as gl_date, subApplied_date as gl_applied_date,
            (COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00)) * -1 as amount, ABS((COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00))) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM insurance_verification_l_code ivlc
            JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id
            JOIN prescription rx ON rx.id = iv.prescription_id
            JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id
            JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id
            AND pin.id = (
            SELECT iv1.patient_insurance_id FROM insurance_verification iv1
            WHERE iv1.prescription_id = rx.id
            ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC,
            iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1)
            LEFT JOIN(
            SELECT rx1.id, MIN(p.id) as payment_id, MIN(ap.applied_date) as subApplied_date, MIN(p.date) as subPayment_date, MAX(p.check_number) as check_number
            FROM payment p
            INNER JOIN applied_payment ap ON p.id = ap.payment_id
            INNER JOIN applied_payment_l_code aplc ON ap.id = aplc.applied_payment_id
            INNER JOIN claim c ON ap.claim_id = c.id
            INNER JOIN prescription rx1 ON c.prescription_id = rx1.id
            GROUP BY rx1.id  ) as x ON x.id = rx.id
            LEFT JOIN(
            SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim
            FROM claim_submission cs1
            INNER JOIN claim c1 ON cs1.claim_id = c1.id
            INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id
            GROUP BY rx1.id) as a ON a.id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN applied_payment_l_code aplc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id) as x
            """;

    //All Other Line Adjustments
    String category9 = insertWithFields + """
            (SELECT DISTINCT 9 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account,
            'Line Adjustments' as category,  aplc.adjustment_type1 as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.adjustment_amount1, 0.00) as amount, ABS(COALESCE(aplc.adjustment_amount1, 0.00)) as abs_amount, rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment_amount1 <> 0.00 AND aplc.adjustment_amount1 IS NOT NULL
            AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type1,INSTR(aplc.adjustment_type1,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date)
            OR (code = SUBSTRING(aplc.adjustment_type1,INSTR(aplc.adjustment_type1,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0
            AND aplc.adjustment_type1 NOT LIKE 'PR-%') as x
            """;

    String category10 = insertWithFields + """
            (SELECT DISTINCT 10 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account,
            'Line Adjustments' as category,  aplc.adjustment_type2 as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.adjustment_amount2, 0.00) as amount, ABS(COALESCE(aplc.adjustment_amount2, 0.00)) as abs_amount, rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment_amount2 <> 0.00 AND aplc.adjustment_amount2 IS NOT NULL
            AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type2,INSTR(aplc.adjustment_type2,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date)
            OR (code = SUBSTRING(aplc.adjustment_type2,INSTR(aplc.adjustment_type2,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0
            AND aplc.adjustment_type2 NOT LIKE 'PR-%') as x
            """;

    String category11 = insertWithFields + """
            (SELECT DISTINCT 11 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account,
            'Line Adjustments' as category,  aplc.adjustment_type3 as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.adjustment_amount3, 0.00) as amount, ABS(COALESCE(aplc.adjustment_amount3, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment_amount3 <> 0.00 AND aplc.adjustment_amount3 IS NOT NULL
            AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type3,INSTR(aplc.adjustment_type3,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date)
            OR (code = SUBSTRING(aplc.adjustment_type3,INSTR(aplc.adjustment_type3,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0
            AND aplc.adjustment_type3 NOT LIKE 'PR-%') as x
            """;

    String category12 = insertWithFields + """
            (SELECT DISTINCT 12 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account,
            'Line Adjustments' as category,  aplc.adjustment_type4 as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.adjustment_amount4, 0.00) as amount, ABS(COALESCE(aplc.adjustment_amount4, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment_amount4 <> 0.00 AND aplc.adjustment_amount4 IS NOT NULL
            AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type4,INSTR(aplc.adjustment_type4,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date)
            OR (code = SUBSTRING(aplc.adjustment_type4,INSTR(aplc.adjustment_type4,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0
            AND aplc.adjustment_type4 NOT LIKE 'PR-%') as x
            """;

    String category13 = insertWithFields + """
            (SELECT DISTINCT 13 as category_id, REPLACE(REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales_contract')  , '&b&', COALESCE(b.code, '000')), '&dt&', (SELECT y.account FROM gl_account y WHERE y.type = 'device_type' AND y.name = dt.orthotic_or_prosthetic)) as gl_account,
            'Line Adjustments' as category,  aplc.adjustment_type5 as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.adjustment_amount5, 0.00) as amount, ABS(COALESCE(aplc.adjustment_amount5, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment_amount5 <> 0.00 AND aplc.adjustment_amount5 IS NOT NULL
            AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type5,INSTR(aplc.adjustment_type5,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date)
            OR (code = SUBSTRING(aplc.adjustment_type5,INSTR(aplc.adjustment_type5,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0
            AND aplc.adjustment_type5 NOT LIKE 'PR-%') as x
            """;
    //******************************************************************* ACCOUNTS RECEIVABLE*** ****************************************************************
    //SALES AR
    String category14 = insertWithFields + """
            (SELECT DISTINCT 14 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000') ) as gl_account,
            'Accounts Receivable' as category, 'Sales' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id,
            primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type,
            null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date,
            c.date_of_service as date_of_service, rx.prescription_date, null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date,
            COALESCE(ivlc.total_charge, 0.00) as amount , ABS(COALESCE(ivlc.total_charge, 0.00)) as abs_amount ,
            rx.active as rx_active, pt.active as pt_active
            FROM insurance_verification_l_code ivlc
            JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id
            JOIN prescription rx ON rx.id = iv.prescription_id
            JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id
            JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id
            AND pin.id = (
            SELECT iv1.patient_insurance_id
            FROM insurance_verification iv1
            WHERE iv1.prescription_id = rx.id
            ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC,
            iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1)
            LEFT JOIN(
            SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim
            FROM claim_submission cs1
            INNER JOIN claim c1 ON cs1.claim_id = c1.id
            INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id
            GROUP BY rx1.id) as x ON x.id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id) as x
            """;

    //Sales AR
    String category15 = insertWithFields + """
            (SELECT DISTINCT 15 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'sales' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000') ) as gl_account,
            'Accounts Receivable' as category, 'Sales' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id , rx.facility_id, rx.id as prescription_id,
            primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type,
            null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date,
            c.date_of_service as date_of_service, rx.prescription_date, null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date,
            COALESCE(ivlc.sales_tax, 0.00) as amount, ABS(COALESCE(ivlc.sales_tax, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM insurance_verification_l_code ivlc
            JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id
            JOIN prescription rx ON rx.id = iv.prescription_id
            JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id
            JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id
            AND pin.id = (
            SELECT iv1.patient_insurance_id
            FROM insurance_verification iv1
            WHERE iv1.prescription_id = rx.id
            ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC,
            iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1)
            LEFT JOIN(
            SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim
            FROM claim_submission cs1
            INNER JOIN claim c1 ON cs1.claim_id = c1.id
            INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id
            GROUP BY rx1.id) as x ON x.id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE ivlc.use_sales_tax = 1 and ivlc.sales_tax IS NOT NULL) as x
            """;

    //Applied Payments AR ---
    String category16 = insertWithFields + """
            (SELECT DISTINCT 16 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'applied_payments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account,
            'Accounts Receivable' as category, 'Applied Payments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id,  rx.id as prescription_id,
            c.id as claim_id, plc.id as plc_id, plc.l_code_id, null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date,
            c.date_of_service as date_of_service, rx.prescription_date, p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.amount, 0.00) * -1 as amount, ABS(COALESCE(aplc.amount, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.amount <> 0.00 AND p.date IS NOT NULL) as x
            """;

    //*** AR ADJUSTMENTS AR***
    String category17 = insertWithFields + """
            (SELECT DISTINCT 17 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account,
            'Accounts Receivable' as category, 'Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            CASE WHEN adj.operation = '+' THEN COALESCE(aplc.adjustment, 0.00) ELSE COALESCE(aplc.adjustment, 0.00) * -1 END as amount, ABS(COALESCE(aplc.adjustment, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN adjustment adj ON aplc.adjustment_type = adj.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment <> 0.00 AND aplc.adjustment IS NOT NULL
            AND p.date IS NOT NULL) as x
            """;

    //CO-45 ESTIMATION
    String category18 = insertWithFields + """
            (SELECT DISTINCT 18 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account,
            'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id,
            primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, null as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type,
            null as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, subDate as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            null as payment_date, null as deposit_date, null as applied_date, COALESCE(c.date_of_service, rx.prescription_date) as gl_date, subDate as gl_applied_date,
            (COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00)) * -1 as amount, ABS(COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM insurance_verification_l_code ivlc
            JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id
            JOIN prescription rx ON rx.id = iv.prescription_id
            JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id
            JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id
            AND pin.id = (
            SELECT iv1.patient_insurance_id FROM insurance_verification iv1
            WHERE iv1.prescription_id = rx.id
            ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC,
            iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1)
            LEFT JOIN(
            SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim
            FROM claim_submission cs1
            INNER JOIN claim c1 ON cs1.claim_id = c1.id
            INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id
            GROUP BY rx1.id) as x ON x.id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id) as x
            """;

    //CO-45 REVERSAL
    String category19 = insertWithFields + """
            (SELECT DISTINCT 19 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account,
            'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id,
            primary_claim as claim_id, plc.id as plc_id, plc.l_code_id, ivlc.id as ivlc_id, x.payment_id as payment_id, null as ap_id, null as aplc_id, null as payer_type, null as payment_type,
            x.check_number as check_number, pin.insurance_company_id, iv.carrier_type, pin.id as patient_insurance, ivlc.insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            subPayment_date as payment_date, null as deposit_date, subApplied_date as applied_date, subPayment_date as gl_date, subApplied_date as gl_applied_date,
            (COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00)) as amount, ABS((COALESCE(ivlc.total_charge, 0.00) - COALESCE(ivlc.total_allowable, 0.00))) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM insurance_verification_l_code ivlc
            JOIN insurance_verification iv ON iv.id = ivlc.insurance_verification_id
            JOIN prescription rx ON rx.id = iv.prescription_id
            JOIN patient_insurance pin ON iv.patient_insurance_id = pin.id
            JOIN claim c ON rx.id = c.prescription_id AND pin.id = c.patient_insurance_id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON ivlc.prescription_l_code_id = plc.id
            AND pin.id = (
            SELECT iv1.patient_insurance_id FROM insurance_verification iv1
            WHERE iv1.prescription_id = rx.id
            ORDER BY iv1.carrier_type = 'primary' DESC, iv1.carrier_type = 'secondary' DESC, iv1.carrier_type = 'tertiary' DESC, iv1.carrier_type = 'quaternary'  DESC, iv1.carrier_type = 'quinary'  DESC, iv1.carrier_type = 'senary'  DESC, iv1.carrier_type = 'septenary'  DESC, iv1.carrier_type = 'octonary'  DESC, iv1.carrier_type = 'nonary'  DESC, iv1.carrier_type = 'denary'  DESC,
            iv1.carrier_type = 'other' DESC, iv1.carrier_type = 'inactive' DESC LIMIT 1)
            LEFT JOIN(
            SELECT rx1.id, MIN(p.id) as payment_id, MIN(ap.applied_date) as subApplied_date, MIN(p.date) as subPayment_date, MAX(p.check_number) as check_number
            FROM payment p
            INNER JOIN applied_payment ap ON p.id = ap.payment_id
            INNER JOIN applied_payment_l_code aplc ON ap.id = aplc.applied_payment_id
            INNER JOIN claim c ON ap.claim_id = c.id
            INNER JOIN prescription rx1 ON c.prescription_id = rx1.id
            GROUP BY rx1.id  ) as x ON x.id = rx.id
            LEFT JOIN(
            SELECT rx1.id, MIN(submission_date) as subDate, MIN(c1.id) as primary_claim
            FROM claim_submission cs1
            INNER JOIN claim c1 ON cs1.claim_id = c1.id
            INNER JOIN prescription rx1 ON c1.prescription_id = rx1.id
            GROUP BY rx1.id) as a ON a.id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN applied_payment_l_code aplc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id) as x
            """;

    //**** LINE ADJUSTMENTS ****
    String category20 = insertWithFields + """
            (SELECT DISTINCT 20 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account,
            'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.adjustment_amount1, 0.00) * -1 as amount, ABS(COALESCE(aplc.adjustment_amount1, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment_amount1 <> 0.00 AND aplc.adjustment_amount1 IS NOT NULL
            AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type1,INSTR(aplc.adjustment_type1,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date)
            OR (code = SUBSTRING(aplc.adjustment_type1,INSTR(aplc.adjustment_type1,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0
            AND aplc.adjustment_type1 NOT LIKE 'PR-%' AND p.date IS NOT NULL) as x
            """;

    String category21 = insertWithFields + """
            (SELECT DISTINCT 21 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account,
            'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.adjustment_amount2, 0.00) * -1 as amount, ABS(COALESCE(aplc.adjustment_amount2, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment_amount2 <> 0.00 AND aplc.adjustment_amount2 IS NOT NULL
            AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type2,INSTR(aplc.adjustment_type2,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date)
            OR (code = SUBSTRING(aplc.adjustment_type2,INSTR(aplc.adjustment_type2,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0
            AND aplc.adjustment_type2 NOT LIKE 'PR-%' AND p.date IS NOT NULL) as x
            """;

    String category22 = insertWithFields + """
            (SELECT DISTINCT 22 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account,
            'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.adjustment_amount3, 0.00) * -1 as amount, ABS(COALESCE(aplc.adjustment_amount3, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment_amount3 <> 0.00 AND aplc.adjustment_amount3 IS NOT NULL
            AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type3,INSTR(aplc.adjustment_type3,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date)
            OR (code = SUBSTRING(aplc.adjustment_type3,INSTR(aplc.adjustment_type3,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0
            AND aplc.adjustment_type3 NOT LIKE 'PR-%'  AND p.date IS NOT NULL) as x
            """;

    String category23 = insertWithFields + """
            (SELECT DISTINCT 23 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account,
            'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date,
            p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.adjustment_amount4, 0.00) * -1 as amount, ABS(COALESCE(aplc.adjustment_amount4, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment_amount4 <> 0.00 AND aplc.adjustment_amount4 IS NOT NULL
            AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type4,INSTR(aplc.adjustment_type4,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date)
            OR (code = SUBSTRING(aplc.adjustment_type4,INSTR(aplc.adjustment_type4,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0
            AND aplc.adjustment_type4 NOT LIKE 'PR-%' AND p.date IS NOT NULL) as x
            """;

    String category24 = insertWithFields + """
            (SELECT DISTINCT 24 as category_id, REPLACE((SELECT x.account FROM gl_account x WHERE x.name = 'line_adjustments' and x.type = 'ar_type')  , '&b&', COALESCE(b.code, '000')) as gl_account,
            'Accounts Receivable' as category, 'Line Adjustments' as sub_category, pt.id as patient_id, COALESCE(c.billing_branch_id, rx.branch_id) as billing_branch_id, pt.primary_branch_id as patient_branch_id, rx.branch_id as prescription_branch_id, rx.facility_id, rx.id as prescription_id, c.id as claim_id, plc.id as plc_id, plc.l_code_id,
            null as ivlc_id, p.id as payment_id, ap.id as ap_id, aplc.id as aplc_id, p.payer_type, p.payment_type,
            p.check_number as check_number, p.insurance_company_id, null as carrier_type, pin.id as patient_insurance, null as insurance_verification_id, dt.id as device_type_id, dt.orthotic_or_prosthetic as device_type,
            rx.treating_practitioner_id, rx.primary_care_physician_id, rx.therapist_id, rx.referring_physician_id, null as claim_submission_date, c.date_of_service as date_of_service, rx.prescription_date,
            p.date as payment_date, p.deposit_date as deposit_date, ap.applied_date as applied_date, p.date as gl_date, ap.applied_date as gl_applied_date,
            COALESCE(aplc.adjustment_amount5, 0.00) * -1 as amount, ABS(COALESCE(aplc.adjustment_amount5, 0.00)) as abs_amount,
            rx.active as rx_active, pt.active as pt_active
            FROM applied_payment_l_code aplc
            JOIN applied_payment ap ON aplc.applied_payment_id = ap.id
            JOIN payment p ON ap.payment_id = p.id
            JOIN claim c ON ap.claim_id = c.id
            JOIN prescription rx ON c.prescription_id = rx.id
            JOIN patient pt ON rx.patient_id = pt.id
            JOIN patient_insurance pin ON c.patient_insurance_id = pin.id
            JOIN device_type dt ON rx.device_type_id = dt.id
            JOIN prescription_l_code plc ON aplc.prescription_l_code_id = plc.id
            LEFT JOIN branch b ON c.billing_branch_id = b.id
            WHERE aplc.adjustment_amount5 <> 0.00 AND aplc.adjustment_amount5 IS NOT NULL
            AND (SELECT  COUNT(*) FROM era_adjustment_reason_code WHERE (code = SUBSTRING(aplc.adjustment_type5,INSTR(aplc.adjustment_type5,'-') + 1) AND active = false AND affects_balance = true AND  start <= ap.applied_date  AND end >= ap.applied_date)
            OR (code = SUBSTRING(aplc.adjustment_type5,INSTR(aplc.adjustment_type5,'-') + 1) AND active = true AND affects_balance = true AND  start <= ap.applied_date)) > 0
            AND aplc.adjustment_type5 NOT LIKE 'PR-%' AND p.date IS NOT NULL) as x
            """;

}
