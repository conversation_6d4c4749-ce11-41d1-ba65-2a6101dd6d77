package com.nymbl.tenant.controller;

import com.nymbl.config.controller.AbstractController;
import com.nymbl.config.dto.PatientIntakeFromPatientDTO;
import com.nymbl.config.dto.reports.PatientLastTouchRow;
import com.nymbl.config.dto.reports.ResupplyRow;
import com.nymbl.config.service.TwilioService;
import com.nymbl.config.utils.FileUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.tenant.Auditable;
import com.nymbl.tenant.model.Patient;
import com.nymbl.tenant.model.PatientIntake;
import com.nymbl.tenant.service.PatientIntakeService;
import com.nymbl.tenant.service.PatientMergeService;
import com.nymbl.tenant.service.PatientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.nymbl.config.utils.OptimisticLockingUtil.SAVED;

/**
 * Created by Bradley Moore on 05/20/ø
 */
@Slf4j
@RestController
@RequestMapping("/api/patient")
public class PatientController extends AbstractController<Patient, Long> {

    private final PatientService patientService;
    private final TwilioService twilioService;
    private final PatientIntakeService patientIntakeService;
    private final PatientMergeService patientMergeService;
    private final FileUtil fileUtil;

    @Autowired
    public PatientController(PatientService patientService,
                             TwilioService twilioService,
                             PatientIntakeService patientIntakeService,
                             PatientMergeService patientMergeService,
                             FileUtil fileUtil) {
        super(patientService);
        this.patientService = patientService;
        this.twilioService = twilioService;
        this.patientIntakeService = patientIntakeService;
        this.patientMergeService = patientMergeService;
        this.fileUtil = fileUtil;
    }

    @Auditable(entry = "Save Patient")
    @Override
    public ResponseEntity<?> save(@RequestBody Patient patient, HttpServletRequest request) {

        Map<String, Object> respMap;
        if (patient.getActive() == null) {
            patient.setActive(false);
        }
        if (patientService.canInactivate(patient)) {
            try {
                respMap = patientService.saveForVersion(patient);
                if (null != respMap.get(SAVED)) {
                    return ResponseEntity.ok(respMap.get(SAVED));
                }
                return ResponseEntity.badRequest().body(respMap);
            } catch (ConstraintViolationException constraintViolationException) {
                // send back the attribute errors
                Set<ConstraintViolation<?>> constraintViolations = constraintViolationException.getConstraintViolations();
                Set<String> messages = new HashSet<>(constraintViolations.size());
                messages.addAll(constraintViolations.stream()
                        .map(constraintViolation -> String.format("%s value '%s' %s", constraintViolation.getPropertyPath(),
                                constraintViolation.getInvalidValue(), constraintViolation.getMessage()))
                        .collect(Collectors.toList()));
                return new ResponseEntity<>(messages, HttpStatus.BAD_REQUEST);
            }
        } else {
            String message = "Patient cannot be flagged 'inactive'. Claims submissions exists and/or Payments have been applied for this patient.";
            String response = "{ \"message\" : \"" + message + "\" }";
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
        }
    }

    //    @Auditable(entry = "Search Patient By Keyword, Active, Branch Id")
    @GetMapping(value = "/searchBy", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getPatients(@RequestParam(name = "q", required = false) String keyword,
                                         @RequestParam(name = "active", required = false) Boolean active,
                                         @RequestParam(name = "branchId", required = false) Long branchId,
                                         HttpServletRequest request) {
        List<Patient> results = patientService.getPatients(keyword, active, branchId);
        return ResponseEntity.ok(results);
    }

    //    @Auditable(entry = "Full Text Search Patient")
    @GetMapping(value = "/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> search(@RequestParam(name = "q") String q,
                                    @RequestParam(name = "inactive", required = false) Boolean inactive,
                                    HttpServletRequest request) {
        Set<Patient> results = patientService.search(q, inactive);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/sort", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> sort(@RequestParam(name = "branchId", required = false) Long branchId,
                                  @RequestParam(name = "active", required = false) Boolean active,
                                  @RequestParam(name = "q", required = false) String keyword,
                                  @RequestParam(name = "primaryPractitionerId", required = false) Long primaryPractitionerId,
                                  Pageable pageable,
                                  HttpServletRequest request) {
        Page<Patient> results = patientService.sort(keyword, active, branchId, primaryPractitionerId, pageable);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/validate-unique-patient", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> validateUniquePatient(@RequestParam(name = "firstName") String firstName,
                                                   @RequestParam(name = "lastName") String lastName,
                                                   @RequestParam(name = "dob") @DateTimeFormat(pattern = "yyyy-MM-dd") Date dob,
                                                   HttpServletRequest request) {
        List<Patient> duplicatePatients = patientService.validateUniquePatient(firstName, lastName, dob);
        return ResponseEntity.ok(duplicatePatients);
    }

    @GetMapping(value = "/text-medical-history", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> textMedicalHistoryForm(@RequestParam(name = "patientId") Long patientId, HttpServletRequest request) {
        String result = twilioService.sendMedicalHistoryForm(patientService.findOne(patientId));
        return ResponseEntity.ok(result);
    }

    @PostMapping(value = "/save-intake", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> createIntakeFromPatient(@RequestBody PatientIntakeFromPatientDTO dto, HttpServletRequest request) {
        PatientIntake intake = patientIntakeService.createIntakeFromPatient(dto);
        PatientIntake result = patientIntakeService.save(intake);
        return ResponseEntity.ok(result);
    }

    @PostMapping(value = "/save-patient", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> savePatient(@RequestBody Patient patient, HttpServletRequest request) {

        Map<String, Object> respMap;
        if (patient.getCellPhone() != null && !patient.getCellPhone().trim().equals("")) {
            String errorCode = twilioService.getSmsCellCheckErrorCode(patient.getCellPhone());
            if (errorCode != null) {
                patient.setSmsCompliantCell(false);
                patient.setSmsCellErrorCode(errorCode);
            } else {
                patient.setSmsCompliantCell(true);
            }
        } else {
            patient.setSmsCompliantCell(false);
        }
        try {
            respMap = patientService.saveForVersion(patient);

            if (null != respMap.get(SAVED)) {
                return ResponseEntity.ok(respMap.get(SAVED));
            }
            return ResponseEntity.badRequest().body(respMap);
        } catch (Exception e) {
            String exceptionAsString = StringUtil.getExceptionAsString(e);
            log.error(exceptionAsString);
            return ResponseEntity.badRequest().body(StringUtil.getExceptionAsString(e));
        }
    }

    @PostMapping(value = "/merge/{fromPatientId}/{toPatientId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> mergePatient(@PathVariable Long fromPatientId, @PathVariable Long toPatientId, HttpServletRequest request) {

        try {
            patientMergeService.mergePatient(fromPatientId, toPatientId);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            String exceptionAsString = StringUtil.getExceptionAsString(e);
            log.error(exceptionAsString);
            return ResponseEntity.badRequest().body(StringUtil.getExceptionAsString(e));
        }
    }

    @GetMapping(value = "/photo/{id}")
    public ResponseEntity<byte[]> getUserPhoto(@PathVariable Long id,
                                               HttpServletRequest request) {
        try {
            String path = MessageFormat.format(fileUtil.getPatientProfilePhotosDirectory(), request.getHeader("x-tenant-id"));
            byte[] contents = Files.readAllBytes(Paths.get(path.concat("/" + id + ".png")));
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.parseMediaType(MediaType.IMAGE_PNG_VALUE))
                    .body(Base64.getEncoder().encode(contents));
        } catch (IOException e) {
            return ResponseEntity.noContent().build();
        }
    }
    @GetMapping(value = "/patient-last-touch", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getPatientLastTouchReport(@RequestParam(name = "branchId", required = false) Long branchId) {
        List<PatientLastTouchRow> results = patientService.getPatientLastTouchReport(branchId);
        return ResponseEntity.ok(results);
    }

    @GetMapping(value = "/resupply-report", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getResupplyQuery(@RequestParam(name = "branchId", required = false) Long branchId,
                                              @RequestParam(name = "daysSinceDos", required = false) Long daysSinceDos,
                                              HttpServletRequest request) {
        List<ResupplyRow> results = patientService.getResupplyQuery(branchId, daysSinceDos);
        return ResponseEntity.ok(results);
    }
}
