package com.nymbl.tenant.controller;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.common.base.Strings;
import com.nymbl.config.controller.AbstractController;
import com.nymbl.config.utils.JsonDateDeserializer;
import com.nymbl.config.utils.JsonDateSerializer;
import com.nymbl.tenant.model.ItemPhysical;
import com.nymbl.tenant.model.L_Code;
import com.nymbl.tenant.model.Prescription_L_Code;
import com.nymbl.tenant.model.PurchaseOrder_Item;
import com.nymbl.tenant.repository.PurchaseOrder_ItemRepository;
import com.nymbl.tenant.service.ItemPhysicalService;
import com.nymbl.tenant.service.Prescription_L_CodeService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.sql.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.nymbl.config.utils.OptimisticLockingUtil.SAVED;

@RestController
@RequestMapping("/api/item-physical")
public class ItemPhysicalController extends AbstractController<ItemPhysical, Long> {

    private final ItemPhysicalService itemPhysicalService;
    private final Prescription_L_CodeService prescription_L_CodeService;
    private final PurchaseOrder_ItemRepository purchaseOrder_ItemRepository;

    @Autowired
    public ItemPhysicalController(ItemPhysicalService itemPhysicalService,
                                  Prescription_L_CodeService prescription_L_CodeService,
                                  PurchaseOrder_ItemRepository purchaseOrder_ItemRepository) {
        super(itemPhysicalService);
        this.itemPhysicalService = itemPhysicalService;
        this.purchaseOrder_ItemRepository = purchaseOrder_ItemRepository;
        this.prescription_L_CodeService = prescription_L_CodeService;
    }

    @GetMapping(value = "/count-by-itemid", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> countByItemId(@RequestParam List<Long> itemIds) {
        if (itemIds == null || itemIds.size() == 0) {
            return ResponseEntity.ok().body(new HashMap<Long, Integer>());
        }
        try {
            List<Long[]> result = itemPhysicalService.countByItemId(itemIds);
            return ResponseEntity.ok().body(result);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    @PostMapping(value = "/create-items", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> createItems(@RequestBody ItemPhysical itemPhysical, @RequestParam int quantity) {
        int itemCount;
        String error = "null";
        if (quantity <= 0) {
            itemCount = -1;
            error = "\"Create quantitiy undefined\"";
        } else {
            try {
                itemCount = itemPhysicalService.createItems(itemPhysical, quantity);
            } catch (Exception ex) {
                itemCount = 0;
                error = "\"" + ex.getMessage() + "\"";
            }
        }
        String responseBody = String.format("{\"itemCount\":%d,\"error\":%s}", itemCount > 0 ? itemCount : 0, error);
        HttpStatus responseStatus = itemCount > 0 ? HttpStatus.OK :
                (itemCount == 0 ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.BAD_REQUEST);
        return ResponseEntity.status(responseStatus).body(responseBody);
    }

    @GetMapping(value = "/delete-items", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> deleteItems(@RequestParam Long branchId,
                                         @RequestParam Long itemId,
                                         @RequestParam Long purchaseOrderItemId,
                                         @RequestParam int quantity,
                                         @RequestParam String reason) {
        int itemCount;
        String error = "null";
        if (quantity <= 0) {
            itemCount = -1;
            error = "\"Delete quantitiy undefined\"";
        } else {
            try {
                itemCount = itemPhysicalService.deleteItems(branchId, itemId, purchaseOrderItemId, quantity, reason);
            } catch (Exception ex) {
                itemCount = 0;
                error = "\"" + ex.getMessage() + "\"";
            }
        }
        String responseBody = String.format("{\"itemCount\":%d,\"error\":%s}", itemCount > 0 ? itemCount : 0, error);
        HttpStatus responseStatus = itemCount > 0 ? HttpStatus.OK :
                (itemCount == 0 ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.BAD_REQUEST);
        return ResponseEntity.status(responseStatus).body(responseBody);
    }

    @GetMapping(value = "/by-branch-and-item", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByBranchIdAndItemId(@RequestParam Long branchId,
                                                     @RequestParam Long itemId,
                                                     @RequestParam (name = "quantity", required = false) Integer quantity,
                                                     @RequestParam (name = "status", required = false) String status) {
        if (quantity == null || quantity <= 0) {
            quantity = 1000;
        }
        if (Strings.isNullOrEmpty(status)) {
            status = null;
        }
        try {
            List<ItemPhysical> result = itemPhysicalService.findByBranchIdAndItemId(branchId, itemId, quantity, status);
            return ResponseEntity.ok().body(result);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    @GetMapping(value = "/by-prescription", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByPrescription(@RequestParam Long prescriptionId,
                                                @RequestParam(name = "prescriptionLCodeId", required = false) Long prescriptionLCodeId,
                                                @RequestParam(name = "stripDown", required = false) Boolean stripDown) {
        if (prescriptionId == null || prescriptionId <= 0) {
            return ResponseEntity.badRequest().body("{\"error\":\"prescriptionId is required\"}");
        }
        try {
            List<ItemPhysical> result = itemPhysicalService.findByPrescription(prescriptionId, prescriptionLCodeId);
            if (stripDown != null && stripDown == true) {
                result = result.stream().map(ItemPhysical::shallowCopy).toList();
            }
            return ResponseEntity.ok().body(result);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    @GetMapping(value = "/by-purchase-order", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findByPurchaseOrderId(@RequestParam Long purchaseOrderId) {
        List<ItemPhysical> result;
        if (purchaseOrderId == null || purchaseOrderId <= 0) {
            result = new ArrayList<>();
        } else {
            try {
                result = itemPhysicalService.findByPurchaseOrderId(purchaseOrderId);
            } catch (Exception ex) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
            }
        }
        return ResponseEntity.ok().body(result);
    }

    @GetMapping(value = "/by-patientid", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findRentalsByPatientId(@RequestParam Long patientId) {
        if (patientId == null || patientId <= 0) {
            return ResponseEntity.badRequest().body("{\"error\":\"prescriptionId is required\"}");
        }
        try {
            List<ItemPhysical> result = itemPhysicalService.findRentalsByPatientId(patientId);
            return ResponseEntity.ok().body(result);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    @GetMapping(value = "/by-patientid-from-trans", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> findRentalsByPatientIdFromTrans(@RequestParam Long patientId) {
        if (patientId == null || patientId <= 0) {
            return ResponseEntity.badRequest().body("{\"error\":\"patientId is required\"}");
        }
        try {
            List<ItemPhysical> result = itemPhysicalService.findRentalsByPatientIdFromTrans(patientId);
            return ResponseEntity.ok().body(result);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    @GetMapping(value = "/inventory-po-counts", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getInventoryPoItemCountsByPoiId(@RequestParam Long purchaseOrderId) {
        try {
            Map<Long, Long> results = itemPhysicalService.getInventoryPoItemCountsByPoiId(purchaseOrderId);
            return ResponseEntity.ok(results);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    @GetMapping(value = "/get-items", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getItems(
            @RequestParam(name = "itemId") Long itemId,
            @RequestParam(name = "allStatuses") Boolean allStatuses,
            @RequestParam(name = "blankSerial") Boolean blankSerial) {
        try {
            List<ItemPhysical> results = itemPhysicalService.getItems(itemId, allStatuses, blankSerial);
            return ResponseEntity.ok(results);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    @Override
    public ResponseEntity<?> save(@RequestBody ItemPhysical itemPhysical, HttpServletRequest request) {
        String error = null;
        if (itemPhysical == null) {
            error = "ItemPhysical must be not NULL";
        }
        Boolean readOnly = itemPhysical.getReadOnly();
        if (readOnly != null && readOnly == true) {
            error = "This ItemPhysical is read only (reconstructed from transaction history)";
        }
        if (!Strings.isNullOrEmpty(error)) {
            return ResponseEntity.badRequest().body(new RuntimeException(error));
        }

        Long ipId = itemPhysical.getId();
        ItemPhysical original = ipId != null && ipId.compareTo(0L) > 0
                ? itemPhysicalService.findOne(itemPhysical.getId())
                : null;
        // Fill in serial number in PurchaseOrder_Item if it is blank
        String snNew = itemPhysical.getSerialNumber();
        String snOld = original == null ? "" : original.getSerialNumber();
        if (Strings.isNullOrEmpty(snOld) && !Strings.isNullOrEmpty(snNew)) {
            Long poiId = itemPhysical.getPurchaseOrderItemId();
            PurchaseOrder_Item poi = poiId != null && poiId.compareTo(0L) > 0
                    ? purchaseOrder_ItemRepository.getOne(poiId)
                    : null;
            if (poi != null && Strings.isNullOrEmpty(poi.getSerialNumber())) {
                poi.setSerialNumber(snNew);
                try {
                    purchaseOrder_ItemRepository.save(poi);
                } catch (Exception ex) {
                    error = ex.getMessage();
                }
            }
        }
        // May need to update rentalStatus and start/end dates in PLC
        Long plcId = itemPhysical.getPrescriptionLCodeId();
        if (plcId != null && plcId.compareTo(0L) > 0) {
            String newStatus = itemPhysical.getStatus();
            if (newStatus == null) newStatus = "";
            String oldStatus = original == null ? "" : original.getStatus();
            if (oldStatus == null) oldStatus = "";
            Date newEndDate = itemPhysical.getDateOfServiceEnd();
            Date newStartDate = itemPhysical.getDateOfServiceStart();
            Date oldEndDate = original == null ? null : original.getDateOfServiceEnd();
            Date oldStartDate = original == null ? null : original.getDateOfServiceStart();
            List<String> rentalStatuses = List.of("loaned", "rented");
            List<String> returnStatuses = List.of("cleaning", "repair");
            // This concerns only rental items
            if ((rentalStatuses.contains(newStatus) || rentalStatuses.contains(oldStatus)
                    || returnStatuses.contains(newStatus) || rentalStatuses.contains(oldStatus)
            )
                    // and something about their rental state have changed
                    && (!newStatus.equals(oldStatus)
                    || (newEndDate == null && oldEndDate != null)
                    || (newEndDate != null && (oldEndDate == null || !newEndDate.equals(oldEndDate)))
                    || (newStartDate == null && oldStartDate != null)
                    || (newStartDate != null && (oldStartDate == null || !newStartDate.equals(oldStartDate))))
            ) {
                Prescription_L_Code plc = prescription_L_CodeService.findOne(plcId);
                if (plc != null) {
                    List<String> modifiers = List.of(Strings.nullToEmpty(plc.getModifier1()),
                            Strings.nullToEmpty(plc.getModifier2()),
                            Strings.nullToEmpty(plc.getModifier3()),
                            Strings.nullToEmpty(plc.getModifier4()));
                    if (modifiers.contains("LL") || modifiers.contains("RR")) {
                        Date plcEndDate = plc.getDateOfServiceEnd();
                        Date plcStartDate = plc.getDateOfService();
                        String plcStatus = plc.getRentalStatus();
                        if (plcStatus == null) plcStatus = "";
                        boolean isChanged = false;
                        if ((newEndDate == null && plcEndDate != null)
                                || (newEndDate != null && (plcEndDate == null || !newEndDate.equals(plcEndDate)))) {
                            isChanged = true;
                            plc.setDateOfServiceEnd(newEndDate);
                        }
                        if ((newStartDate == null && plcStartDate != null)
                                || (newStartDate != null && (plcStartDate == null || !newStartDate.equals(plcStartDate)))) {
                            isChanged = true;
                            plc.setDateOfService(newStartDate);
                        }
                        if (rentalStatuses.contains(newStatus) && !"OUT".equals(plcStatus)) {
                            isChanged = true;
                            plc.setRentalStatus("OUT");
                        } else if (returnStatuses.contains(newStatus) && !"IN".equals(plcStatus)) {
                            isChanged = true;
                            plc.setRentalStatus("IN");
                        }
                        if (isChanged) {
                            try {
                                prescription_L_CodeService.save(plc);
                            } catch (Exception ex) {
                                error = ex.getMessage();
                            }
                        }
                    } else {
                        L_Code lCode = plc.getLCode();
                        String plcIdentifier = lCode != null ? lCode.getName() : ("#" + plcId);
                        error = "related Prescription Code " + plcIdentifier + " was not updated because it lacks a rental modifier";
                    }
                }
            }
        }


        Map<String, Object> respMap;
        try {
            respMap = itemPhysicalService.saveForVersion(itemPhysical);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
        Object savedObject = respMap.get(SAVED);
        if (savedObject != null) {
            return ResponseEntity.ok(savedObject);
        } else {
            return ResponseEntity.badRequest().body(respMap);
        }
    }

    @PostMapping(value = "/save-all", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> saveAll(@RequestBody List<ItemPhysical> physicalItems) {
        try {
            List<ItemPhysical> savedItems = itemPhysicalService.saveAll(physicalItems);
            return ResponseEntity.ok(savedItems);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    @PostMapping(value = "/search-rental", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> searchRental(@RequestBody Filter filter, Pageable pageable) {
        Page<ItemPhysical> items;
        try {
            items = itemPhysicalService.searchRental(filter.branchId, filter.endDate, filter.itemId, pageable,
                    filter.patientId, null, filter.serializedOnly, filter.serialNumber, filter.startDate,
                    filter.status, filter.useChildBranches);
            return ResponseEntity.ok().body(items);
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex);
        }
    }

    @Getter
    @Setter
    public static class Filter {
        private Long branchId;
        @JsonSerialize(using = JsonDateSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
        private Date endDate;
        private Long itemId;
        private Long patientId;
        private Boolean serializedOnly;
        private String serialNumber;
        @JsonSerialize(using = JsonDateSerializer.class)
        @JsonDeserialize(using = JsonDateDeserializer.class)
        private Date startDate;
        private String status;
        private Boolean useChildBranches;
    }
}
