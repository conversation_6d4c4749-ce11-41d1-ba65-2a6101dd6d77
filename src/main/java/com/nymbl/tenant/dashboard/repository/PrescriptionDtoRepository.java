package com.nymbl.tenant.dashboard.repository;

import com.nymbl.tenant.dashboard.dto.PrescriptionDto;
import com.nymbl.tenant.dashboard.dto.PrescriptionInsuranceVerifDto;
import com.nymbl.tenant.model.Prescription;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.sql.Date;
import java.util.List;

public interface PrescriptionDtoRepository extends JpaRepository<Prescription, Long>, JpaSpecificationExecutor<Prescription>  {

    @Query(value = PRESCRIPTION_DTO_DASHBOARD_QUERY, nativeQuery = true)
    List<PrescriptionDto> findPrescriptionForDto(Long userId, Date startDate, Date endDate, Long range);

    @Query(value = PRESCRIPTION_DTO_BY_ID_DASHBOARD_QUERY, nativeQuery = true)
    PrescriptionDto findPrescriptionById(Long id);

    @Query(value = PRESCRIPTION_INSURANCE_VERIF_QUERY, nativeQuery = true)
    List<PrescriptionInsuranceVerifDto> findPrescriptionForEstimatedMonthlyDto(Date startDate, Date endDate, List<Long> branchIds);

    @Query(value = PRESCRIPTION_DEVICE_BY_PROJ_DEL_DATE_AND_BRANCH_ID, nativeQuery = true)
    List<PrescriptionDto> findPrescriptionByProjectedDeliveryDateDto(Date startDate, Date endDate, Long branchId);

    @Query(value = PRESCRIPTION_DTO_BY_PATIENT_BRANCH, nativeQuery = true)
    List<PrescriptionDto> findPrescriptionByPatientBranchAndSubmissionDate(Date startDate, Date endDate, Long branchId);

    @Query(value = PRESCRIPTION_DTO_BY_PRESCRIPTION_BRANCH, nativeQuery = true)
    List<PrescriptionDto> findPrescriptionByPrescriptionBranchAndSubmissionDate(Date startDate, Date endDate, Long branchId);

    @Query(value = PRESCRIPTION_DTO_BY_BILLING_BRANCH_BRANCH, nativeQuery = true)
    List<PrescriptionDto> findPrescriptionByBillingBranchAndSubmissionDate(Date startDate, Date endDate, Long branchId);

    @Query(value = PRESCRIPTION_DTO_GL_IVLC_BY_PATIENT_BRANCH, nativeQuery = true)
    List<PrescriptionDto> findPrescriptionGLByPatientBranchAndSubmissionDate(Date startDate, Date endDate, Long branchId);

    @Query(value = PRESCRIPTION_DTO_GL_IVLC_BY_PRESCRIPTION_BRANCH, nativeQuery = true)
    List<PrescriptionDto> findPrescriptionGLByPrescriptionBranchAndSubmissionDate(Date startDate, Date endDate, Long branchId);

    @Query(value = PRESCRIPTION_DTO_GL_IVLC_BY_BILLING_BRANCH, nativeQuery = true)
    List<PrescriptionDto> findPrescriptionGLByBillingBranchAndSubmissionDate(Date startDate, Date endDate, Long branchId);

    @Query(value = PRESCRIPTION_DEVICE_BY_BILLED_AND_BRANCH_ID, nativeQuery = true)
    List<PrescriptionDto> findPrescriptionByBilledDto(Date startDate, Date endDate, Long branchId);

    String PRESCRIPTION_DTO_DASHBOARD_QUERY =
            "SELECT p.id, d.name as deviceType, p.created_at as createdAt, p.prescription_date as prescriptionDate, ps.section,  " +
                    "       p.patient_id as patientId, pa.first_name as firstName, pa.last_name as lastName " +
                    "FROM prescription p    " +
                    "    JOIN device_type d ON p.device_type_id = d.id   " +
                    "    JOIN prescription_section ps ON p.id = ps.prescription_id   " +
            "    JOIN patient pa ON p.patient_id = pa.id  " +
            "WHERE p.active = 1    " +
            "   AND p.clerical_user_id = :userId   " +
            "   AND ps.locked = 0   " +
            "   AND CASE   " +
            "   WHEN :range = 90   " +
            "   THEN p.created_at <= :endDate   " +
            "   ELSE p.created_at BETWEEN :startDate AND :endDate   " +
            "   END   " +
            "GROUP BY p.id, ps.section ORDER BY p.created_at ASC";


    String PRESCRIPTION_INSURANCE_VERIF_QUERY =
            "SELECT DISTINCT rx.id, iv.id as insuranceVerificationId, " +
                    "   rx.treating_practitioner_id as practitionerId, " +
                    "   u.first_name as firstName, u.last_name as lastName, u.middle_name as middleName, u.credentials " +
                    "FROM prescription rx  " +
                    "   INNER JOIN patient pt ON pt.id = rx.patient_id " +
                    "   JOIN insurance_verification iv ON iv.prescription_id = rx.id  " +
                    "   JOIN nymbl_master.user u ON u.id = rx.treating_practitioner_id " +
                    "   LEFT JOIN claim c on rx.id = c.prescription_id " +
                    "   LEFT JOIN claim_submission sub on c.id = sub.claim_id " +
                    "WHERE ((rx.projected_delivery_date BETWEEN :startDate AND :endDate) OR (sub.submission_date BETWEEN :startDate AND :endDate)) " +
                    "   AND rx.treating_practitioner_id IS NOT NULL " +
            "   AND iv.carrier_type = 'primary' " +
            "   AND pt.primary_branch_id IN (:branchIds)  " +
            "   AND rx.active = 1 ";

    String PRESCRIPTION_DEVICE_BY_PROJ_DEL_DATE_AND_BRANCH_ID =
            "SELECT p.id, d.name as deviceType, d.orthotic_or_prosthetic as orthoticOrProsthetic, p.created_at as createdAt, " +
                    "p.prescription_date as prescriptionDate, p.patient_id as patientId, pa.first_name as firstName, pa.last_name as lastName  " +
                    "FROM prescription p  " +
                    "   JOIN device_type d ON p.device_type_id = d.id    " +
                    "   JOIN patient pa ON p.patient_id = pa.id " +
                    "   LEFT JOIN claim c on p.id = c.prescription_id " +
                    "   LEFT JOIN (SELECT claim_id, MIN(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) sub on c.id = sub.claim_id " +
                    "WHERE ((c.date_of_service BETWEEN :startDate AND :endDate) OR (p.projected_delivery_date BETWEEN :startDate AND :endDate) OR (sub.submission_date BETWEEN :startDate AND :endDate))" +
                    "   AND p.active = 1 " +
                    "   AND pa.primary_branch_id = :branchId ";

    String PRESCRIPTION_DEVICE_BY_BILLED_AND_BRANCH_ID =
            "SELECT gl.prescription_id as id, SUM(gl.amount) as totalAllowable, p.projected_delivery_date as projectedDeliveryDate, d.name as deviceType, d.orthotic_or_prosthetic as orthoticOrProsthetic, pa.id as patientId\n" +
                    "FROM general_ledger gl\n" +
                    "JOIN prescription p on gl.prescription_id = p.id\n" +
                    "JOIN device_type d on p.device_type_id = d.id\n" +
                    "JOIN patient pa ON p.patient_id = pa.id\n" +
                    "WHERE gl.category in ('Charge', 'Line Adjustments') AND gl.sub_category IN ('Billable', 'CO-45 Estimate', 'CO-45 Reversal', 'CO-45') \n" +
                    "AND p.active = 1\n" +
                    "AND gl.claim_submission_date BETWEEN :startDate AND :endDate\n" +
                    "AND pa.primary_branch_id = :branchId\n" +
                    "GROUP BY gl.prescription_id, p.projected_delivery_date\t";

    String PRESCRIPTION_DTO_BY_ID_DASHBOARD_QUERY =
            "SELECT p.id, d.name as deviceType, d.orthotic_or_prosthetic as orthoticOrProsthetic, p.created_at as createdAt, " +
                    "p.prescription_date as prescriptionDate, p.patient_id as patientId, pa.first_name as firstName, pa.last_name as lastName  " +
                    "FROM prescription p  " +
                    "   JOIN device_type d ON p.device_type_id = d.id    " +
                    "   JOIN patient pa ON p.patient_id = pa.id " +
                    "WHERE p.id = :id";


    String PRESCRIPTION_DTO_BY_PATIENT_BRANCH = "SELECT p.id, d.name as deviceType, d.orthotic_or_prosthetic as orthoticOrProsthetic, p.created_at as createdAt, " +
            "    p.prescription_date as prescriptionDate, p.patient_id as patientId, pa.first_name as firstName, pa.last_name as lastName  " +
            "   FROM prescription p  " +
            "   JOIN device_type d ON p.device_type_id = d.id    " +
            "   JOIN patient pa ON p.patient_id = pa.id " +
            "   LEFT JOIN claim c on p.id = c.prescription_id " +
            "   LEFT JOIN (SELECT claim_id, MIN(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) sub on c.id = sub.claim_id " +
            "   WHERE ((c.date_of_service BETWEEN :startDate AND :endDate) OR (sub.submission_date BETWEEN :startDate AND :endDate) OR (p.projected_delivery_date BETWEEN :startDate AND :endDate))" +
            "   AND p.active = 1 " +
            "   AND pa.primary_branch_id = :branchId ";

    String PRESCRIPTION_DTO_BY_PRESCRIPTION_BRANCH = "SELECT p.id, d.name as deviceType, d.orthotic_or_prosthetic as orthoticOrProsthetic, p.created_at as createdAt, " +
            "    p.prescription_date as prescriptionDate, p.patient_id as patientId, pa.first_name as firstName, pa.last_name as lastName  " +
            "   FROM prescription p  " +
            "   JOIN device_type d ON p.device_type_id = d.id    " +
            "   JOIN patient pa ON p.patient_id = pa.id " +
            "   LEFT JOIN claim c on p.id = c.prescription_id " +
            "   LEFT JOIN (SELECT claim_id, MIN(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) sub on c.id = sub.claim_id " +
            "   WHERE ((c.date_of_service BETWEEN :startDate AND :endDate) OR (sub.submission_date BETWEEN :startDate AND :endDate) OR (p.projected_delivery_date BETWEEN :startDate AND :endDate))" +
            "   AND p.active = 1 " +
            "   AND p.branch_id = :branchId ";

    String PRESCRIPTION_DTO_BY_BILLING_BRANCH_BRANCH = "SELECT p.id, d.name as deviceType, d.orthotic_or_prosthetic as orthoticOrProsthetic, p.created_at as createdAt, " +
            "       p.prescription_date as prescriptionDate, p.patient_id as patientId, pa.first_name as firstName, pa.last_name as lastName  " +
            "   FROM prescription p  " +
            "   JOIN device_type d ON p.device_type_id = d.id    " +
            "   JOIN patient pa ON p.patient_id = pa.id " +
            "   JOIN claim c on p.id = c.prescription_id " +
            "   LEFT JOIN (SELECT claim_id, MIN(submission_date) AS submission_date FROM claim_submission GROUP BY claim_id) sub on c.id = sub.claim_id " +
            "   WHERE ((c.date_of_service BETWEEN :startDate AND :endDate) OR (sub.submission_date BETWEEN :startDate AND :endDate) OR (p.projected_delivery_date BETWEEN :startDate AND :endDate))" +
            "   AND p.active = 1 " +
            "   AND c.billing_branch_id  = :branchId ";

    String PRESCRIPTION_DTO_GL_IVLC_BY_PATIENT_BRANCH = "SELECT gl.prescription_id as id, SUM(gl.amount) as totalAllowable, p.projected_delivery_date as projectedDeliveryDate, " +
            "       d.name as deviceType, d.orthotic_or_prosthetic as orthoticOrProsthetic, pa.id as patientId\n" +
            "   FROM general_ledger gl\n" +
            "   JOIN prescription p on gl.prescription_id = p.id\n" +
            "   JOIN device_type d on p.device_type_id = d.id\n" +
            "   JOIN patient pa ON p.patient_id = pa.id\n" +
            "   WHERE gl.category in ('Charge', 'Line Adjustments') AND gl.sub_category IN ('Billable', 'CO-45 Estimate', 'CO-45 Reversal', 'CO-45') \n" +
            "   AND p.active = 1\n" +
            "   AND gl.claim_submission_date BETWEEN :startDate AND :endDate\n" +
            "   AND pa.primary_branch_id = :branchId\n" +
            "   GROUP BY gl.prescription_id, p.projected_delivery_date;";

    String PRESCRIPTION_DTO_GL_IVLC_BY_PRESCRIPTION_BRANCH = "SELECT gl.prescription_id as id, SUM(gl.amount) as totalAllowable, p.projected_delivery_date as projectedDeliveryDate, " +
            "       d.name as deviceType, d.orthotic_or_prosthetic as orthoticOrProsthetic, pa.id as patientId\n" +
            "   FROM general_ledger gl\n" +
            "   JOIN prescription p on gl.prescription_id = p.id\n" +
            "   JOIN device_type d on p.device_type_id = d.id\n" +
            "   JOIN patient pa ON p.patient_id = pa.id\n" +
            "   WHERE gl.category in ('Charge', 'Line Adjustments') AND gl.sub_category IN ('Billable', 'CO-45 Estimate', 'CO-45 Reversal', 'CO-45') \n" +
            "   AND p.active = 1\n" +
            "   AND gl.claim_submission_date BETWEEN :startDate AND :endDate\n" +
            "   AND p.branch_id = :branchId\n" +
            "   GROUP BY gl.prescription_id, p.projected_delivery_date;";

    String PRESCRIPTION_DTO_GL_IVLC_BY_BILLING_BRANCH = "SELECT gl.prescription_id as id, SUM(gl.amount) as totalAllowable, p.projected_delivery_date as projectedDeliveryDate, " +
            "       d.name as deviceType, d.orthotic_or_prosthetic as orthoticOrProsthetic, pa.id as patientId\n" +
            "   FROM general_ledger gl\n" +
            "   JOIN prescription p on gl.prescription_id = p.id\n" +
            "   JOIN device_type d on p.device_type_id = d.id\n" +
            "   JOIN patient pa ON p.patient_id = pa.id\n" +
            "   JOIN claim c ON c.prescription_id = p.id\n" +
            "   WHERE gl.category in ('Charge', 'Line Adjustments') AND gl.sub_category IN ('Billable', 'CO-45 Estimate', 'CO-45 Reversal', 'CO-45') \n" +
            "   AND p.active = 1\n" +
            "   AND gl.claim_submission_date BETWEEN :startDate AND :endDate\n" +
            "   AND c.billing_branch_id = :branchId\n" +
            "   GROUP BY gl.prescription_id, p.projected_delivery_date;";
}
