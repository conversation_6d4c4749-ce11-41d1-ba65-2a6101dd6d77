package com.nymbl.tenant.dashboard.dto;

public interface MissingAppointmentNotesDto extends AppointmentDto {
    Long getAttendingUserId();
    Long getUserTwoId();
    Long getUserThreeId();
    Long getSupervisingUserId();
    Long getPrescriptionId();
    Long getPrescriptionTwoId();
    String getBranch();

    String getStatus();

    /**
     * AI Note transcription relationship.
     */
    Long getTranscriptionDetailId();
}
