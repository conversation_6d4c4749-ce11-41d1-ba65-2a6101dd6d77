package com.nymbl.tenant.dashboard.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.dashboard.comparators.PatientSummaryDashboardDtoComparator;
import com.nymbl.tenant.dashboard.dto.PatientDto;
import com.nymbl.tenant.dashboard.dto.PatientSummaryDashboardDto;
import com.nymbl.tenant.dashboard.dto.PrescriptionDto;
import com.nymbl.tenant.dashboard.repository.InsuranceVerificationDtoRepository;
import com.nymbl.tenant.dashboard.repository.PrescriptionDtoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatientSummaryDashboardService {

    final PrescriptionDtoRepository prescriptionDtoRepository;
    final InsuranceVerificationDtoRepository insuranceVerificationDtoRepository;

    @Autowired
    public PatientSummaryDashboardService(PrescriptionDtoRepository prescriptionDtoRepository, InsuranceVerificationDtoRepository insuranceVerificationDtoRepository) {
        this.prescriptionDtoRepository = prescriptionDtoRepository;
        this.insuranceVerificationDtoRepository = insuranceVerificationDtoRepository;
    }

    /**
     * Query for Patient summary dashboard Values for UI
     *
     * @param userId
     * @param range
     * @return
     */
    public List<PatientSummaryDashboardDto> getPatientSummaryDashboard(Long userId, Long range) {
//        java.util.Date juDate = new java.util.Date();
//        DateTime today = new DateTime(juDate);
        ZonedDateTime today = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of(TenantContext.getUserTimezoneId()));
//        Date startDate = range.equals(90L) ? null : new Date(today.minusDays(Math.toIntExact(range) + 30).toDate().getTime());


        Date startDate = range.equals(90L) ? null : Date.valueOf(today.minusDays(Math.toIntExact(range) + 30).toLocalDate());
        Date endDate = range.equals(0L) ? Date.valueOf(today.plusDays(1).toLocalDate()) : Date.valueOf(today.minusDays(Math.toIntExact(range)).toLocalDate());

        List<PrescriptionDto> prescriptionDtoList;

        prescriptionDtoList = prescriptionDtoRepository.findPrescriptionForDto(userId, startDate, endDate, range);
        Map<Long, List<PrescriptionDto>> sectionMap = prescriptionDtoList.stream().collect(Collectors.groupingBy(PrescriptionDto::getId));
        List<PatientSummaryDashboardDto> patientSummaryDashboardDtoList = sectionMap.values().stream().map(this::makePatientSummaryDashboardDto).toList();

        return patientSummaryDashboardDtoList.stream().sorted(new PatientSummaryDashboardDtoComparator()).collect(Collectors.toList());
    }

    /**
     * Generate dto for UI
     *
     * @param dtos
     * @return
     */
    private PatientSummaryDashboardDto makePatientSummaryDashboardDto(List<PrescriptionDto> dtos) {
        PatientSummaryDashboardDto patientSummaryDashboardDto = new PatientSummaryDashboardDto();

        PrescriptionDto prescriptionDto = dtos.get(0);
        patientSummaryDashboardDto.setPrescription(prescriptionDto);
        patientSummaryDashboardDto.setPrescriptionSections(populateSections(dtos));
        patientSummaryDashboardDto.setPatient(new PatientDto(prescriptionDto.getPatientId(), prescriptionDto.getFirstName(), prescriptionDto.getLastName(), null));

        BigDecimal totalAllowable = insuranceVerificationDtoRepository.calculateSumTotalAllowableByPrescriptionId(prescriptionDto.getId());
        patientSummaryDashboardDto.setEstimatedValue(totalAllowable != null && totalAllowable.compareTo(BigDecimal.ZERO) > 0 ? totalAllowable : BigDecimal.ZERO);

        return patientSummaryDashboardDto;
    }

    /**
     *
     * Populate prescription sections values
     * @param dtos
     * @return
     */
    @SuppressWarnings("unchecked")
    private List<String> populateSections(List<PrescriptionDto> dtos) {

        ObjectMapper mapper = new ObjectMapper();
        try
        {
            List<LinkedHashMap<String, String>> sectionMap = mapper.readValue(ResourceUtils.getURL("classpath:static/scripts/jsons/sections.json"), List.class);
            return dtos.stream().map(PrescriptionDto::getSection).map(x -> mapSections(x, sectionMap)).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Exception occurred while getting section map");
        }
        return new ArrayList<>();
    }

    /**
     *
     * Map section to key
     *
     * @param key
     * @param sectionMap
     * @return
     */
    private String mapSections(String key, List<LinkedHashMap<String, String>> sectionMap) {

        for (LinkedHashMap<String, String> entry : sectionMap) {
            if (entry.get("key").equals(key)) {
                return entry.get("name");
            }
        }
        return "";
    }

}
