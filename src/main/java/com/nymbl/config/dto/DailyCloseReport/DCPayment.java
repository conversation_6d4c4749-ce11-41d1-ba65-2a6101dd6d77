package com.nymbl.config.dto.DailyCloseReport;

import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by <PERSON> on 5/21/25.
 */

public interface DCPayment {

   Long getPaymentId();
   void setPaymentId(Long paymentId);

   Long getBranchId();
   void setBranchId(Long branchId);

   String getBranchName();
   void setBranchName(String branchName);

   Long getPatientId();
   void setPatientId(Long patientId);

   Date getPaymentDate();
   void setPaymentDate(Date paymentDate);

   Date getDepositDate();
   void setDepositDate(Date depositDate);

   String getPatientName();
   void setPatientName(String patientName);

   String getPayerType();
   void setPayerType(String payerType);

   String getPaymentType();
   void setPaymentType(String paymentType);

   String getDescription();
   void setDescription(String description);

   String getCheckNumber();
   void setCheckNumber(String checkNumber);

   BigDecimal getPaymentApplied();
   void setPaymentApplied(BigDecimal paymentApplied);

   BigDecimal getPaymentUnapplied();
   void setPaymentUnapplied(BigDecimal paymentUnapplied);

   BigDecimal getPaymentAmount();
   void setPaymentAmount(BigDecimal paymentAmount);

   BigDecimal getAdjustmentAmount();
   void setAdjustmentAmount(BigDecimal adjustmentAmount);

   BigDecimal getAdjustmentApplied();
   void setAdjustmentApplied(BigDecimal adjustmentApplied);

   BigDecimal getAdjustmentUnapplied();
   void setAdjustmentUnapplied(BigDecimal adjustmentUnapplied);

   Long getAdjustmentTypeId();
   void setAdjustmentTypeId(Long adjustmentTypeId);

   Boolean getWithdraw();
   void setWithdraw(Boolean withdraw);

   String getPaymentTypeOrigin();
   void setPaymentTypeOrigin(String paymentTypeOrigin);

   String getPayerName();
   void setPayerName(String payerName);

   Long getCreatedById();
   void setCreatedById(Long createdById);

   @Value("#{(T(com.nymbl.config.utils.StringUtil).isBlank(target.createdByFirstName) ? '' :  target.createdByFirstName + ' ' + target.createdByLastName)}")
   String getCreatedByName();
   void setCreatedByName(String createdByName);

   Date getAppliedDate();
   void setAppliedDate(Date appliedDate);

}
