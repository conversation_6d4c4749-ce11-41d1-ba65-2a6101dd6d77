package com.nymbl.config.dto.DailyCloseReport;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class DailyCloseBranchDTO {

    List<DCPayment> paymentList = new ArrayList<>();
    List<DCPayment> appliedPaymentList = new ArrayList<>();
    String branchName;
    Long branchId;
    BigDecimal branchCash = BigDecimal.ZERO;
    BigDecimal branchCredit = BigDecimal.ZERO;
    BigDecimal branchCheck = BigDecimal.ZERO;
    BigDecimal branchAdjustment = BigDecimal.ZERO;
    BigDecimal branchElectronic = BigDecimal.ZERO;
    BigDecimal branchAch = BigDecimal.ZERO;
    BigDecimal branchEra = BigDecimal.ZERO;
    BigDecimal branchTotalApplied = BigDecimal.ZERO;
    BigDecimal branchTotalUnApplied = BigDecimal.ZERO;
    BigDecimal branchTotal = BigDecimal.ZERO;

}
