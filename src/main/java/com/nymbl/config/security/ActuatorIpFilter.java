package com.nymbl.config.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.web.util.matcher.IpAddressMatcher;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Actuator IP Filter using Spring's IpAddressMatcher
 */
@Slf4j
@Component
public class ActuatorIpFilter extends OncePerRequestFilter {

    @Value("${actuator.allowed.cidr:}")
    private String allowedCidr;

    @Value("${actuator.ip.filter.enabled:true}")
    private boolean ipFilterEnabled;

    private List<IpAddressMatcher> ipMatchers;
    private boolean hasValidConfiguration = false;

    @Override
    protected void initFilterBean() throws ServletException {
        super.initFilterBean();
        
        if (StringUtils.hasText(allowedCidr)) {
            try {
                // Initialize IP matchers once during startup, handling invalid CIDR formats gracefully
                ipMatchers = Arrays.stream(allowedCidr.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .map(cidr -> {
                        try {
                            return new IpAddressMatcher(cidr);
                        } catch (IllegalArgumentException e) {
                            log.error("Invalid CIDR format '{}': {}", cidr, e.getMessage());
                            return null; // Invalid CIDR will be filtered out
                        }
                    })
                    .filter(Objects::nonNull)
                    .toList();
                
                hasValidConfiguration = !ipMatchers.isEmpty();
                
                if (hasValidConfiguration) {
                    log.info("Initialized actuator IP filter with {} valid CIDR ranges", ipMatchers.size());
                } else {
                    log.warn("No valid CIDR ranges configured for actuator endpoints - all access will be denied");
                }
            } catch (Exception e) {
                log.error("Failed to initialize IP matchers: {}", e.getMessage());
                hasValidConfiguration = false;
            }
        } else {
            log.warn("No CIDR ranges configured for actuator endpoints - all access will be denied");
            hasValidConfiguration = false;
        }
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) 
            throws ServletException, IOException {
        
        String requestPath = request.getRequestURI();
        
        // Allow non-actuator requests and health/info endpoints
        if (!requestPath.startsWith("/actuator") ||
            requestPath.startsWith("/actuator/health") || 
            requestPath.equals("/actuator/info")) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // Skip filtering if disabled
        if (!ipFilterEnabled) {
            log.debug("Actuator IP filtering is disabled - allowing access");
            filterChain.doFilter(request, response);
            return;
        }
        
        // Deny if no valid IP matchers configured (secure by default)
        if (!hasValidConfiguration) {
            log.warn("Actuator access denied - no authorized IP ranges configured for: {}", requestPath);
            denyAccess(response, "No authorized IP ranges configured");
            return;
        }
        
        String clientIp = request.getRemoteAddr();
        
        // Check if client IP matches any allowed CIDR range
        boolean allowed = ipMatchers.stream()
            .anyMatch(matcher -> matcher.matches(clientIp));
            
        if (allowed) {
            log.debug("Actuator access allowed for IP {} to: {}", clientIp, requestPath);
            filterChain.doFilter(request, response);
        } else {
            log.warn("Actuator access denied for IP {} to: {}", clientIp, requestPath);
            denyAccess(response, "IP not in allowed range");
        }
    }
    
    private void denyAccess(HttpServletResponse response, String reason) throws IOException {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("text/plain");
        response.getWriter().write("Access denied: " + reason);
    }
} 