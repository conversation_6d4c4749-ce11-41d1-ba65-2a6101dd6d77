package com.nymbl.config.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
public class ApiSecurityConfiguration {

    private final EntryPointUnauthorizedHandler unauthorizedHandler;
    private final AuthenticationTokenFilter authenticationTokenFilter;
    private final ApiKeyAuthFilter apiKeyAuthFilter;
    private final CorsSecurityConfiguration corsConfiguration;
    private final ActuatorIpFilter actuatorIpFilter;

    public ApiSecurityConfiguration(
            EntryPointUnauthorizedHandler unauthorizedHandler,
            AuthenticationTokenFilter authenticationTokenFilter,
            ApiKeyAuthFilter apiKeyAuthFilter, 
            CorsSecurityConfiguration corsConfiguration,
            ActuatorIpFilter actuatorIpFilter) {
        this.unauthorizedHandler = unauthorizedHandler;
        this.authenticationTokenFilter = authenticationTokenFilter;
        this.apiKeyAuthFilter = apiKeyAuthFilter;
        this.corsConfiguration = corsConfiguration;
        this.actuatorIpFilter = actuatorIpFilter;
    }

    @Bean
    @Order(3)
    public SecurityFilterChain apiKeySecurityFilterChain(HttpSecurity http) throws Exception {
        http
                .securityMatcher( "/medsender/**"
                )
                .cors(cors -> cors.configurationSource(corsConfiguration.corsConfigurationSource()))
                .csrf(AbstractHttpConfigurer::disable)
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
                .authorizeHttpRequests(auth -> auth
                        // API endpoints
                        .requestMatchers(HttpMethod.POST, "/medsender/**").authenticated()

                        .anyRequest().authenticated()
                )
                .addFilterBefore(apiKeyAuthFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    @Order(2)
    public SecurityFilterChain apiSecurityFilterChain(HttpSecurity http) throws Exception {
        http
            .securityMatcher(
                "/api/**", "/actuator", "/actuator/**",
                "/uploads/**", "/logs/**", "/refresh/**",
                "/confirmation/**", "/confirm_appointment/**",
                "/guest-auth/**", "/notification/**", "/auth/**", "/cron/**",
                "/survey/**", "/patient_intake/**", "/nymbl_patient_pay/**",
                "/retail/**", "/test/**", "/console/**"
            )
            .cors(cors -> cors.configurationSource(corsConfiguration.corsConfigurationSource()))
            .csrf(AbstractHttpConfigurer::disable)
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
            .authorizeHttpRequests(auth -> auth
                // API endpoints
                .requestMatchers(HttpMethod.OPTIONS, "/api/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/**").authenticated()
                .requestMatchers(HttpMethod.POST, "/api/**").authenticated()
                .requestMatchers(HttpMethod.PUT, "/api/**").authenticated()
                .requestMatchers(HttpMethod.PATCH, "/api/**").authenticated()
                .requestMatchers(HttpMethod.DELETE, "/api/**").authenticated()
                
                // Actuator endpoints - controlled by IP filter
                .requestMatchers("/actuator", "/actuator/**").permitAll()
                
                // Swagger/OpenAPI endpoints
                .requestMatchers("/v2/api-docs", "/configuration/**", "/swagger*/**", 
                               "/webjars/**", "/v3/api-docs/**", "/swagger-ui.html").permitAll()

                // Protected resources
                .requestMatchers("/uploads/**", "/logs/**", "/refresh/**").authenticated()

                // Public endpoints
                .requestMatchers("/confirmation/**", "/confirm_appointment/**", "/guest-auth/**", 
                               "/notification/**", "/auth/**", "/cron/**", "/survey/**", 
                               "/patient_intake/**", "/nymbl_patient_pay/**", "/retail/**").permitAll()
                
                // Development/testing endpoints
                .requestMatchers("/test/**", "/console/**").permitAll()
                
                .anyRequest().authenticated()
            )
            .addFilterBefore(actuatorIpFilter, UsernamePasswordAuthenticationFilter.class)
            .addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
} 
