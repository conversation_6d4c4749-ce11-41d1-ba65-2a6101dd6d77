package com.nymbl.config.security;

import com.nymbl.config.utils.SsoUtils;
import com.nymbl.master.model.User;
import com.nymbl.master.repository.UserRepository;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

@Slf4j
@Component
public class SuccessHandler implements AuthenticationSuccessHandler {

    private final UserRepository userRepository;

    @Autowired
    public SuccessHandler(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException {
        OAuth2User oauth2User = (OAuth2User) authentication.getPrincipal();
        String email = oauth2User.getAttributes().get("email").toString();

        List<User> users = userRepository.findByEmail(email);
        if (users.size() != 1 || !users.get(0).getActive()) {
            new DefaultRedirectStrategy().sendRedirect(request, response, "/sso_error");
            return;
        }

        SsoUtils.setEmailCookie(response, email);

        new DefaultRedirectStrategy().sendRedirect(request, response, "/sso_redirect");
    }
}
