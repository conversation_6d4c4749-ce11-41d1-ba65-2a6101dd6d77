package com.nymbl.config.cron;

import com.nymbl.config.enums.AlertType;
import com.nymbl.config.service.QuicksightExportService;
import com.nymbl.config.utils.DateUtil;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.master.model.Company;
import com.nymbl.master.model.User;
import com.nymbl.master.repository.CompanyRepository;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.interfaces.service.PrescriptionSummaryService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.service.*;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class DailyJobExecutor {

    private final CompanyRepository companyRepository;
    private final PrescriptionService prescriptionService;
    private final NotificationService notificationService;
    private final UserNotificationService userNotificationService;
    private final InsuranceVerificationService insuranceVerificationService;
    private final CriticalMessageService criticalMessageService;
    private final TaskService taskService;
    private final InsuranceCompanyService insuranceCompanyService;
    private final PrescriptionFileService prescriptionFileService;
    private final PhysicianService physicianService;
    private final InsuranceVerificationLCodeService insuranceVerificationLCodeService;
    private final GeneralLedgerService generalLedgerService;
    private final ClinicalOperationsService clinicalOperationsService;
    private final PrescriptionSummaryService rxSummaryService;
    private final UserService userService;
    private final FeatureFlagService featureFlagService;
    private final GeneralLedgerService2 generalLedgerService2;
    private final QuicksightExportService quicksightExportService;

    @Value("#{${nymbl.daily.timezone.dt.map}}")
    private Map<Integer, String> dailyTzMap;

    @Value("#{${nymbl.ledger.timezone.dt.map}}")
    private Map<Integer, String> glTzMap;

    @Value("#{${nymbl.clinic.timezone.dt.map}}")
    private Map<Integer, String> coTzMap;

    @Value("#{${nymbl.quicksight.timezone.dt.map}}")
    private Map<Integer, String> qsTzMap;

    @Autowired
    public DailyJobExecutor(CompanyRepository companyRepository,
                            PrescriptionService prescriptionService,
                            NotificationService notificationService,
                            UserNotificationService userNotificationService,
                            InsuranceVerificationService insuranceVerificationService,
                            CriticalMessageService criticalMessageService,
                            TaskService taskService,
                            InsuranceCompanyService insuranceCompanyService,
                            PrescriptionFileService prescriptionFileService,
                            PhysicianService physicianService,
                            InsuranceVerificationLCodeService insuranceVerificationLCodeService,
                            GeneralLedgerService generalLedgerService,
                            ClinicalOperationsService clinicalOperationsService,
                            PrescriptionSummaryService rxSummaryService,
                            PurchasingHistoryService purchasingHistoryService,
                            UserService userService,
                            FeatureFlagService featureFlagService,
                            GeneralLedgerService2 generalLedgerService2,
                            QuicksightExportService quicksightExportService,
                            UserPrivilegeService userPrivilegeService) {
        this.companyRepository = companyRepository;
        this.prescriptionService = prescriptionService;
        this.notificationService = notificationService;
        this.userNotificationService = userNotificationService;
        this.insuranceVerificationService = insuranceVerificationService;
        this.criticalMessageService = criticalMessageService;
        this.taskService = taskService;
        this.insuranceCompanyService = insuranceCompanyService;
        this.prescriptionFileService = prescriptionFileService;
        this.physicianService = physicianService;
        this.insuranceVerificationLCodeService = insuranceVerificationLCodeService;
        this.generalLedgerService = generalLedgerService;
        this.clinicalOperationsService = clinicalOperationsService;
        this.rxSummaryService = rxSummaryService;
        this.userService = userService;
        this.featureFlagService = featureFlagService;
        this.generalLedgerService2 = generalLedgerService2;
        this.quicksightExportService = quicksightExportService;
    }

    //Daily Jobs Everyday at 3:15 AM PT/ET to ensure west coast has started a new day
    public void userAssetPermissionsJob() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily User Asset QuickSight Start Time = " + DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("Daily User Asset QuickSight  - Start Time =  " + DateUtil.timeFormatter().format(now));
        try {
            quicksightExportService.uploadAssetUserPermissionsCsvS3Quicksight();
            log.info("Daily User Asset QuickSight Finish Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
            Sentry.captureMessage("Daily User Asset QuickSight  - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        } catch (Exception e) {
            log.error("Daily User Asset QuickSight - Failed : {}", e.getMessage());
            Sentry.captureMessage("Daily User Asset QuickSight - Failed : " + e.getMessage());
        }
    }

    public void userPermissionsJob() {
        try {
            LocalDateTime now = LocalDateTime.now();
            log.info("Daily User Permissions QuickSight Start Time = {}", DateUtil.timeFormatter().format(now));
            Sentry.captureMessage("Daily User Permissions QuickSight  - Start Time =  " + DateUtil.timeFormatter().format(now));
            quicksightExportService.uploadUserPermissionsCsvS3Quicksight();
            log.info("Daily User Permissions QuickSight Finish Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
            Sentry.captureMessage("Daily User Permissions QuickSight  - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        } catch (Exception e) {
            log.error("Daily User Permissions QuickSight - Failed : {}", e.getMessage());
            Sentry.captureMessage("Daily User Permissions QuickSight - Failed : " + e.getMessage());
        }
    }

    public void billDmeRentalsJob() {

        LocalDateTime now = LocalDateTime.now();
        log.info("BillDmeRentals Job - Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("BillDmeRentals Job - Start Time = " + DateUtil.timeFormatter().format(now));
        User u = userService.findOne(1L);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(u, null, u.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
        for (Company company : companies) {
            TenantContext.setCurrentTenant(company.getKey());
            prescriptionService.billDmeRentals(DateUtil.getCurrentDate());
            TenantContext.clear();
        }
        log.info("BillDmeRentals Job - End Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("BillDmeRentals Job - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void requiredDocumentsAlertJob() {
        LocalDateTime now = LocalDateTime.now();
        log.info("RequiredDocumentsAlert Job - Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("RequiredDocumentsAlert Job - Start Time = " + DateUtil.timeFormatter().format(now));
        User u = userService.findOne(1L);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(u, null, u.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
        for (Company company : companies) {
            TenantContext.setCurrentTenant(company.getKey());
            requiredDocumentsAlert();
            TenantContext.clear();
        }
        log.info("RequiredDocumentsAlert Job - End Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("RequiredDocumentsAlert Job - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void timelyFilingJob() {
        LocalDateTime now = LocalDateTime.now();
        log.info("TimelyFiling Job - Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("TimelyFiling Job - Start Time = " + DateUtil.timeFormatter().format(now));
        User u = userService.findOne(1L);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(u, null, u.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
        for (Company company : companies) {
            TenantContext.setCurrentTenant(company.getKey());
            timelyFiling();
            TenantContext.clear();
        }
        log.info("TimelyFiling Job - End Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("TimelyFiling Job - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void insuranceVerificationLCodePreAuthExpirationJob() {
        LocalDateTime now = LocalDateTime.now();
        log.info("InsuranceVerificationLCodePreAuthExpiration Job - Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("InsuranceVerificationLCodePreAuthExpiration Job - Start Time = " + DateUtil.timeFormatter().format(now));
        User u = userService.findOne(1L);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(u, null, u.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
        for (Company company : companies) {
            TenantContext.setCurrentTenant(company.getKey());
            insuranceVerificationLCodePreAuthExpiration();
            TenantContext.clear();
        }
        log.info("InsuranceVerificationLCodePreAuthExpiration Job - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("InsuranceVerificationLCodePreAuthExpiration Job - End Time =  " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void preAuthExpirationJob() {
        LocalDateTime now = LocalDateTime.now();
        log.info("PreAuthExpiration Job - Start Time = " + DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("PreAuthExpiration Job - Start Time = " + DateUtil.timeFormatter().format(now));
        User u = userService.findOne(1L);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(u, null, u.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
        for (Company company : companies) {
            TenantContext.setCurrentTenant(company.getKey());
            preAuthExpiration();
            TenantContext.clear();
        }
        log.info("PreAuthExpiration Job - End Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("PreAuthExpiration Job - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void patientRecallJob() {
        LocalDateTime now = LocalDateTime.now();
        log.info("PatientRecall Job - Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("PatientRecall Job - Start Time = " + DateUtil.timeFormatter().format(now));
        User u = userService.findOne(1L);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(u, null, u.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
        for (Company company : companies) {
            TenantContext.setCurrentTenant(company.getKey());
            patientRecall();
            TenantContext.clear();
        }
        log.info("PatientRecall Job - End Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("PatientRecall Job - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void dueTasksJob() {
        LocalDateTime now = LocalDateTime.now();
        log.info("DueTasks Job - Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("DueTasks Job - Start Time =  " + DateUtil.timeFormatter().format(now));
        User u = userService.findOne(1L);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(u, null, u.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
        for (Company company : companies) {
            TenantContext.setCurrentTenant(company.getKey());
            dueTasks();
            TenantContext.clear();
        }
        log.info("DueTasks Job - End Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("DueTasks Job - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void pecosVerificationUpdate() {
        log.info("Bimonthly Job - Start Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Bimonthly Job - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        List<Company> companies = companyRepository.findAllByActiveTrue();
        for (Company c : companies) {
            TenantContext.setCurrentTenant(c.getKey());
            physicianService.pecosVerificationUpdate();
            TenantContext.clear();
        }
        log.info("Bimonthly Job - End Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Bimonthly Job - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));

    }

    public void dailyGeneralLedgerUpdate() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily General Ledger Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("Daily General Ledger Job - Start Time =  " + DateUtil.timeFormatter().format(now));
        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(glTzMap.get(now.getHour()));
        for (Company c : companies) {
            TenantContext.setCurrentTenant(c.getKey());
            generalLedgerService.dailyReload(c.getHasQuicksight());
            TenantContext.clear();
        }
        log.info("Daily General Ledger Finish Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily General Ledger Job - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void dailyClinicalOperationsUpdate() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily Clinical Operations Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("Daily Clinical Operations Job - Start Time = " + DateUtil.timeFormatter().format(now));

        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(coTzMap.get(now.getHour()));
        for (Company c : companies) {
            TenantContext.setCurrentTenant(c.getKey());
            clinicalOperationsService.clinicalOperationsPopulate();
            if (c.getHasQuicksight()) {
                quicksightExportService.uploadClinicalOperationsCsvS3Quicksight();
            }
            if (featureFlagService.findFeatureFlagByFeature("upload_clinical_operations_aws_s3") != null) {
                clinicalOperationsService.uploadClinicalOpsToAws();
            }
            TenantContext.clear();
        }
        log.info("Daily Clinical Operations Finish Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily Clinical Operations Job - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));

    }

    public void dailyGeneralLedgerQuickSightUpdate() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily General Ledger QuickSight Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("Daily General Ledger QuickSight - Start Time = " + DateUtil.timeFormatter().format(now));

        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(qsTzMap.get(now.getHour()));
        for (Company c : companies) {
            TenantContext.setCurrentTenant(c.getKey());
            if (BooleanUtils.toBooleanDefaultIfNull(c.getHasQuicksight(), Boolean.FALSE)) {
                quicksightExportService.uploadGeneralLedgerCsvS3Quicksight();
            }
            if (featureFlagService.findFeatureFlagByFeature("upload_general_ledger_aws_s3") != null) {
                generalLedgerService.importGeneralLedgerHistoryToMultitenant();
                generalLedgerService.triggerGLToAws();
            }
            TenantContext.clear();
        }
        log.info("Daily General Ledger QuickSight Finish Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily General Ledger QuickSight  - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));

    }

    public void dailyPurchasingHistoryQuickSightUpdate() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily Purchasing History QuickSight Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("Daily Purchasing History QuickSight - Start Time = " + DateUtil.timeFormatter().format(now));

        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(qsTzMap.get(now.getHour()));
        for (Company c : companies) {
            if (BooleanUtils.toBooleanDefaultIfNull(c.getHasQuicksight(), Boolean.FALSE)) {
                TenantContext.setCurrentTenant(c.getKey());
                quicksightExportService.uploadPurchasingHistoryCsvS3Quicksight();
//                purchasingHistoryService.runDailyPurchasingHistoryMultiTenantPopulation(c.getKey());
                TenantContext.clear();
            }
        }
        log.info("Daily Purchasing History QuickSight Finish Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily Purchasing History QuickSight  - End Time =  " + DateUtil.timeFormatter().format(LocalDateTime.now()));

    }

    public void dailyPrescriptionSummaryQuickSightUpdate() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily Prescription Summary QuickSight Start Time = " + DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("Daily Prescription Summary QuickSight  - Start Time =  " + DateUtil.timeFormatter().format(now));

        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(qsTzMap.get(now.getHour()));
        for (Company c : companies) {
            TenantContext.setCurrentTenant(c.getKey());
            if (BooleanUtils.toBooleanDefaultIfNull(c.getHasQuicksight(), Boolean.FALSE)) {
                quicksightExportService.uploadPrescriptionSummaryCsvS3Quicksight();
            }
            if (featureFlagService.findFeatureFlagByFeature("upload_rx_summary_aws_s3") != null) {
                rxSummaryService.importPrescriptionSummary(c.getKey());
                rxSummaryService.uploadRxSummaryToAws();
            }
            TenantContext.clear();
        }
        log.info("Daily Prescription Summary QuickSight Finish Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily Prescription Summary QuickSight  - End Time =  " + DateUtil.timeFormatter().format(LocalDateTime.now()));

    }

    public void dailyNymblStatusHistoryQuicksightUpdate() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily Nymbl Status History QuickSight Start Time = " + DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("Daily Nymbl Status History QuickSight - Start Time = " + DateUtil.timeFormatter().format(now));

        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(qsTzMap.get(now.getHour()));
        for (Company c : companies) {
            if (BooleanUtils.toBooleanDefaultIfNull(c.getHasQuicksight(), Boolean.FALSE)) {
                TenantContext.setCurrentTenant(c.getKey());
                quicksightExportService.uploadNymblStatusHistoryCsvS3Quicksight();
                TenantContext.clear();
            }
        }
        log.info("Daily Nymbl Status History QuickSight Finish Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily Nymbl Status History QuickSight - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));

    }

    public void dailyAiNotesUsageQuicksightUpdate() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily AI Notes Usage QuickSight Start Time = " + DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("Daily AI Notes Usage QuickSight - Start Time = " + DateUtil.timeFormatter().format(now));

        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(qsTzMap.get(now.getHour()));
        for (Company c : companies) {
            if (BooleanUtils.toBooleanDefaultIfNull(c.getHasQuicksight(), Boolean.FALSE)) {
                TenantContext.setCurrentTenant(c.getKey());
                quicksightExportService.uploadAiNotesUsageCsvS3Quicksight();
                TenantContext.clear();
            }
        }
        log.info("Daily AI Notes Usage QuickSight Finish Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily AI Notes Usage QuickSight - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));

    }

    public void dailyLincareAlacritiPatientStatementS3ftpUpdate() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily Lincare Alacriti Patient Statement Start Time = " + DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("Daily Lincare Alacriti Patient Statement - Start Time = " + DateUtil.timeFormatter().format(now));

        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(qsTzMap.get(now.getHour()));
        for (Company c : companies) {
            TenantContext.setCurrentTenant(c.getKey());
            if (featureFlagService.findFeatureFlagByFeature("upload_lincare_alacriti_patient_statement") != null) {
                quicksightExportService.uploadLincareAlacritiPatientStatementCsvS3ftp();
            }
            TenantContext.clear();
        }
        log.info("Daily Lincare Alacriti Patient Statement Finish Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily Lincare Alacriti Patient Statement - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));

    }
    public void dailyUserTaskQuicksightUpdate() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily Task QuickSight Start Time = {}", DateUtil.timeFormatter().format(now));
        Sentry.captureMessage("Daily Task QuickSight - Start Time =  " + DateUtil.timeFormatter().format(now));

        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(qsTzMap.get(now.getHour()));
        for (Company c : companies) {
            TenantContext.setCurrentTenant(c.getKey());
            boolean clientSftp = featureFlagService.findFeatureFlagByFeature("upload_user_task_s3_sftp") != null;
            if (BooleanUtils.toBooleanDefaultIfNull(c.getHasQuicksight(), Boolean.FALSE) || clientSftp) {
                quicksightExportService.uploadUserTaskCsvS3Quicksight();
                if (clientSftp) {
                    quicksightExportService.uploadQuicksightUserTaskCsvAwsSftp();
                }
            }
            TenantContext.clear();
        }
        log.info("Daily Task QuickSight Finish Time = {}", DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily Task QuickSight - End Time =  " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    //TODO figure out a time best to be done in the future because QS will use this.  FUTURE
    public void dailyStaticGeneralLedgerPopulation() {
        log.info("Daily Static General Ledger Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily Static General Ledger - Start Time =  " + DateUtil.timeFormatter().format(LocalDateTime.now()));

        List<Company> companies = companyRepository.findAllByActiveTrue();
        for (Company c : companies) {
            TenantContext.setCurrentTenant(c.getKey());
            generalLedgerService.executeStaticGeneralLedgerProcess();
            TenantContext.clear();
        }
        log.info("Daily Static General Ledger Finish Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        Sentry.captureMessage("Daily Static General Ledger - End Time =  " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void patientRecall() {
        log.info("Patient Recall " + TenantContext.getCurrentTenant() + " - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        List<Prescription> prescriptions = prescriptionService.findByPatientRecallIsTrue();
        for (Prescription p : prescriptions) {
            if (p.getDeliveredOn() != null &&
                    p.getRecallDays() != null &&
                    p.getPatientRecallUserId() != null &&
                    DateUtil.isOlderThan(p.getDeliveredOn(), p.getRecallDays())) {

                String message = p.getRecallReason();
                Long idClass = p.getId();
                Long patientId = p.getPatientId();
                Long userId = p.getPatientRecallUserId();
                p.setPatientRecall(false);
                prescriptionService.save(p);
                try {
                    userNotificationService.createUserNotification(AlertType.CLINICAL.ordinal(), message, "prescription", idClass, patientId, userId, null);
                } catch (Exception e) {
                    log.error(StringUtil.getExceptionAsString(e));
                }
            }
        }
        log.info("Patient Recall " + TenantContext.getCurrentTenant() + " - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void dueTasks() {
        log.info("Due Tasks " + TenantContext.getCurrentTenant() + " - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        List<Task> tasks = taskService.findByDueDateAndCompleted(new Date());
        if (tasks != null) {
            for (Task task : tasks.stream().filter(task -> task.getUser() != null).toList()) {
                try {
                    String message = "You have a task due today: \"" + task.getName() + "\"";
                    Long idClass = task.getId();
                    Long patientId = task.getPatientId();
                    Long userId = task.getUserId();
                    if (task.getUser().getNotificationTypes().stream().anyMatch(o -> o.getName().equals("Task Notification"))) {
                        userNotificationService.createUserNotification(AlertType.GENERAL.ordinal(), message, "task", idClass, patientId, userId, task.getId());
                    }
                } catch (Exception e) {
                    log.error(StringUtil.getExceptionAsString(e));
                }
            }
        }
        log.info("Due Tasks " + TenantContext.getCurrentTenant() + " - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void preAuthExpiration() {
        log.info("Pre-Auth Critical Message " + TenantContext.getCurrentTenant() + " - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        Date juDate = new Date();
        DateTime today = new DateTime(juDate);
        java.sql.Date sqlToday = new java.sql.Date(juDate.getTime());
        DateTime twoWeeksFromNow = today.plusDays(14);
        Date expirationDate = twoWeeksFromNow.toDate();
        try {
            List<InsuranceVerification> ivs = insuranceVerificationService.findByExpirationDate(expirationDate);
            for (InsuranceVerification iv : ivs) {
                CriticalMessage c = new CriticalMessage();
                c.setDate(sqlToday);
                c.setUserId(1L);
                c.setPatientId(iv.getPatientInsurance().getPatientId());
                c.setMessage("The pre-authorization for " + iv.getPatientInsurance().getInsuranceCompany().getName() + " will expire on " + expirationDate);
                criticalMessageService.save(c);
            }
        } catch (Exception e) {
            log.error(StringUtil.getExceptionAsString(e));
        }
        log.info("Pre-Auth Critical Message " + TenantContext.getCurrentTenant() + " - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void timelyFiling() {
        log.info("Timely Filing " + TenantContext.getCurrentTenant() + " - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        try {
            List<InsuranceCompany> insuranceCompanies = insuranceCompanyService.findByTimelyFilingDaysIsNotNull();
            for (InsuranceCompany ic : insuranceCompanies) {
                List<Prescription> prescriptions = prescriptionService.findByInsuranceCompanyWithNoClaimSubmissionsAndTimelyFilingToday(ic.getId());
                for (Prescription prescription : prescriptions) {
                    try {
                        notificationService.createTimelyFilingNotification(prescription);
                    } catch (Exception e) {
                        log.error(StringUtil.getExceptionAsString(e));
                    }
                }
            }
        } catch (Exception e) {
            log.error(StringUtil.getExceptionAsString(e));
        }
        log.info("Timely Filing " + TenantContext.getCurrentTenant() + " - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void requiredDocumentsAlert() {
        log.info("Required Documentation Alert " + TenantContext.getCurrentTenant() + " - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        try {
            prescriptionFileService.requiredDocumentsAlert();
        } catch (Exception e) {
            log.error(StringUtil.getExceptionAsString(e));
        }
        log.info("Required Documentation Alert " + TenantContext.getCurrentTenant() + " - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void insuranceVerificationLCodePreAuthExpiration() {
        log.info("Insurance Verification L-Code Pre-Authorization Expiration Date Alert " +
                TenantContext.getCurrentTenant() + " - Start Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
        try {
            LocalDate today = LocalDate.now();
            LocalDate twoWeeksFromNow = today.plusDays(14);
            String expirationDateString = twoWeeksFromNow.toString();
            Date expirationDate = Date.from(twoWeeksFromNow.atStartOfDay(ZoneId.systemDefault()).toInstant());
            List<InsuranceVerification_L_Code> ivlcs = insuranceVerificationLCodeService
                    .findByExpirationDateWhereInsuranceVerificationIsActiveAndRxHasNoClaimSubmission(expirationDateString);
            for (InsuranceVerification_L_Code ivlc : ivlcs) {
                CriticalMessage c = criticalMessageService.createIVLCPreAuthCriticalMessage(ivlc, expirationDate);
                criticalMessageService.save(c);
            }
        } catch (Exception e) {
            log.error(StringUtil.getExceptionAsString(e));
        }
        log.info("Insurance Verification L-Code Pre-Authorization Expiration Date Alert " +
                TenantContext.getCurrentTenant() + " - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }

    public void dataMigrationLiveGL() {
        log.info("Nightly Live GL Migration Start");
        List<Company> companies = companyRepository.findAllByActiveTrue();
        for (Company c : companies) {
            TenantContext.setCurrentTenant(c.getKey());
            if (featureFlagService.findFeatureFlagByFeature("live_gl") != null) {
                //Only run if it is the first overnight and no transactions are present and live gl feature flag is present
                generalLedgerService2.runDataMigrationForLiveGL(c.getKey());
            }
            TenantContext.clear();
        }
        log.info("Nightly Live GL Migration Start");
    }

    /**
     * Check insurance eligibility the day before
     * prescription auto-renewal via waystarAPI
     */
    public void checkEligibility() {
        LocalDateTime now = LocalDateTime.now();
        log.info("Daily Eligibility Check - Start Time = " + DateUtil.timeFormatter().format(now));

        User u = userService.findOne(1L);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(u, null, u.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        List<Company> companies = companyRepository.findAllByActiveTrueAndTimezoneEqualsOrderByCronSequenceAsc(dailyTzMap.get(now.getHour()));
        for (Company company : companies) {
            TenantContext.setCurrentTenant(company.getKey());
            // We are checking eligibility for tomorrow's prescriptions
            prescriptionService.checkEligibility(DateTime.now().plusDays(1).toDate());
            TenantContext.clear();
        }
        log.info("Daily Eligibility Check - End Time = " + DateUtil.timeFormatter().format(LocalDateTime.now()));
    }
}
