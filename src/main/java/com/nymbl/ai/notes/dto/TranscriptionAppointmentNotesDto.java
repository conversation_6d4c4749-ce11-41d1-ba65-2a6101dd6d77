package com.nymbl.ai.notes.dto;

import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentation;
import com.nymbl.ai.notes.data.conversation.Conversation;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TranscriptionAppointmentNotesDto {

    private Conversation conversation;
    private ClinicalDocumentation clinicalDocumentation;
    /**
     * Concatenated clinical documentation sections.
     * TODO discuss getting HTML instead of Mark Down.
     */
    private String reviewNoteString;
    private String transcriptString;
    private Long transcriptionDetailsId;
    private Long noteId;
    private boolean isReviewed;     // TODO AI Notes Phase 3 Save as Draft/Sign/Published
    private AINOTE status;          // TODO AI Notes Phase 3 return status
    private String subject;         // TODO AI Notes Phase 3 return subject
    private boolean isArchived;
}
