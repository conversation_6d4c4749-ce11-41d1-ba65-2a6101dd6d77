package com.nymbl.ai.notes.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ReviewedNoteRequest {
    @Schema(description = "Note string")
    private String reviewedNoteString;
    @Schema(description = "Transcriptions detail id", implementation = Long.class)
    private Long transcriptionDetailId;
    @Schema(description = "Note subject")
    private String subject;
    @Schema(description = "Treating practitioner id", implementation = Long.class)
    private Long treatingPractitionerId;
    @Schema(description = "Patient id", implementation = Long.class)
    private Long patientId;
    @Schema(description = "Appointment id", implementation = Long.class)
    private Long appointmentId;
    @Schema(description = "Prescription id", implementation = Long.class)
    private Long prescriptionId;
    @Schema(description = "Note id", implementation = Long.class)
    private Long noteId;
    @Schema(description = "Action being performed")
    private AINOTE action;
}
