package com.nymbl.ai.notes.controller;


import com.nymbl.ai.notes.dto.TranscriptionUploadRequest;
import com.nymbl.ai.notes.service.HealthScribeService;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.master.service.CompanyService;
import com.nymbl.tenant.TenantContext;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/healthscribe")
@Slf4j
public class HealthScribeController {

    private final TranscriptionUtil transcriptionUtil;
    @Value("${healthscribe.lambda.event}")
    private String lambdaEvent;

    private final HealthScribeService healthScribeService;
    private final CompanyService companyService;
    private final AwsUtil awsUtil;

    public HealthScribeController(HealthScribeService healthScribeService, CompanyService companyService, AwsUtil awsUtil, TranscriptionUtil transcriptionUtil) {
        this.healthScribeService = healthScribeService;
        this.companyService = companyService;
        this.awsUtil = awsUtil;
        this.transcriptionUtil = transcriptionUtil;
    }

    /**
     * Triggers the AI Notes transcription processing
     *
     * @param file           file
     * @param practitionerId practitionerId
     * @param appointmentId  appointmentId
     * @param patientId      patientId
     * @param prescriptionId prescriptionId
     * @param branchId       branchId
     * @param audioLength    audioLength
     * @return response
     */
    @Operation(
            summary = "Trigger AI Note processing",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Successfully triggered transcription for process",
                            content = @Content(
                                    schema = @Schema(implementation = String.class)
                            )
                    ),
                    @ApiResponse(responseCode = "400", description = "Invalid request", content = @Content),
                    @ApiResponse(responseCode = "500", description = "Server error", content = @Content)
            }
    )
    @PostMapping(value = "/upload-audio", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> uploadAudio(@RequestParam("file") MultipartFile file, Long practitionerId, Long appointmentId, Long patientId, Long prescriptionId, Long branchId, double audioLength) {

        log.info("AWSConfig: Begin audio file Upload to HealthScribe");
        TranscriptionUploadRequest request = new TranscriptionUploadRequest();
        request.setPrescriptionId(prescriptionId);
        request.setBranchId(branchId);
        request.setAppointmentId(appointmentId);
        request.setPatientId(patientId);
        request.setPractitionerId(practitionerId);
        // TODO -> Removed the previous logic, is going away with disabling of v1, remove this comment before release
        request.setFileExtension(transcriptionUtil.getFileExtension(file));
        request.setAudioLength(audioLength);


        try
        {
            Long detailsId = healthScribeService.initiateRecording(request);
            String response = healthScribeService.uploadAudioFileToS3(file, detailsId);
            if ("success".equals(response)) {
                log.info("AWSConfig: End audio file Upload to HealthScribe");
                return ResponseEntity.ok(response);
            }
            else {
                log.error("AWSConfig: Error in audio file Upload to HealthScribe");
                return ResponseEntity.internalServerError().body(response);
            }
        }
        catch (Exception ex)
        {
            log.error("AWSConfig: Exception in audio file Upload to HealthScribe {}", ex.getMessage());
            return ResponseEntity.internalServerError().body(ex.getMessage());
        }

    }

    /**
     * STOP: This is AI Notes maintenance endpoint only
     * This method is used to set up S3 buckets and lambda events for AI Notes tenants
     *
     * @return success message
     */
    @GetMapping(value = "/create-bucket", produces = MediaType.APPLICATION_JSON_VALUE)
    @Hidden
    public ResponseEntity<?> createBucket() {

        String tenant = TenantContext.getCurrentTenant();
        log.info("AWSConfig: Begin create s3 bucket for HealthScribe");
        if (companyService.findByKey(tenant) == null) {
            return ResponseEntity.internalServerError().body("Invalid tenant Provided");
        }

        try
        {
            AWSS3Service awss3Service = awsUtil.getUsEast1Client();
            String bucketName = healthScribeService.getOutputBucketName(tenant);
            if (awss3Service.doesBucketExist(bucketName)) {
                return ResponseEntity.ok("Bucket already exists!!!");
            }

            awss3Service.createBucketAndAddLambdaTrigger(bucketName, lambdaEvent);
        } catch (Exception ex) {
            return ResponseEntity.internalServerError().body(ex.getMessage());
        }

        log.info("AWSConfig: End create s3 bucket for HealthScribe");
        return ResponseEntity.ok("success");
    }

}
