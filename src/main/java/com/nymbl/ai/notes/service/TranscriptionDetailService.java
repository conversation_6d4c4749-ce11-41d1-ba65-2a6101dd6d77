package com.nymbl.ai.notes.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentation;
import com.nymbl.ai.notes.data.clinicaldoc.ClinicalDocumentationContainer;
import com.nymbl.ai.notes.data.clinicaldoc.Section;
import com.nymbl.ai.notes.data.conversation.Conversation;
import com.nymbl.ai.notes.data.conversation.ConversationContainer;
import com.nymbl.ai.notes.data.conversation.TranscriptSegment;
import com.nymbl.ai.notes.dto.*;
import com.nymbl.ai.notes.exception.TranscriptionDetailException;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.repository.TranscriptionDetailRepository;
import com.nymbl.ai.notes.util.TranscriptionUtil;
import com.nymbl.config.aws.AwsUtil;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.utils.OptimisticLockingUtil;
import com.nymbl.master.model.User;
import com.nymbl.master.service.AWSS3Service;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.Appointment;
import com.nymbl.tenant.model.Note;
import com.nymbl.tenant.model.Prescription;
import com.nymbl.tenant.service.AppointmentService;
import com.nymbl.tenant.service.NoteService;
import com.nymbl.tenant.service.NotificationService;
import com.nymbl.tenant.service.PrescriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TranscriptionDetailService extends AbstractTableService<TranscriptionDetail, Long> {

    private final TranscriptionDetailRepository transcriptionDetailRepository;
    private final TranscriptionUtil transcriptionUtil;
    private final NotificationService notificationService;
    private final PrescriptionService prescriptionService;
    private final AppointmentService appointmentService;
    private final NoteService noteService;
    private final AwsUtil awsUtil;
    private final UserService userService;

    private final String DRAFT_SUBJECT = "AI Note: Save As Draft Generated Subject Line";

    public TranscriptionDetailService(TranscriptionDetailRepository transcriptionDetailRepository, TranscriptionUtil transcriptionUtil, NotificationService notificationService, PrescriptionService prescriptionService, AppointmentService appointmentService, NoteService noteService, AwsUtil awsUtil, UserService userService) {
        super(transcriptionDetailRepository);
        this.transcriptionDetailRepository = transcriptionDetailRepository;
        this.transcriptionUtil = transcriptionUtil;
        this.notificationService = notificationService;
        this.prescriptionService = prescriptionService;
        this.appointmentService = appointmentService;
        this.noteService = noteService;
        this.awsUtil = awsUtil;
        this.userService = userService;
    }

    /**
     * Search AI notes for the AI notes list display page
     *
     * @param practitionerId practitionerId
     * @param patientId patientId
     * @param appointmentId appointmentId
     * @param startDate startDate
     * @param endDate endDate
     * @param status status
     * @param isArchived isArchived
     * @param pageable pageable
     * @return List<TranscriptionDetailsDto>
     */
    public Optional<List<TranscriptionDetailsDto>> search(List<Long> practitionerId, Long patientId, Long appointmentId, Date startDate, Date endDate, String status, boolean isArchived, Pageable pageable) {
        try
        {
            Specification<TranscriptionDetail> transcriptionDetailsSpecification = TranscriptionDetailSpecs.search(practitionerId, patientId, appointmentId, startDate, endDate, status, isArchived);
            List<TranscriptionDetail> results = transcriptionDetailRepository.findAll(transcriptionDetailsSpecification, pageable).getContent();
            return Optional.of(results.stream()
                    .map(transcriptionUtil::convertToDto)
                    .sorted(new TranscriptionStatusComparator())
                    .collect(Collectors.toList()));
        }
        catch(Exception ex) {
            log.error("Transcription: Exception searching for transcription list ", ex);
        }
        return Optional.empty();
    }

    /**
     * Finds Transcription details by Id
     *
     * @param detailsId detailsId
     * @return Optional<TranscriptionDetailsDto>
     */
    public Optional<TranscriptionDetailsDto> getDetailsById(Long detailsId) throws TranscriptionDetailException {
        try
        {
            Optional<TranscriptionDetail> details = transcriptionDetailRepository.findById(detailsId);
            if(details.isPresent()) {
                return Optional.of(transcriptionUtil.convertToDto(details.get()));
            }
        }
        catch(Exception ex) {
            log.error("Transcription: Exception finding transcription by id {}", detailsId, ex);
            throw new TranscriptionDetailException("Transcription: Exception finding transcription");
        }
        return Optional.empty();
    }

    /**
     * This method pulls in AI transcription results from S3
     *
     * @param aiNoteWebhookDto AiNoteWebhookDto
     * @return boolean
     * @throws TranscriptionDetailException on error
     */
    public boolean writeCompletedNotesFromBucket(AiNoteWebhookDto aiNoteWebhookDto) throws TranscriptionDetailException {

        Long detailId = transcriptionUtil.getTranscriptionDetailsId(aiNoteWebhookDto.getJobName());

        Optional<TranscriptionDetail> transcriptionDetailsOptional = transcriptionDetailRepository.findById(detailId);
        
        if (transcriptionDetailsOptional.isEmpty()) {
            throw new TranscriptionDetailException("Transcription detail not found for job: " + aiNoteWebhookDto.getJobName());
        }

        TranscriptionDetail transcriptionDetail = transcriptionDetailsOptional.get();
        transcriptionDetail.setStatus(AINOTE.READY);

        Instant instant = Instant.parse(aiNoteWebhookDto.getEventTime());
        Timestamp endTime = Timestamp.from(instant);
        transcriptionDetail.setEndTime(endTime);
        transcriptionDetail.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
        transcriptionDetail.setUpdatedById(transcriptionDetail.getCreatedById());

        String summary = getAISummary(transcriptionDetail);
        Long noteId = createPatientNote(transcriptionDetail, summary);

        transcriptionDetail.setNoteId(noteId);
        transcriptionDetail.setReviewed(false);
        transcriptionDetail.setSubject(DRAFT_SUBJECT);

        transcriptionDetailRepository.save(transcriptionDetail);
        notificationService.createAINoteNotification(transcriptionDetail);

        return true;
    }

    /**
     *
     *
     * @param transcriptionDetail transcriptionDetail
     * @param summary note
     * @return note id
     *
     */
    private Long createPatientNote(TranscriptionDetail transcriptionDetail, String summary) {
        ReviewedNoteRequest request = populateDraftRequest(transcriptionDetail, summary);
        Note savedNote = saveNote(request, transcriptionDetail.getId(), null, transcriptionDetail.getCreatedById());
        return savedNote.getId();
    }

    private String getAISummary(TranscriptionDetail transcriptionDetail) throws TranscriptionDetailException {
        String summary;
        try
        {
            String summaryJson = populateAiNote(transcriptionDetail, "/summary.json");
            // Order the json
            String filterOrderSummary = transcriptionUtil.filterAndOrderSectionsInRawJson(summaryJson);
            // This gets saved to the notes table
            ClinicalDocumentationContainer container = transcriptionUtil.fromJson(filterOrderSummary, ClinicalDocumentationContainer.class);
            
            if (container == null || container.getClinicalDocumentation() == null) {
                log.error("Transcription: Exception while pulling AI summary from S3 for detail id {} - null container or clinical documentation", transcriptionDetail.getId());
                throw new TranscriptionDetailException("Error parsing summary json - null container or clinical documentation");
            }
            
            summary = getClinicalNotesString(container.getClinicalDocumentation());

        } catch (IOException e) {
            log.error("Transcription: Exception while pulling AI summary from S3 for detail id {} parsing error", transcriptionDetail.getId());
            throw new TranscriptionDetailException("Error parsing summary json!!!");
        }

        return summary;
    }

    public void saveTranscriptionChanges(TranscriptionDetail transcriptionDetail) {
        transcriptionDetailRepository.save(transcriptionDetail);
    }


    /**
     * Pull AI note transcript or note from s3 only when in ready or archived state
     *
     * @param transcriptionDetail TranscriptionDetail
     * @param fileName summary.json or transcript.json
     * @return transcript or note string
     * @throws TranscriptionDetailException TranscriptionDetailException
     */
    public String populateAiNote(TranscriptionDetail transcriptionDetail, String fileName) throws TranscriptionDetailException {
        AWSS3Service awss3Service = awsUtil.getUsEast1Client();
        Optional<byte[]> transcriptBytes = awss3Service.getFile(transcriptionDetail.getOutputBucketName(), transcriptionDetail.getJobName() + fileName);
        String transcriptJson;

        if (transcriptBytes.isPresent()) {
            transcriptJson = new String(transcriptBytes.get(), StandardCharsets.UTF_8);
        }
        else
        {
            log.error("Transcription: Exception while pulling {} from S3 for detail Id {}", fileName, transcriptionDetail.getId());
            throw new TranscriptionDetailException("Unable to pull transcript from S3 bucket!!!");
        }
        return  transcriptJson;
    }


    /**
     * Create a draft request to create a patient chart note
     *
     * @param detail TranscriptionDetail
     * @param transcribedNote note string
     * @return ReviewedNoteRequest
     */
    private ReviewedNoteRequest populateDraftRequest(TranscriptionDetail detail, String transcribedNote) {

        ReviewedNoteRequest request = new ReviewedNoteRequest();

        request.setPrescriptionId(detail.getPrescriptionId());
        request.setAppointmentId(detail.getAppointmentId());
        request.setAction(AINOTE.DRAFT);
        request.setSubject(DRAFT_SUBJECT);
        request.setPatientId(detail.getPatientId());
        request.setTreatingPractitionerId(detail.getPractitionerId());
        request.setReviewedNoteString(transcribedNote);

        return request;
    }


    /**
     *  This method returns the transcription detail
     *
     * @param transcriptionDetailsId transcriptionDetailsId
     * @return TranscriptionDetail
     */
    public TranscriptionDetail findByTranscriptionDetailId(Long transcriptionDetailsId) {
        Optional<TranscriptionDetail> transcriptionDetailOpt = transcriptionDetailRepository.findById(transcriptionDetailsId);

        transcriptionDetailOpt.ifPresent(this::loadForeignKeys);
        return transcriptionDetailOpt.orElse(null);
    }

    /**
     * This is called to view the notes on the AI Notes edit screen
     * If called on initial view (isReviewed false) pull from AI notes table
     * When status is draft/signed/published pull from Notes table
     *
     *
     * @param transcriptionDetailsId identifier
     * @return TranscriptionAppointmentNotesDto
     */
    public Optional<TranscriptionAppointmentNotesDto> viewGeneratedNotes(Long transcriptionDetailsId) throws TranscriptionDetailException {

        TranscriptionDetail transcriptionDetail = findByTranscriptionDetailId(transcriptionDetailsId);

        if (null == transcriptionDetail) {
            throw new TranscriptionDetailException("Transcription detail not found!!!");
        }

        ConversationContainer conversation;
        ClinicalDocumentationContainer clinicalDocumentation;
        TranscriptionAppointmentNotesDto transcriptionAppointmentNotesDto;
        try
        {
            conversation = transcriptionUtil.fromJson(populateAiNote(transcriptionDetail, "/transcript.json"), ConversationContainer.class);
            clinicalDocumentation = transcriptionUtil.fromJson(populateAiNote(transcriptionDetail, "/summary.json"), ClinicalDocumentationContainer.class);

            transcriptionAppointmentNotesDto = decideNotesToDisplay(clinicalDocumentation.getClinicalDocumentation(), transcriptionDetail);

            transcriptionAppointmentNotesDto.setReviewed(transcriptionDetail.isReviewed());
            transcriptionAppointmentNotesDto.setTranscriptionDetailsId(transcriptionDetailsId);
            transcriptionAppointmentNotesDto.setConversation(conversation.getConversation());
            // Turning this off until needed
            //transcriptionAppointmentNotesDto.setClinicalDocumentation(clinicalDocumentation.getClinicalDocumentation());
            transcriptionAppointmentNotesDto.setSubject(transcriptionDetail.getSubject());
            // The UI does not need this its uses the conversation segments
            //transcriptionAppointmentNotesDto.setTranscriptString(getTranscriptsString(conversation.getConversation()));

        } catch (JsonProcessingException e) {
            log.error("Transcription: Exception while retrieving notes for review {}", transcriptionDetailsId, e);
            throw new TranscriptionDetailException("Error processing transcription notes: " + e.getMessage());
        }
        return Optional.of(transcriptionAppointmentNotesDto);
    }

    /***
     * Displays notes from the AI results for status ready, and publish
     * Displays from notes table for status draft and sign
     *
     * @param clinicalDocumentation clinicalDocumentation
     */
    private TranscriptionAppointmentNotesDto decideNotesToDisplay(ClinicalDocumentation clinicalDocumentation, TranscriptionDetail transcriptionDetail) {
        TranscriptionAppointmentNotesDto transcriptionAppointmentNotesDto = new TranscriptionAppointmentNotesDto();
        AINOTE status = transcriptionDetail.getStatus();
        transcriptionAppointmentNotesDto.setNoteId(transcriptionDetail.getNoteId());

        if (transcriptionDetail.isReviewed()) {
            Note note = transcriptionDetail.getNote();
            assert note != null;
            transcriptionAppointmentNotesDto.setReviewNoteString(note.getNote());
            transcriptionAppointmentNotesDto.setSubject(note.getSubject());
            transcriptionAppointmentNotesDto.setNoteId(note.getId());
        }
        else
        {
            // Condition in which AI note has never been changed, READY Status
            transcriptionAppointmentNotesDto.setSubject(transcriptionDetail.getSubject());
            transcriptionAppointmentNotesDto.setReviewNoteString(getClinicalNotesString(clinicalDocumentation));

        }
        transcriptionAppointmentNotesDto.setStatus(status);
        if (transcriptionDetail.isArchived()) {
            transcriptionAppointmentNotesDto.setStatus(AINOTE.ARCHIVED);
        }

        transcriptionAppointmentNotesDto.setArchived(transcriptionDetail.isArchived());

        return transcriptionAppointmentNotesDto;
    }

    /**
     *
     * @param request Archive true or false
     * @return success
     * @throws TranscriptionDetailException on error
     * Only treating practitioner or supervising practitioner can archive
     * Only ready and draft can be archived
     * <p>
     * Archiving deletes existing PC note and resets AI note reviewed to false
     * Un-archiving create a new PC note, and resets the AI Note status to READY
     *
     */
    public String archiveTranscription(ArchiveRequest request) throws TranscriptionDetailException {
        TranscriptionDetail transcriptionDetail = findByTranscriptionDetailId(request.getTranscriptionDetailId());

        if (null == transcriptionDetail) {
            log.error("Transcription: Exception while archiving transcription note {}", request.getTranscriptionDetailId());
            throw new TranscriptionDetailException("Transcription Detail not found!!!");
        }


        AINOTE status = transcriptionDetail.getStatus();
        if (!EnumSet.of(AINOTE.READY, AINOTE.DRAFT).contains(status)) {
            log.error("Transcription: Exception while archiving transcription note {} invalid status", request.getTranscriptionDetailId());
            throw new TranscriptionDetailException("Invalid note status!!!");
        }

        User user = userService.getCurrentUser();
        if (!user.getIsSuperAdmin()) {
            if (isNotValidTreatingPractitioner(transcriptionDetail.getPrescriptionId(), transcriptionDetail.getAppointmentId(), user.getId())) {
                log.error("Transcription: Exception while archiving transcription note {} user is not a valid practitioner", request.getTranscriptionDetailId());
                throw new TranscriptionDetailException("Permission error!!!");
            }
        }


        Long userId = user.getId();
        Timestamp now = new Timestamp(System.currentTimeMillis());

        if (request.archiveNote)
        {
            Long noteId = transcriptionDetail.getNoteId();
            if(null != noteId)
                noteService.delete(noteId);
            transcriptionDetail.setNoteId(null);
            transcriptionDetail.setSubject(null);
            transcriptionDetail.setReviewed(false);
            transcriptionDetail.setNote(null);
        }
        else
        {
            String summary = getAISummary(transcriptionDetail);
            Long noteId = createPatientNote(transcriptionDetail, summary);
            transcriptionDetail.setNoteId(noteId);
            transcriptionDetail.setSubject(DRAFT_SUBJECT);

            // Reset ai note to ready status
            transcriptionDetail.setStatus(AINOTE.READY);
        }

        transcriptionDetail.setArchived(request.archiveNote);
        transcriptionDetail.setUpdatedAt(now);
        transcriptionDetail.setUpdatedById(userId);

        transcriptionDetailRepository.save(transcriptionDetail);

        return "success";
    }

    /**
     * This method now acts as an update of the webhook created note for all scenarios
     *
     * @param request ReviewedNoteRequest
     * @return String response success or failure
     */
    public String reviewNotes(ReviewedNoteRequest request) throws TranscriptionDetailException {
        User user = userService.getCurrentUser();

        if (!user.getIsSuperAdmin()) {
            if (request.getAction().equals(AINOTE.SIGNED) && isInValidCareExtenderOrResident(request, user.getId())) {
                throw new TranscriptionDetailException("Sign action not allowed for user!!!");
            }
        }

        if (!user.getIsSuperAdmin()){
            if (request.getAction().equals(AINOTE.PUBLISHED) && isNotValidTreatingPractitioner(request.getPrescriptionId(), request.getAppointmentId(), user.getId())) {
                throw new TranscriptionDetailException("Publish action not allowed for user!!!");
            }
        }

        TranscriptionDetail transcriptionDetail = findByTranscriptionDetailId(request.getTranscriptionDetailId());

        if (null == transcriptionDetail) {
            log.error("Transcription: Exception while saving notes transcription detail search {}", request.getTranscriptionDetailId());
            throw new TranscriptionDetailException("Transcription detail not found!!!");
        }

        if (transcriptionDetail.isArchived())
        {
            log.error("Transcription: Exception while saving notes transcription cannot edit archived notes {}", request.getTranscriptionDetailId());
            throw new TranscriptionDetailException("Cannot edit an archived note!!!");
        }

        AINOTE currentStatus = transcriptionDetail.getStatus();
        if (!user.getIsSuperAdmin()) {
            if (AINOTE.SIGNED.equals(currentStatus) && request.getAction().equals(AINOTE.DRAFT) && isNotValidTreatingPractitioner(request.getPrescriptionId(), request.getAppointmentId(), user.getId())) {
                log.error("Transcription: Exception user cannot save signed note as draft {}", request.getTranscriptionDetailId());
                throw new TranscriptionDetailException("Sign to Draft action not allowed for user!!!");
            }
        }

        if (AINOTE.PUBLISHED.equals(currentStatus))
        {
            log.error("Transcription: Exception while saving notes transcription published notes cannot be edited {}", request.getTranscriptionDetailId());
            throw new TranscriptionDetailException("Cannot edit a published note!!!");
        }

        try
        {
            Note currentNote = transcriptionDetail.getNote();
            saveNote(request, transcriptionDetail.getId(), currentNote, user.getId());
            transcriptionDetail.setSubject(request.getSubject());
            transcriptionDetail.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
            transcriptionDetail.setUpdatedById(user.getId());
            transcriptionDetail.setStatus(request.getAction());
            transcriptionDetail.setReviewed(true);
            transcriptionDetailRepository.save(transcriptionDetail);
        }
        catch(Exception ex) {
            log.error("Transcription: Exception while saving notes {}", request.getTranscriptionDetailId());
            throw new TranscriptionDetailException("Error saving note!!!");
        }

        return "success";
    }

    /**
     * Check if user can perform publish action on the note
     *
     * @param prescriptionId Prescription Id
     * @param appointmentId Appointment Id
     * @param userId current user
     * @return boolean
     */
     boolean isNotValidTreatingPractitioner(Long prescriptionId, Long appointmentId, Long userId) throws TranscriptionDetailException {
        Long defaultTreatingPractitionerId = null;

        Prescription prescription = prescriptionId != null ? prescriptionService.findOne(prescriptionId) : null;
        Appointment appointment = appointmentId != null ? appointmentService.findOne(appointmentId) : null;

        if (appointment != null && appointment.getUserFourId() != null) {
            defaultTreatingPractitionerId = appointment.getUserFourId();
        } else if (appointment != null && appointment.getUserId() != null) {
            defaultTreatingPractitionerId = appointment.getUserId();
        } else if (prescription != null && prescription.getTreatingPractitionerId() != null) {
            defaultTreatingPractitionerId = prescription.getTreatingPractitionerId();
        }

        if (defaultTreatingPractitionerId == null) {
            throw new TranscriptionDetailException("Error saving note, cannot validate treating practitioner!!!");
        }
        return !defaultTreatingPractitionerId.equals(userId);
    }

    /**
     * Check if user can perform sign action on the note
     *
     * @param request ReviewedNoteRequest
     * @return boolean
     * @throws TranscriptionDetailException on error
     */
    boolean isInValidCareExtenderOrResident(ReviewedNoteRequest request, Long userId) throws TranscriptionDetailException {
        Note note = null;
        Prescription prescription = null;
        Appointment appointment = null;

        if (request.getNoteId() != null) {
            note = noteService.findOne(request.getNoteId());
        }
        else
        {
            prescription = prescriptionService.findOne(request.getPrescriptionId());
            appointment = appointmentService.findOne(request.getAppointmentId());
        }

        Long appointmentSupervisingPractitioner = null;
        Long appointmentAttendingId = null;
        Long prescriptionTreatingPractitioner = null;
        Long prescriptionResidentId = null;

        if (note != null) {
            if (note.getAppointment() != null) {
                appointmentSupervisingPractitioner = note.getAppointment().getUserFourId();
                appointmentAttendingId = note.getAppointment().getUserId();
            }
            if (note.getPrescription() != null) {
                prescriptionTreatingPractitioner = note.getPrescription().getTreatingPractitionerId();
                prescriptionResidentId = note.getPrescription().getResidentId();
            }
        } else if (prescription != null) {
            if (appointment != null) {
                appointmentSupervisingPractitioner = appointment.getUserFourId();
                appointmentAttendingId = appointment.getUserId();
            }
            prescriptionTreatingPractitioner = prescription.getTreatingPractitionerId();
            prescriptionResidentId = prescription.getResidentId();
        } else {
            throw new TranscriptionDetailException("Error saving note - error determining Treating/Supervising Practitioner-");
        }

        if (appointmentSupervisingPractitioner != null) {
            return appointmentSupervisingPractitioner.equals(userId) || appointmentAttendingId == null || !appointmentAttendingId.equals(userId);
        } else if (appointmentAttendingId != null && appointmentAttendingId.equals(userId)) {
            return true; // can save and publish (old appointment or an actual practitioner)
        } else return prescriptionTreatingPractitioner == null || prescriptionTreatingPractitioner.equals(userId)
                || prescriptionResidentId == null || !prescriptionResidentId.equals(userId);
    }


    /**
     * Save AI Note to notes table
     *
     * @param request ReviewedNoteRequest
     * @param transcriptionDetailId transcriptionDetailId
     * @return Note
     */
    Note saveNote(ReviewedNoteRequest request, Long transcriptionDetailId, Note savedNote, Long currentUserId) {
        Note note = savedNote;
        if (null == savedNote) {
            note = new Note();
            note.setNoteType("clinical");
            note.setCreatedAt(Timestamp.from(Instant.now()));
            note.setCreatedById(currentUserId);
            note.setPrescriptionId(request.getPrescriptionId());
            note.setAppointmentId(request.getAppointmentId());
            note.setPatientId(request.getPatientId());
            note.setTreatingPractitionerId(request.getTreatingPractitionerId());
            note.setTranscriptionDetailId(transcriptionDetailId);
        }

         // When modifications come from the AI Notes UI we do not need to reconcile changes to the notes
        note.setNote(request.getReviewedNoteString());
        note.setUpdatedAt(Timestamp.from(Instant.now()));
        note.setSubject(request.getSubject());
        note.setUserId(currentUserId);

        String action = request.getAction().name().toLowerCase();

        if (AINOTE.PUBLISHED.equals(request.getAction())) {
            note.setPublished(true);
            action = "publish";
        }
        else if(AINOTE.SIGNED.equals(request.getAction())) {
            action = "sign";
        }

        Map<String, Object> noteMap = noteService.saveNote(note, action);
        return  (Note) noteMap.get(OptimisticLockingUtil.SAVED);
    }



    /**
     * @param clinicalDocumentation clinical documents
     * @return String of output document
     */
    public String getClinicalNotesString(ClinicalDocumentation clinicalDocumentation) {
        if (clinicalDocumentation == null) {
            return "";
        }
        
        StringBuilder stringBuilder = new StringBuilder();
        List<Section> sections = clinicalDocumentation.getSections();

        // Define the desired order of sections
        List<String> orderedSectionNames = List.of(
                "CHIEF_COMPLAINT",
                "HISTORY_OF_PRESENT_ILLNESS",
                "PAST_MEDICAL_HISTORY",
                "REVIEW_OF_SYSTEMS",
                "PHYSICAL_EXAMINATION",
                "PLAN"
        );

        // Build a map for fast lookup
        Map<String, Section> sectionMap = sections.stream()
                .collect(Collectors.toMap(Section::getSectionName, s -> s, (a, b) -> a));

        // Append in the required order
        for (String sectionName : orderedSectionNames) {
            Section section = sectionMap.get(sectionName);
            if (section != null) {
                stringBuilder.append(section.printSectionDetails());
                stringBuilder.append("<br />");
            }
        }

        return stringBuilder.toString();
    }

    /**
     *
     * @param conversation conversation
     * @return Transcript string
     */
    public String getTranscriptsString(Conversation conversation) {
        if (conversation == null) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        List<TranscriptSegment> segments = conversation.getTranscriptSegments();

        for (TranscriptSegment segment : segments) {
            stringBuilder.append(segment.printTranscriptSegment());
        }
        return stringBuilder.toString();
    }

    @Override
    public void loadForeignKeys(TranscriptionDetail transcriptionDetail) {

        if (null != transcriptionDetail) {
            Long noteId = transcriptionDetail.getNoteId();
            if (null != noteId) {
                Note note = noteService.findOne(noteId);
                transcriptionDetail.setNote(note);
            }
        }
    }
}
