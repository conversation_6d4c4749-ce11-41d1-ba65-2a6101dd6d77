package com.nymbl.ai.notes.aspect;

import com.nymbl.ai.notes.dto.AINOTE;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.ai.notes.service.TranscriptionDetailService;
import com.nymbl.config.dto.NoteDTO;
import com.nymbl.master.model.User;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.Note;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.Instant;


/**
 *
 * This aspect keeps the AI Note status aligned with changes made to the Note on the Patient Chart
 *
 */
@Aspect
@Component
@Slf4j
public class NoteSaveAspect {

    private final TranscriptionDetailService transcriptionDetailService;
    private final UserService userService;

    public NoteSaveAspect(TranscriptionDetailService transcriptionDetailService, UserService userService) {
        this.transcriptionDetailService = transcriptionDetailService;
        this.userService = userService;
    }

    @AfterReturning(
            pointcut = "execution(public org.springframework.http.ResponseEntity com.nymbl.tenant.controller.NoteController.saveDTO(..))",
            returning = "response"
    )
    public void afterControllerSave(JoinPoint joinPoint, ResponseEntity<?> response) {
        try
        {
            if (response == null || response.getBody() == null) return;
            Object body = response.getBody();

            if (!(body instanceof Note note)) return;

            if (note.getTranscriptionDetailId() == null) return;

            TranscriptionDetail tapNote = transcriptionDetailService
                    .findByTranscriptionDetailId(note.getTranscriptionDetailId());

            if (tapNote == null) return;

            NoteDTO noteDto = null;

            for (Object arg : joinPoint.getArgs()) {
                if (arg instanceof NoteDTO) {
                    noteDto = (NoteDTO) arg;
                }
            }

            if (noteDto == null || noteDto.getAction() == null) {
                log.warn("NoteDTO not found in arguments");
                return;
            }

            boolean modified = updateModifiedOutsideAiNotesUI(tapNote, note, noteDto.getAction());
            if (modified) {
                transcriptionDetailService.saveTranscriptionChanges(tapNote);
            }
        } catch (Exception ex) {
            // log and swallow so controller isn’t affected
            log.error("Exception while updating ai note", ex);
        }
    }


    /***
     * This method checks if any changes have been made to the notes on the v1 screen
     *
     * @param transcriptionDetail transcriptionDetail
     * @return boolean
     */
    private boolean updateModifiedOutsideAiNotesUI(TranscriptionDetail transcriptionDetail, Note note, String action) {
        boolean isModified = false;
        User user = userService.getCurrentUser();

        AINOTE currentStatus = transcriptionDetail.getStatus();
        if (AINOTE.PUBLISHED.equals(currentStatus)) {
            // No changes will be made on the AI Notes side after the note has been published
            return false;
        } else if (action.equals("publish")) {
            // if it gets here it means the AI note is not in publish status, and publish was done in patient chart view
            transcriptionDetail.setStatus(AINOTE.PUBLISHED);
            isModified = true;
        }
        else if (action.equals("sign")) {
            // Track any sign action
            transcriptionDetail.setStatus(AINOTE.SIGNED);
            isModified = true;
        }
        else if (action.equals("unsign") || action.equals("draft") || action.equals("save")){
            // This tracks unsigned action and draft actions
            transcriptionDetail.setStatus(AINOTE.DRAFT);
            isModified = true;
        }

        transcriptionDetail.setSubject(note.getSubject());
        transcriptionDetail.setUpdatedAt(Timestamp.from(Instant.now()));
        transcriptionDetail.setUpdatedById(user.getId());
        transcriptionDetail.setReviewed(true);

        // Track draft to draft changes
        return isModified;
    }

}
