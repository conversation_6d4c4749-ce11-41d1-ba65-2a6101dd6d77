package com.nymbl.ai.notes.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.nymbl.ai.notes.dto.AINOTE;
import com.nymbl.ai.notes.dto.TranscriptionDetailsDto;
import com.nymbl.ai.notes.dto.TranscriptionUploadRequest;
import com.nymbl.ai.notes.model.TranscriptionDetail;
import com.nymbl.master.model.UserDto;
import com.nymbl.master.repository.UserDtoRepository;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.dashboard.dto.AppointmentDto;
import com.nymbl.tenant.dashboard.dto.PatientDto;
import com.nymbl.tenant.dashboard.repository.AppointmentDtoRepository;
import com.nymbl.tenant.model.Branch;
import com.nymbl.tenant.model.Patient;
import com.nymbl.tenant.model.Prescription;
import com.nymbl.tenant.repository.BranchRepository;
import com.nymbl.tenant.repository.PatientRepository;
import com.nymbl.tenant.service.PrescriptionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.sax.BodyContentHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class TranscriptionUtil {

    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    private final BranchRepository branchRepository;
    private final PatientRepository patientRepository;
    private final AppointmentDtoRepository appointmentRepository;
    private final PrescriptionService prescriptionService;

    private final UserDtoRepository userRepository;

    public TranscriptionUtil(BranchRepository branchRepository, PatientRepository patientRepository, AppointmentDtoRepository appointmentRepository, PrescriptionService prescriptionService, UserDtoRepository userRepository) {
        this.branchRepository = branchRepository;
        this.patientRepository = patientRepository;
        this.appointmentRepository = appointmentRepository;
        this.prescriptionService = prescriptionService;
        this.userRepository = userRepository;
    }

    /**
     * Replaces placeholders in a path string with actual values.
     *
     * @param pathTemplate The path string containing placeholders.
     * @param values Map containing placeholder names and their corresponding values.
     * @return The path string with placeholders replaced by actual values.
     */
    public String replacePathVariables(String pathTemplate, Map<String, String> values) {
        Pattern pattern = Pattern.compile("\\{([^{}]+)\\}"); // Regex to match {placeholder} patterns
        Matcher matcher = pattern.matcher(pathTemplate);

        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String variableName = matcher.group(1);
            String replacement = values.getOrDefault(variableName, "");
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);

        return result.toString();
    }

    public Map<String, String> convertToMap(TranscriptionUploadRequest request) {
        Map<String, String> map = new HashMap<>();
        Optional<Branch> branch = branchRepository.findById(request.getBranchId());
        map.put("branch", branch.get().getName());
        map.put("patientId", String.valueOf(request.getPatientId()));
        map.put("practitionerId", String.valueOf(request.getPractitionerId()));
        map.put("tenant", TenantContext.getCurrentTenant());
        return map;
    }

    public <T> T fromJson(String json, Class<T> clazz) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(json, clazz);
    }

    public String toJson(Object object) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(object);
    }

    public String makeJobName(TranscriptionDetail transcriptionDetail) {
        if (!"prod".equalsIgnoreCase(activeProfile)) {
            return "TS" + TenantContext.getCurrentTenant() + "-" + activeProfile + "-" + transcriptionDetail.getId();
        }
        return "TS" + TenantContext.getCurrentTenant() + "-" + transcriptionDetail.getId();
    }

    /**
     * This method gets the transcription detail id from the webhook event
     *
     *
     * @param jobName transcription job name
     * @return transcriptionDetailId
     */
    public Long getTranscriptionDetailsId(String jobName) {
        String[] parts = jobName.split("-");

        if (!"prod".equalsIgnoreCase(activeProfile)) {
            return Long.valueOf(parts[2]);
        }
        return Long.valueOf(parts[1]);
    }

    /**
     * Formats the audio file duration into a string
     *
     * @param seconds audio duration
     * @return duration as time formatted string
     */
    public String formatTime(double seconds) {

        long wholeSeconds = (long) seconds; // Convert to long for easier calculations
        long hours = TimeUnit.SECONDS.toHours(wholeSeconds);
        long minutes = TimeUnit.SECONDS.toMinutes(wholeSeconds) - TimeUnit.HOURS.toMinutes(hours);
        long secs = wholeSeconds - TimeUnit.HOURS.toSeconds(hours) - TimeUnit.MINUTES.toSeconds(minutes);


        return String.format("%02d:%02d:%02d", hours, minutes, secs);
    }

    /**
     * Converts TranscriptionDetail to TranscriptionDetailsDto.
     * Mapping common fields needed for the client, and additional DTOs;
     *  <ul>
     *      <li>Patient</li>
     *      <li>Appointment</li>
     *      <li>Practitioner</li>
     *  </ul>
     * @param details transcription detail
     * @return transcriptionDetailsDto
     */
    public TranscriptionDetailsDto convertToDto(TranscriptionDetail details) {
        // Map common fields between the entity and DTO
        TranscriptionDetailsDto transcriptionDetailsDto = new TranscriptionDetailsDto();
        transcriptionDetailsDto.setId(details.getId());
        transcriptionDetailsDto.setStatus(details.getStatus());

        if (details.isArchived())
            transcriptionDetailsDto.setStatus(AINOTE.ARCHIVED);

        transcriptionDetailsDto.setJobName(details.getJobName());
        transcriptionDetailsDto.setBranchId(details.getBranchId());
        transcriptionDetailsDto.setPrescriptionId(details.getPrescriptionId());
        transcriptionDetailsDto.setStartTime(details.getStartTime());
        transcriptionDetailsDto.setEndTime(details.getEndTime());
        transcriptionDetailsDto.setUpdatedAt(details.getUpdatedAt());
        transcriptionDetailsDto.setAudioTime(details.getAudioTime());
        transcriptionDetailsDto.setArchived(details.isArchived());

        Prescription prescription = prescriptionService.findOne(details.getPrescriptionId());

        if (null != prescription)
            transcriptionDetailsDto.setResidentId(prescription.getResidentId());

        // Map patient DTO with id, and name fields.
        Optional<Patient> patient = patientRepository.findById(details.getPatientId());
        if(patient.isPresent()){
            Patient p = patient.get();
            transcriptionDetailsDto.setPatient(new PatientDto(p.getId(), p.getFirstName(), p.getLastName(), ""));
        } else {
            transcriptionDetailsDto.setPatient(null);
        }

        // Map appointment dto with offset date time date fields.
        AppointmentDto appointment = appointmentRepository.getAppointmentDtoById(details.getAppointmentId());
        transcriptionDetailsDto.setAppointment(appointment);

        // Map the UserDto that does not include password salts and other sensitive information.
        // TODO validate which treating practitioner
        UserDto practitioner = userRepository.findUserDtoById(details.getPractitionerId());
        transcriptionDetailsDto.setPractitioner(practitioner);


        return transcriptionDetailsDto;
    }

    /**
     * Gets the file type from mime type or extension
     *
     * @param file audio file
     * @return extension
     */
    public  String getFileExtension(MultipartFile file) {
        String extension = getFileExtensionFromName(file);
        if (extension.isEmpty()) {
            extension = getExtensionFromMimeType(file);
        }
        return extension;
    }

    // Get extension from file name
    private  String getFileExtensionFromName(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename != null && originalFilename.contains(".")) {
            return "." + originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        }
        return "";
    }

    // Get extension from MIME type
    private String getExtensionFromMimeType(MultipartFile file) {
        String contentType = file.getContentType();
        if (contentType != null) {
            return switch (contentType) {
                case "audio/mp4" -> ".m4a";
                case "audio/wav" -> ".wav";
                default -> ".mp3";
            };
        }
        return "";
    }

    /**
     * Calculates audio lengths for uploaded files
     *
     * @param file uploaded file
     * @return duration
     */
    public double calculateAudioLength(MultipartFile file) {
        Metadata metadata = new Metadata();
        BodyContentHandler handler = new BodyContentHandler();

        try (InputStream stream = file.getInputStream()) {
            AutoDetectParser parser = new AutoDetectParser();
            parser.parse(stream, handler, metadata, new ParseContext());

            String duration = metadata.get("xmpDM:duration");
            if (duration != null) {
                return Double.parseDouble(duration); // Convert to seconds
            }
        } catch (Exception e) {
            log.error("AWSConfig: Exception while getting audio length", e);
        }
        return 0.0;
    }


    public String filterAndOrderSectionsInRawJson(String rawJson) throws IOException {
        List<String> sectionOrder = List.of(
                "CHIEF_COMPLAINT",
                "HISTORY_OF_PRESENT_ILLNESS",
                "PAST_MEDICAL_HISTORY",
                "REVIEW_OF_SYSTEMS",
                "PHYSICAL_EXAMINATION",
                "PLAN"
        );

        Set<String> allowedSections = new HashSet<>(sectionOrder);
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode root = objectMapper.readTree(rawJson);
        JsonNode clinicalDocNode = root.path("ClinicalDocumentation");

        if (clinicalDocNode.isMissingNode() || !clinicalDocNode.has("sections")) {
            return rawJson;
        }

        ArrayNode originalSections = (ArrayNode) clinicalDocNode.get("sections");

        // Group sections by name for quick access
        Map<String, JsonNode> sectionMap = new LinkedHashMap<>();
        for (JsonNode section : originalSections) {
            String name = section.path("sectionName").asText();
            if (allowedSections.contains(name) && !sectionMap.containsKey(name)) {
                sectionMap.put(name, section);
            }
        }

        // Reconstruct in desired order
        ArrayNode reorderedSections = objectMapper.createArrayNode();
        for (String sectionName : sectionOrder) {
            JsonNode sectionNode = sectionMap.get(sectionName);
            if (sectionNode != null) {
                reorderedSections.add(sectionNode);
            }
        }

        ((ObjectNode) clinicalDocNode).set("sections", reorderedSections);
        return objectMapper.writeValueAsString(root);
    }

}
