package com.nymbl.ai.notes.data.clinicaldoc;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class Section {
    @JsonProperty("SectionName")
    private String sectionName;
    @JsonProperty("Summary")
    private List<Summary> summary;

    public String printSectionDetails() {
        StringBuilder sb = new StringBuilder();
        String sectionHeader = sectionName.replace("_", " ");
        sb.append("<h3>").append(sectionHeader).append("</h3>");
        sb.append("<br />");
        for(Summary s : summary) { // Iterate through each Summary object in the list
            sb.append("<p>").append(s.getSummarizedSegment()).append("</p>"); // Print the SummarizedSegment
            sb.append("<br />");
        }
        return sb.toString();
    }
}

